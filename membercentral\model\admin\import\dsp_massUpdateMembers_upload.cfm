<cfsavecontent variable="local.massUpdateJS">
	<cfoutput>
		<script language="javascript">
		function showUploadTypeScreen() {
			$('.memUpdTypeScreen').hide();
			$('input[name="uploadType"]').prop('checked',false);
			$('.chooseMemUpdType').show(300);
		}

		function showUploadScreen(){
			var uploadType = $('input[name="uploadType"]:checked').val();
			$('.chooseMemUpdType').hide();
			$('##'+uploadType).show(300);
		}
		function showLoadingBtn(mode) {
			if(mode == 'partial') {
				$('##divFormPartialUploadStandard').hide(300);
				$('##divFormPartialUploadStandardLoading').show();
			} else if(mode == 'complete') {
				$('##divFormCompleteUploadStandard').hide(300);
				$('##divFormCompleteUploadStandardLoading').show();
			} else if(mode == 'changeMemNumber') {
				$('##divFormChangeMemNumberUploadStandard').hide(300);
				$('##divFormChangeMemNumberUploadStandardLoading').show();
			} else if(mode == 'deleteMembers') {
				$('##divFormDeleteMemberUpload').hide(300);
				$('##divFormDeleteMemberUploadLoading').show();
			}
			return true;
		}
		function showCustomLoadingBtn() {
			$('##divFormCustomUpload').hide(300);
			$('##divFormCustomUploadLoading').show();
			return true;
		}
		<cfif arguments.event.valueExists('err')>
			$(function() {
				var errmsg = '';
				<cfswitch expression="#arguments.event.getValue('err')#">
					<cfcase value="TemplateQRYLARGE">
						errmsg = 'There are too many possible columns for your data to produce a template import file. Contact MemberCentral if you need assistance importing your changes.';
						showMemUpdateFile();
					</cfcase>
					<cfcase value="TemplateUNK">
						errmsg = 'The sample import file could not be generated. Contact MemberCentral for assistance.';
						showMemUpdateFile();
					</cfcase>
				</cfswitch>
				if (errmsg.length > 0) showAlert(errmsg);
			});
		</cfif>
		function enableUpload() {			
			if($.trim($("##confirm").val().toUpperCase()) == 'DELETE'){
				$("##btnDeleteMembers").prop('disabled',false);
			}
			else{
				$("##btnDeleteMembers").prop('disabled',true);
			}			
		}
			$(function() {
				mca_setupCustomFileControls('frmPartialUpload');
				mca_setupCustomFileControls('frmCompleteUpload');
				mca_setupCustomFileControls('frmChangeMemberNumber');
				mca_setupCustomFileControls('frmDeleteMembers');
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.massUpdateJS)#">

<cfoutput>
<div id="err_massupdmem_uploads" class="alert alert-danger d-none mb-2"></div>
<div class="row">
	<div class="col">
		<cfif structKeyExists(local,'importResults')>
			<div id="uploadResultScreen">
				#local.importResults#
			</div>
		<cfelse>	
			<div id="uploadTypeScreen" class="chooseMemUpdType">
				<h5>Type of Upload</h5>
				<div class="form-check mb-3 ml-2">
					<input class="form-check-input" type="radio" name="uploadType" id="uploadTypePartial" value="partialUploadType" onclick="showUploadScreen();">
					<label class="form-check-label" for="uploadTypePartial">
						Upload a <b>partial list of members</b> to add or update
					</label>
					<br/>
					<i>This option is used to update fields for a specific group of members and/or add new members to your database.</i>
				</div>
				<div class="form-check mb-3 ml-2">
					<input class="form-check-input" type="radio" name="uploadType" id="uploadTypeFull" value="completeUploadType" onclick="showUploadScreen();">
					<label class="form-check-label" for="uploadTypeFull">
						Upload a <b>complete list of active members</b> to sync
					</label>	
					<br/>
					<i>This syncs your third-party membership database with your MemberCentral or SeminarWeb website.</i>			
				</div>
				<div class="form-check mb-3 ml-2">
					<input class="form-check-input" type="radio" name="uploadType" id="uploadTypeChangeMemNum" value="changeMemberNumber" onclick="showUploadScreen();">
					<label class="form-check-label" for="uploadTypeChangeMemNum">
						Upload a <b>list of MemberNumbers to change</b>
					</label>
					<br/>
					<i>This option is used to update existing MemberNumbers for a specific group of members.</i>				
				</div>
				<div class="form-check mb-3 ml-2">
					<input class="form-check-input" type="radio" name="uploadType" id="uploadTypeDeleteMembers" value="deleteMembers" onclick="showUploadScreen();">
					<label class="form-check-label" for="uploadTypeDeleteMembers">
						Upload a <b>list of members to delete</b>
					</label>
					<br/>
					<i>This option is used to delete members from your database.</i>				
				</div>
				<cfif len(local.customData)>
					<div class="form-check mb-3 ml-2">
						<input class="form-check-input" type="radio" name="uploadType" id="uploadTypeCustom" value="customUploadType" onclick="showUploadScreen();">
						<label class="form-check-label" for="uploadTypeCustom">
							<b>Custom</b> upload of members</label>
						</label>
						<br/>
						<i>MemberCentral has coded an upload unique to your database that requires specific fields and/or files.</i>
					</div>
				</cfif>
			</div>
			<div id="partialUploadType" class="memUpdTypeScreen" style="display:none;">
				<div class="mb-4">
					<a href="##" onclick="showUploadTypeScreen();">
						<i class="fa-solid fa-chevrons-left"></i> Back to Updates Menu
					</a>
				</div>
				<h5>Upload a partial list of members to add or update.</h5>
				<div id="divFormPartialUploadStandard">
					<div class="mb-4">
						Select your upload file and review the available options for your upload.
					</div>
					<div class="ml-4">
						<cfform name="frmPartialUpload" id="frmPartialUpload" action="#this.link.processImport#" method="post" enctype="multipart/form-data" onsubmit="return showLoadingBtn('partial');">
							<div class="mt-2">
								<div class="form-check">
									<cfinput type="checkbox" class="form-check-input" name="incMemActive" id="incMemActive" value="1"> 
									<label class="form-check-label" for="incMemActive">
										Ensure all members in the uploaded file are set to Active
									</label>
								</div>										
							</div>
							<div class="mt-2">
								<div class="form-check">
									<cfinput type="checkbox" class="form-check-input" name="autogenMemNum" id="autogenMemNum" value="1"> 
									<label class="form-check-label" for="autogenMemNum">
										Ensure those without membernumbers in the uploaded file are added with auto-generated membernumbers
									</label>
								</div>
							</div>
							<div class="mt-2">
								<div class="form-check">
									<cfinput type="checkbox" class="form-check-input" name="ignoreInvalidCols" value="1" checked="checked"> 
									<label class="form-check-label" for="ignoreInvalidCols">
										Ignore invalid columns in the uploaded file
									</label>
								</div>
							</div>
							<br/>
							<div class="custom-file form-control-sm col-sm-5 col-md-5">
								<input type="file" class="custom-file-input" name="importFileName" id="importFileName" required="yes" message="Select the CSV or XLS file from your computer.">
								<label class="custom-file-label" for="importFileName">Select File</label>
							</div>
							<br/><br/>									
							<button type="submit" name="btnPartialUpload" id="btnPartialUpload" class="btn btn-primary btn-sm">Upload</button>
						</cfform>
					</div>
					<div class="mt-4">
						<b>Note for Microsoft Excel users</b>: 
						Ensure your file is saved using a XLS file extension, commonly found in "Excel 97-2003 Workbook" format. Excel files saved with an "XLSX" extension will be rejected.
						Also, due to how Excel stores and displays data internally, you will run into fewer data import issues if you save your Excel sheet as a CSV file and upload the CSV instead of the Excel file.
					</div>
					<div class="mt-4">
						<a href="#this.link.sampleImportTemplate#&isPartial=1">Download Sample Import Template</a>
					</div>
				</div>
				<div id="divFormPartialUploadStandardLoading" style="display:none;" class="text-center">
					<i class="fa-light fa-circle-notch fa-spin fa-2x"></i> <b>Uploading and validating data. Please wait...</b>
				</div>
			</div>
			<div id="completeUploadType" class="memUpdTypeScreen" style="display:none;">
				<div class="mb-4">
					<a href="##" onclick="showUploadTypeScreen();">
						<i class="fa-solid fa-chevrons-left"></i> Back to Updates Menu
					</a>
				</div>
				<h5>Upload a complete list of active members to sync.</h5>
				<div id="divFormCompleteUploadStandard">
					<div class="mb-4">
						Select your upload file.<br/>
						We will ensure all members in your file are set to Active and all Users not in your file are set to Inactive.
					</div>
					<div class="ml-4">
						<cfform name="frmCompleteUpload" id="frmCompleteUpload" action="#this.link.processImport#" method="post" enctype="multipart/form-data" onsubmit="return showLoadingBtn('complete');">
							<div class="mt-2">
								<div class="form-check">
									<cfinput type="checkbox" class="form-check-input" name="ignoreInvalidCols" value="1" checked="checked"> 
									<label class="form-check-label" for="incMemActive">
										Ignore invalid columns in the uploaded file
									</label>
								</div>
							</div>
							<div class="mt-2">
								Stop update if more than <cfinput type="text" name="thresholdLimit" id="thresholdLimit" value="500" size="4" validate="integer" message="Specify a valid number."> members will be inactivated
							</div>
							<br/>
							<cfinput type="hidden" name="incMemActive" id="incMemActive" value="1">
							<cfinput type="hidden" name="nonIncMemInactive" id="nonIncMemInactive" value="1">
							<div class="custom-file form-control-sm col-sm-5 col-md-5">
								<input type="file" class="custom-file-input" name="importFileName" id="importFileName" required="yes" message="Select the CSV or XLS file from your computer.">
								<label class="custom-file-label" for="importFileName">Select File</label>
							</div>
							<br/><br/>									
							<button type="submit" name="btnCompleteUpload" id="btnCompleteUpload" class="btn btn-primary btn-sm">Upload</button>
						</cfform>
					</div>
					<div class="mt-4">
						<b>Note for Microsoft Excel users</b>: 
						Ensure your file is saved using a XLS file extension, commonly found in "Excel 97-2003 Workbook" format. Excel files saved with an "XLSX" extension will be rejected.
						Also, due to how Excel stores and displays data internally, you will run into fewer data import issues if you save your Excel sheet as a CSV file and upload the CSV instead of the Excel file.
					</div>
					<div class="mt-4">
						<a href="#this.link.sampleImportTemplate#">Download Sample Import Template</a>
					</div>
				</div>
				<div id="divFormCompleteUploadStandardLoading" style="display:none;" class="text-center">
					<i class="fa-light fa-circle-notch fa-spin fa-2x"></i> <b>Uploading and validating data. Please wait...</b>
				</div>
			</div>
			<div id="changeMemberNumber" class="memUpdTypeScreen" style="display:none;">
				<div class="mb-4">
					<a href="##" onclick="showUploadTypeScreen();">
						<i class="fa-solid fa-chevrons-left"></i> Back to Updates Menu
					</a>
				</div>
				<h5>Upload a list of MemberNumbers to change.</h5>
				<div id="divFormChangeMemNumberUploadStandard">
					<div class="mb-4">
						Select your upload file.
					</div>
					<div class="ml-4">
						<cfform name="frmChangeMemberNumber" id="frmChangeMemberNumber" action="#this.link.processMassUpdateMemberNumbers#" method="post" enctype="multipart/form-data" onsubmit="return showLoadingBtn('changeMemNumber');">
							<div class="custom-file form-control-sm col-sm-5 col-md-5">
								<input type="file" class="custom-file-input" name="importFileName" id="importFileName" required="yes" message="Select the CSV or XLS file from your computer.">
								<label class="custom-file-label" for="importFileName">Select File</label>
							</div>
							<br/><br/>
							<button type="submit" name="btnChangeMemNumber" id="btnChangeMemNumber" class="btn btn-primary btn-sm">Upload</button>
						</cfform>
					</div>
					<div class="mt-4">
						<b>Note for Microsoft Excel users</b>: 
						Ensure your file is saved using a XLS file extension, commonly found in "Excel 97-2003 Workbook" format. Excel files saved with an "XLSX" extension will be rejected.
						Also, due to how Excel stores and displays data internally, you will run into fewer data import issues if you save your Excel sheet as a CSV file and upload the CSV instead of the Excel file.
					</div>
					<div class="mt-4">
						<a href="#this.link.sampleMassUpdateMemberNumberTemplate#">Download Sample Import Template</a>
					</div>
				</div>
				<div id="divFormChangeMemNumberUploadStandardLoading" style="display:none;" class="text-center">
					<i class="fa-light fa-circle-notch fa-spin fa-2x"></i> <b>Uploading and validating data. Please wait...</b>
				</div>
			</div>
			<div id="deleteMembers" class="memUpdTypeScreen" style="display:none;">
				<div class="mb-4">
					<a href="##" onclick="showUploadTypeScreen();">
						<i class="fa-solid fa-chevrons-left"></i> Back to Updates Menu
					</a>
				</div>
				<h5>Upload a list of members to delete.</h5>
				<div id="divFormDeleteMemberUpload">
					<div class="mb-4">
						Select your upload file. Before deleting the members, we will first inactivate any members that are not already inactive. 
					</div>
					<div class="ml-4">
						<cfform name="frmDeleteMembers" id="frmDeleteMembers" action="#this.link.processMassDeleteMembers#" method="post" enctype="multipart/form-data" onsubmit="return showLoadingBtn('deleteMembers');">
							<div class="custom-file form-control-sm col-sm-5 col-md-5">
								<input type="file" class="custom-file-input" name="importFileName" id="importFileName" required="yes" message="Select the CSV or XLS file from your computer.">
								<label class="custom-file-label" for="importFileName">Select File</label>
							</div>
							<br/><br/>
							<div class="alert alert-warning">
								<b>Confirmation Needed</b><br/>This upload will DELETE the list of members you import. Type DELETE in the box below to proceed:<br/><br/>
								<input type="text" name="confirm" id="confirm" value="" maxlength="10" class="form-control form-control-sm" style="width:10%;display:inline-block;" onKeyUp="enableUpload();">
 								<button type="submit" name="btnDeleteMembers" id="btnDeleteMembers" class="btn btn-primary btn-sm" disabled = "disabled">Upload</button>
								<button type="button" name="btnCancel" class="btn btn-sm btn-secondary" onclick="showUploadTypeScreen();">Cancel</button>			
							</div>
						</cfform>
					</div>
					<div class="mt-4">
						<b>Note for Microsoft Excel users</b>: 
						Ensure your file is saved using a XLS file extension, commonly found in "Excel 97-2003 Workbook" format. Excel files saved with an "XLSX" extension will be rejected.
						Also, due to how Excel stores and displays data internally, you will run into fewer data import issues if you save your Excel sheet as a CSV file and upload the CSV instead of the Excel file.
					</div>
					<div class="mt-4">
						<a href="#this.link.sampleMassDeleteMembersTemplate#">Download Sample Import Template</a>
					</div>
				</div>
				<div id="divFormDeleteMemberUploadLoading" style="display:none;" class="text-center">
					<i class="fa-light fa-circle-notch fa-spin fa-2x"></i> <b>Uploading and validating data. Please wait...</b>
				</div>
			</div>
			<cfif len(local.customData)>
				<div id="customUploadType" class="memUpdTypeScreen" style="display:none;">
					<div class="mb-4">
						<a href="##" onclick="showUploadTypeScreen();">
							<i class="fa-solid fa-chevrons-left"></i> Back to Updates Menu
						</a>
					</div>
					<h5>Custom upload of members</h5>
					<div id="divFormCustomUpload">
						#local.customData#
					</div>
					<div id="divFormCustomUploadLoading" style="display:none;" class="text-center">
						<i class="fa-light fa-circle-notch fa-spin fa-2x"></i> <b>Uploading and validating data. Please wait...</b>
					</div>
				</div>
			</cfif>
		</cfif>
	</div>
</div>

</cfoutput>