
<cfsavecontent variable="local.mcBillingTabsJS">
	<cfoutput>
	<script language="javascript">
		function validateAndSaveAMSHostingInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_AMSHosting_'+sid);
			$('##btn_AMSHosting_'+sid).prop('disabled',true);
			if($('##AMSHostingEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_AMSHosting_'+sid, arrErr.join('<br />'));
				$('##btn_AMSHosting_'+sid).prop('disabled',false);
				return false;
			}
			var SaveAMSHostingInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_AMSHosting_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_AMSHosting_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                       
					
				} else {
					mca_showAlert('err_AMSHosting_'+sid, 'We were unable to save the AMS Hosting Info. Try again.', false);
					$('##btn_AMSHosting_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, AMSHostingEffectiveDateMonth:$('##AMSHostingEffectiveDate_'+sid+'_Month').val(), AMSHostingEffectiveDateYear:$('##AMSHostingEffectiveDate_'+sid+'_Year').val(),
				AMSHostingInContract:$('##AMSHostingInContract_'+sid).is(':checked'), AMSHostingAdminRate:Number(parseFloat($('##AMSHostingAdminRate_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)), AMSHostingAdminRateInc:parseInt($('##AMSHostingAdminRateInc_'+sid).val().replace(/[^0-9.]/g, '')),
				AMSHostingAdminRateAddtl: Number(parseFloat($('##AMSHostingAdminRateAddtl_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)) , AMSHostingMonthlyMin:Number(parseFloat($('##AMSHostingMonthlyMin_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)), AMSHostingMonthlyFeeAMS:Number(parseFloat($('##AMSHostingMonthlyFeeAMS_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)), 
				AMSHostingMonthlyFeeWeb:Number(parseFloat($('##AMSHostingMonthlyFeeWeb_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				AMSHostingNoFee:$('##AMSHostingNoFee_'+sid).is(':checked')
				};
			TS_AJX('TRIALSMITHBILLING','saveAMSHostingInfo',objParams,SaveAMSHostingInfoResult,SaveAMSHostingInfoResult,10000,SaveAMSHostingInfoResult);
		}
		function validateAndSaveFundraisingInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_Fundraising_'+sid);
			$('##btn_Fundraising_'+sid).prop('disabled',true);
			if($('##FundraisingEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_Fundraising_'+sid, arrErr.join('<br />'));
				$('##btn_Fundraising_'+sid).prop('disabled',false);
				return false;
			}
			var SaveFundraisingInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_Fundraising_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_Fundraising_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_Fundraising_'+sid, 'We were unable to save the Fundraising Info. Try again.', false);
					$('##btn_Fundraising_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, FundraisingEffectiveDateMonth:$('##FundraisingEffectiveDate_'+sid+'_Month').val(), FundraisingEffectiveDateYear:$('##FundraisingEffectiveDate_'+sid+'_Year').val(),
				FundraisingInContract:$('##FundraisingInContract_'+sid).is(':checked'), FundraisingMonthlyFee:Number(parseFloat($('##FundraisingMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				FundraisingNoFee:$('##FundraisingNoFee_'+sid).is(':checked')
				};
			TS_AJX('TRIALSMITHBILLING','saveFundraisingInfo',objParams,SaveFundraisingInfoResult,SaveFundraisingInfoResult,10000,SaveFundraisingInfoResult);
		}
		function validateAndSaveLRISInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_LRIS_'+sid);
			$('##btn_LRIS_'+sid).prop('disabled',true);
			if($('##LRISEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_LRIS_'+sid, arrErr.join('<br />'));
				$('##btn_LRIS_'+sid).prop('disabled',false);
				return false;
			}
			var SaveLRISInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_LRIS_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_LRIS_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_LRIS_'+sid, 'We were unable to save the Referrals Info. Try again.', false);
					$('##btn_LRIS_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, LRISEffectiveDateMonth:$('##LRISEffectiveDate_'+sid+'_Month').val(), LRISEffectiveDateYear:$('##LRISEffectiveDate_'+sid+'_Year').val(),
				LRISInContract:$('##LRISInContract_'+sid).is(':checked'), LRISMonthlyFee:Number(parseFloat($('##LRISMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				LRISNoFee:$('##LRISNoFee_'+sid).is(':checked')
				};
			TS_AJX('TRIALSMITHBILLING','saveLRISInfo',objParams,SaveLRISInfoResult,SaveLRISInfoResult,10000,SaveLRISInfoResult);
		}
		function validateAndSavePublicationsInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_Publications_'+sid);
			$('##btn_Publications_'+sid).prop('disabled',true);
			if($('##PublicationsEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_Publications_'+sid, arrErr.join('<br />'));
				$('##btn_Publications_'+sid).prop('disabled',false);
				return false;
			}
			var SavePublicationsInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_Publications_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_Publications_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_Publications_'+sid, 'We were unable to save the Publications Info. Try again.', false);
					$('##btn_Publications_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, PublicationsEffectiveDateMonth:$('##PublicationsEffectiveDate_'+sid+'_Month').val(), PublicationsEffectiveDateYear:$('##PublicationsEffectiveDate_'+sid+'_Year').val(),
				PublicationsInContract:$('##PublicationsInContract_'+sid).is(':checked'), PublicationsMonthlyFee:Number(parseFloat($('##PublicationsMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				PublicationsNoFee:$('##PublicationsNoFee_'+sid).is(':checked')
				};
			TS_AJX('TRIALSMITHBILLING','savePublicationsInfo',objParams,SavePublicationsInfoResult,SavePublicationsInfoResult,10000,SavePublicationsInfoResult);
		}
		function validateAndSaveSolicitationsInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_Solicitations_'+sid);
			$('##btn_Solicitations_'+sid).prop('disabled',true);
			if($('##SolicitationsEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_Solicitations_'+sid, arrErr.join('<br />'));
				$('##btn_Solicitations_'+sid).prop('disabled',false);
				return false;
			}
			var SaveSolicitationsInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_Solicitations_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_Solicitations_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_Solicitations_'+sid, 'We were unable to save the Solicitations Info. Try again.', false);
					$('##btn_Solicitations_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, SolicitationsEffectiveDateMonth:$('##SolicitationsEffectiveDate_'+sid+'_Month').val(), SolicitationsEffectiveDateYear:$('##SolicitationsEffectiveDate_'+sid+'_Year').val(),
				SolicitationsInContract:$('##SolicitationsInContract_'+sid).is(':checked'), SolicitationsMonthlyFee:Number(parseFloat($('##SolicitationsMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				SolicitationsNoFee:$('##SolicitationsNoFee_'+sid).is(':checked')
				};
			TS_AJX('TRIALSMITHBILLING','saveSolicitationsInfo',objParams,SaveSolicitationsInfoResult,SaveSolicitationsInfoResult,10000,SaveSolicitationsInfoResult);
		}
		function validateAndSaveAPIAccessInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_APIAccess_'+sid);
			$('##btn_APIAccess_'+sid).prop('disabled',true);
			if($('##APIAccessEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_APIAccess_'+sid, arrErr.join('<br />'));
				$('##btn_APIAccess_'+sid).prop('disabled',false);
				return false;
			}
			var SaveAPIAccessInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_APIAccess_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_APIAccess_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_APIAccess_'+sid, 'We were unable to save the API Access Info. Try again.', false);
					$('##btn_APIAccess_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, APIAccessEffectiveDateMonth:$('##APIAccessEffectiveDate_'+sid+'_Month').val(), APIAccessEffectiveDateYear:$('##APIAccessEffectiveDate_'+sid+'_Year').val(),
				APIAccessInContract:$('##APIAccessInContract_'+sid).is(':checked'), APIAccessMonthlyFee:Number(parseFloat($('##APIAccessMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				APIAccessNoofCallIncFee:parseInt($('##APIAccessNoofCallIncFee_'+sid).val().replace(/[^0-9.]/g, '')), APIAccessOverageFee:Number(parseFloat($('##APIAccessOverageFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(6)),
				APIAccessNoofCallsInOverageFee:parseInt($('##APIAccessNoofCallsInOverageFee_'+sid).val().replace(/[^0-9.]/g, '')),
				APIAccessNoFee:$('##APIAccessNoFee_'+sid).is(':checked')
				};
			TS_AJX('TRIALSMITHBILLING','saveAPIAccessInfo',objParams,SaveAPIAccessInfoResult,SaveAPIAccessInfoResult,10000,SaveAPIAccessInfoResult);
		}
		function validateAndSaveEmailBlastInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_EmailBlast_'+sid);
			$('##btn_EmailBlast_'+sid).prop('disabled',true);
			if($('##EmailBlastEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_EmailBlast_'+sid, arrErr.join('<br />'));
				$('##btn_EmailBlast_'+sid).prop('disabled',false);
				return false;
			}
			var SaveEmailBlastInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_EmailBlast_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_EmailBlast_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_EmailBlast_'+sid, 'We were unable to save the Email Blast Info. Try again.', false);
					$('##btn_EmailBlast_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, EmailBlastEffectiveDateMonth:$('##EmailBlastEffectiveDate_'+sid+'_Month').val(), EmailBlastEffectiveDateYear:$('##EmailBlastEffectiveDate_'+sid+'_Year').val(),
				EmailBlastInContract:$('##EmailBlastInContract_'+sid).is(':checked'), EmailBlastMonthlyFee:Number(parseFloat($('##EmailBlastMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				EmailBlastMonthlyComp:parseInt($('##EmailBlastMonthlyComp_'+sid).val().replace(/[^0-9.]/g, '')), EmailBlastMonthlyPerAdminComp:parseInt($('##EmailBlastMonthlyPerAdminComp_'+sid).val().replace(/[^0-9.]/g, '')),
				EmailBlastIncOtherApps:$('##EmailBlastIncOtherApps_'+sid).is(':checked'),
				EmailBlastBillingRate:Number(parseFloat($('##EmailBlastBillingRate_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(6)), EmailBlastNoFee:$('##EmailBlastNoFee_'+sid).is(':checked') 
				};
			TS_AJX('TRIALSMITHBILLING','saveEmailBlastInfo',objParams,SaveEmailBlastInfoResult,SaveEmailBlastInfoResult,10000,SaveEmailBlastInfoResult);
		}
		function validateAndSaveDistrictInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_District_'+sid);
			$('##btn_District_'+sid).prop('disabled',true);
			if($('##DistrictEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_District_'+sid, arrErr.join('<br />'));
				$('##btn_District_'+sid).prop('disabled',false);
				return false;
			}
			var SaveDistrictInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_District_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_District_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_District_'+sid, 'We were unable to save the District Matching Info. Try again.', false);
					$('##btn_District_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, DistrictEffectiveDateMonth:$('##DistrictEffectiveDate_'+sid+'_Month').val(), DistrictEffectiveDateYear:$('##DistrictEffectiveDate_'+sid+'_Year').val(),
				DistrictMatchingInContract:$('##DistrictMatchingInContract_'+sid).is(':checked'), 
				DistrictBillingRate:Number(parseFloat($('##DistrictBillingRate_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(6)), 
				DistrictNoFee:$('##DistrictNoFee_'+sid).is(':checked') 
				};
			TS_AJX('TRIALSMITHBILLING','saveDistrictInfo',objParams,SaveDistrictInfoResult,SaveDistrictInfoResult,10000,SaveDistrictInfoResult);
		}
		function validateAndSaveAddressUpdateInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_AddressUpdate_'+sid);
			$('##btn_AddressUpdate_'+sid).prop('disabled',true);
			if($('##AddressUpdateEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_AddressUpdate_'+sid, arrErr.join('<br />'));
				$('##btn_AddressUpdate_'+sid).prop('disabled',false);
				return false;
			}
			var SaveAddressUpdateInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_AddressUpdate_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_AddressUpdate_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_AddressUpdate_'+sid, 'We were unable to save the Address Updates Info. Try again.', false);
					$('##btn_AddressUpdate_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, AddressUpdateEffectiveDateMonth:$('##AddressUpdateEffectiveDate_'+sid+'_Month').val(), AddressUpdateEffectiveDateYear:$('##AddressUpdateEffectiveDate_'+sid+'_Year').val(),
				AddressUpdateInContract:$('##AddressUpdateInContract_'+sid).is(':checked'), 
				AddressUpdateMonthlyFee:Number(parseFloat($('##AddressUpdateMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				AddressUpdateBillingRate:Number(parseFloat($('##AddressUpdateBillingRate_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(6)), 
				AddressUpdateNoFee:$('##AddressUpdateNoFee_'+sid).is(':checked') 
				};
			TS_AJX('TRIALSMITHBILLING','saveAddressUpdateInfo',objParams,SaveAddressUpdateInfoResult,SaveAddressUpdateInfoResult,10000,SaveAddressUpdateInfoResult);
		}
		function validateAndSaveDedicatedServiceMgrInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_DedicatedServiceMgr_'+sid);
			$('##btn_DedicatedServiceMgr_'+sid).prop('disabled',true);
			if($('##DedicatedServiceMgrEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_DedicatedServiceMgr_'+sid, arrErr.join('<br />'));
				$('##btn_DedicatedServiceMgr_'+sid).prop('disabled',false);
				return false;
			}
			var SaveDedicatedServiceMgrInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_DedicatedServiceMgr_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_DedicatedServiceMgr_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_DedicatedServiceMgr_'+sid, 'We were unable to save the Dedicated Service Manager Info. Try again.', false);
					$('##btn_DedicatedServiceMgr_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, DedicatedServiceMgrEffectiveDateMonth:$('##DedicatedServiceMgrEffectiveDate_'+sid+'_Month').val(), DedicatedServiceMgrEffectiveDateYear:$('##DedicatedServiceMgrEffectiveDate_'+sid+'_Year').val(),
				DedicatedServiceMgrInContract:$('##DedicatedServiceMgrInContract_'+sid).is(':checked'), 
				DedicatedServiceMgrMonthlyFee:Number(parseFloat($('##DedicatedServiceMgrMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				DedicatedServiceMgrNoFee:$('##DedicatedServiceMgrNoFee_'+sid).is(':checked') 
				};
			TS_AJX('TRIALSMITHBILLING','saveDedicatedServiceMgrInfo',objParams,SaveDedicatedServiceMgrInfoResult,SaveDedicatedServiceMgrInfoResult,10000,SaveDedicatedServiceMgrInfoResult);
		}
		function validateAndSaveEmailHostingInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_EmailHosting_'+sid);
			$('##btn_EmailHosting_'+sid).prop('disabled',true);
			if($('##EmailHostingEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_EmailHosting_'+sid, arrErr.join('<br />'));
				$('##btn_EmailHosting_'+sid).prop('disabled',false);
				return false;
			}
			var SaveEmailHostingInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_EmailHosting_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_EmailHosting_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_EmailHosting_'+sid, 'We were unable to save the Email Hosting Info. Try again.', false);
					$('##btn_EmailHosting_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, EmailHostingEffectiveDateMonth:$('##EmailHostingEffectiveDate_'+sid+'_Month').val(), EmailHostingEffectiveDateYear:$('##EmailHostingEffectiveDate_'+sid+'_Year').val(),
				EmailHostingInContract:$('##EmailHostingInContract_'+sid).is(':checked'), 
				EmailHostingMonthlyFee:Number(parseFloat($('##EmailHostingMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				EmailHostingNoFee:$('##EmailHostingNoFee_'+sid).is(':checked') 
				};
			TS_AJX('TRIALSMITHBILLING','saveEmailHostingInfo',objParams,SaveEmailHostingInfoResult,SaveEmailHostingInfoResult,10000,SaveEmailHostingInfoResult);
		}
		function validateAndSavePrivateListServerDomainInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_PrivateListServerDomain_'+sid);
			$('##btn_PrivateListServerDomain_'+sid).prop('disabled',true);
			if($('##PrivateListServerDomainEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_PrivateListServerDomain_'+sid, arrErr.join('<br />'));
				$('##btn_PrivateListServerDomain_'+sid).prop('disabled',false);
				return false;
			}
			var SavePrivateListServerDomainInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_PrivateListServerDomain_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_PrivateListServerDomain_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_PrivateListServerDomain_'+sid, 'We were unable to save the Privatized List Server Domain Info. Try again.', false);
					$('##btn_PrivateListServerDomain_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, PrivateListServerDomainEffectiveDateMonth:$('##PrivateListServerDomainEffectiveDate_'+sid+'_Month').val(), PrivateListServerDomainEffectiveDateYear:$('##PrivateListServerDomainEffectiveDate_'+sid+'_Year').val(),
				PrivateListServerDomainInContract:$('##PrivateListServerDomainInContract_'+sid).is(':checked'), 
				PrivateListServerDomainMonthlyFee:Number(parseFloat($('##PrivateListServerDomainMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				PrivateListServerDomainNoFee:$('##PrivateListServerDomainNoFee_'+sid).is(':checked') 
				};
			TS_AJX('TRIALSMITHBILLING','savePrivateListServerDomainInfo',objParams,SavePrivateListServerDomainInfoResult,SavePrivateListServerDomainInfoResult,10000,SavePrivateListServerDomainInfoResult);
		}
		function validateAndSavePrivateEmailSendingDomainInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_PrivateEmailSendingDomain_'+sid);
			$('##btn_PrivateEmailSendingDomain_'+sid).prop('disabled',true);
			if($('##PrivateEmailSendingDomainEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_PrivateEmailSendingDomain_'+sid, arrErr.join('<br />'));
				$('##btn_PrivateEmailSendingDomain_'+sid).prop('disabled',false);
				return false;
			}
			var SavePrivateEmailSendingDomainInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_PrivateEmailSendingDomain_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_PrivateEmailSendingDomain_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_PrivateEmailSendingDomain_'+sid, 'We were unable to save the Privatized Email Sending Domain Info. Try again.', false);
					$('##btn_PrivateEmailSendingDomain_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, PrivateEmailSendingDomainEffectiveDateMonth:$('##PrivateEmailSendingDomainEffectiveDate_'+sid+'_Month').val(), PrivateEmailSendingDomainEffectiveDateYear:$('##PrivateEmailSendingDomainEffectiveDate_'+sid+'_Year').val(),
				PrivateEmailSendingDomainInContract:$('##PrivateEmailSendingDomainInContract_'+sid).is(':checked'), 
				PrivateEmailSendingDomainMonthlyFee:Number(parseFloat($('##PrivateEmailSendingDomainMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				PrivateEmailSendingDomainNoFee:$('##PrivateEmailSendingDomainNoFee_'+sid).is(':checked') 
				};
			TS_AJX('TRIALSMITHBILLING','savePrivateEmailSendingDomainInfo',objParams,SavePrivateEmailSendingDomainInfoResult,SavePrivateEmailSendingDomainInfoResult,10000,SavePrivateEmailSendingDomainInfoResult);
		}
		function validateAndSaveEntPlatformSecurityInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_EntPlatformSecurity_'+sid);
			$('##btn_EntPlatformSecurity_'+sid).prop('disabled',true);
			if($('##EntPlatformSecurityEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_EntPlatformSecurity_'+sid, arrErr.join('<br />'));
				$('##btn_EntPlatformSecurity_'+sid).prop('disabled',false);
				return false;
			}
			var SaveEntPlatformSecurityInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_EntPlatformSecurity_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_EntPlatformSecurity_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_EntPlatformSecurity_'+sid, 'We were unable to save the Enterprise Platform Security Info. Try again.', false);
					$('##btn_EntPlatformSecurity_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, EntPlatformSecurityEffectiveDateMonth:$('##EntPlatformSecurityEffectiveDate_'+sid+'_Month').val(), EntPlatformSecurityEffectiveDateYear:$('##EntPlatformSecurityEffectiveDate_'+sid+'_Year').val(),
				EntPlatformSecurityInContract:$('##EntPlatformSecurityInContract_'+sid).is(':checked'), 
				EntPlatformSecurityMonthlyFee:Number(parseFloat($('##EntPlatformSecurityMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				EntPlatformSecurityNoFee:$('##EntPlatformSecurityNoFee_'+sid).is(':checked') 
				};
			TS_AJX('TRIALSMITHBILLING','saveEntPlatformSecurityInfo',objParams,SaveEntPlatformSecurityInfoResult,SaveEntPlatformSecurityInfoResult,10000,SaveEntPlatformSecurityInfoResult);
		}
		function validateAndSaveBadgePrintingInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_BadgePrinting_'+sid);
			$('##btn_BadgePrinting_'+sid).prop('disabled',true);
			if($('##BadgePrintingEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_BadgePrinting_'+sid, arrErr.join('<br />'));
				$('##btn_BadgePrinting_'+sid).prop('disabled',false);
				return false;
			}
			var SaveBadgePrintingInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_BadgePrinting_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_BadgePrinting_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_BadgePrinting_'+sid, 'We were unable to save the Badge Printing Info. Try again.', false);
					$('##btn_BadgePrinting_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, BadgePrintingEffectiveDateMonth:$('##BadgePrintingEffectiveDate_'+sid+'_Month').val(), BadgePrintingEffectiveDateYear:$('##BadgePrintingEffectiveDate_'+sid+'_Year').val(),
				BadgePrintingInContract:$('##BadgePrintingInContract_'+sid).is(':checked'), 
				BadgePrintingMonthlyFee:Number(parseFloat($('##BadgePrintingMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				BadgePrintingNoofCallIncFee:parseInt($('##BadgePrintingNoofCallIncFee_'+sid).val().replace(/[^0-9.]/g, '')),
				BadgePrintingOverageFee:Number(parseFloat($('##BadgePrintingOverageFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				BadgePrintingNoofCallsInOverageFee:parseInt($('##BadgePrintingNoofCallsInOverageFee_'+sid).val().replace(/[^0-9.]/g, '')),
				BadgePrintingNoFee:$('##BadgePrintingNoFee_'+sid).is(':checked') 
				};
			TS_AJX('TRIALSMITHBILLING','saveBadgePrintingInfo',objParams,SaveBadgePrintingInfoResult,SaveBadgePrintingInfoResult,10000,SaveBadgePrintingInfoResult);
		}
		function validateAndSaveDiscretionaryInfo(sid) {
			var arrErr = [];
			mca_hideAlert('err_Discretionary_'+sid);
			$('##btn_Discretionary_'+sid).prop('disabled',true);
			if($('##DiscretionaryEffectiveDate_'+sid+'_Month').val().trim() == '') {
				arrErr.push('Select Month.');
			} 
			if (arrErr.length) {
				mca_showAlert('err_Discretionary_'+sid, arrErr.join('<br />'));
				$('##btn_Discretionary_'+sid).prop('disabled',false);
				return false;
			}
			var SaveDiscretionaryInfoResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
						if(sid>0){
							$('##btn_Discretionary_'+sid).next('span').html('<small class="badge badge-success">Saved Successfully</small>').fadeOut(4000);
							$('##btn_Discretionary_'+sid).prop('disabled',false);
						}else {
							self.location.href = "#this.link.trialSmithBilling#&tab=mcbilling";
						}                 
				} else {
					mca_showAlert('err_Discretionary_'+sid, 'We were unable to save the Discretionary Billing Info. Try again.', false);
					$('##btn_Discretionary_'+sid).prop('disabled',false);
					
				}
			};
			let objParams = { scheduleID:sid, DiscretionaryEffectiveDateMonth:$('##DiscretionaryEffectiveDate_'+sid+'_Month').val(), DiscretionaryEffectiveDateYear:$('##DiscretionaryEffectiveDate_'+sid+'_Year').val(),
				DiscretionaryInContract:$('##DiscretionaryInContract_'+sid).is(':checked'), 
				DiscretionaryMonthlyFee:Number(parseFloat($('##DiscretionaryMonthlyFee_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				DiscretionaryNumMonths:Number(parseInt($('##DiscretionaryNumMonths_'+sid).val().replace(/[^0-9.]/g, '')).toFixed(2)),
				DiscretionaryFeeDesc:$('##DiscretionaryFeeDesc_'+sid).val() 
				};
			TS_AJX('TRIALSMITHBILLING','saveDiscretionaryInfo',objParams,SaveDiscretionaryInfoResult,SaveDiscretionaryInfoResult,10000,SaveDiscretionaryInfoResult);
		}
		function clearAddressUpdateSchedule(id) {
			if (document.getElementById('AddressUpdateNoFee_' + id).checked) {
				document.getElementById('AddressUpdateMonthlyFee_' + id).value = formatCurrency(0);
				document.getElementById('AddressUpdateBillingRate_' + id).value = formatCurrency(0);
				document.getElementById('tbody_AddressUpdate_' + id).addClass('d-none');
			} else {
				document.getElementById('tbody_AddressUpdate_' + id).removeClass('d-none');
			}
		}
		function clearMCBillingSectionSchedule(id,section) {
			if (document.getElementById(section+'NoFee_'+id).checked) {
				document.querySelectorAll('.'+section+'MoneyField_'+id).forEach(function(el) {
					el.value = formatCurrency(0);
				});
				document.querySelectorAll('.'+section+'CountField_'+id).forEach(function(el) {
					el.value = 0;
				});
				document.querySelectorAll('.'+section+'TextField_'+id).forEach(function(el) {
					el.value = '';
				});
				document.querySelectorAll('.'+section+'BillRow_'+id).forEach(function(el) {
					el.classList.add('d-none');
				});
			} else {
				document.querySelectorAll('.'+section+'BillRow_'+id).forEach(function(el) {
					el.classList.remove('d-none');
				});
			}
		}
		function clearEmailBlastSchedule(id) {
			if (document.getElementById('EmailBlastNoFee_' + id).checked) {
				document.getElementById('EmailBlastMonthlyFee_' + id).value = formatCurrency(0);
				document.getElementById('EmailBlastMonthlyComp_' + id).value = 0;
				document.getElementById('EmailBlastMonthlyPerAdminComp_' + id).value = 0;
				document.getElementById('EmailBlastBillingRate_' + id).value = formatCurrency(0);
				document.getElementById('tbody_EmailBlast_' + id).addClass('d-none');
			} else {
				document.getElementById('tbody_EmailBlast_' + id).removeClass('d-none');
			}
		}
		function clearDistrictSchedule(id) {
			if (document.getElementById('DistrictNoFee_' + id).checked) {
				document.getElementById('DistrictBillingRate_' + id).value = formatCurrency(0);
				document.getElementById('tbody_District_' + id).addClass('d-none');
			} else {
				document.getElementById('tbody_District_' + id).removeClass('d-none');
			}
		}
		$(function() {
			$('div.PastBillSchedTabContent').find('input:not([type=hidden]),select,textarea,button').prop('disabled',true);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.mcBillingTabsJS)#">

<cfoutput>
<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="amshosting">Website/AMS Hosting</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgHosting">
				<cfset local.thisTabName = "AMSHosting#local.strDepoTLA.qryOrgHosting.scheduleID#">
				<cfset local.thisTabID = "AMSHosting#local.strDepoTLA.qryOrgHosting.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgHosting.effectiveDate eq local.strDepoTLA.qryDepoTLA.HostingEffectiveDate OR (local.strDepoTLA.qryDepoTLA.HostingEffectiveDate eq "" and local.strDepoTLA.qryOrgHosting.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgHosting.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "AMSHostingX">
			<cfset local.thisTabID = "AMSHostingXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgHosting.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
				   + Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgHosting">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgHosting.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgHosting.effectiveDate eq local.strDepoTLA.qryDepoTLA.HostingEffectiveDate OR (local.strDepoTLA.qryDepoTLA.HostingEffectiveDate eq "" and local.strDepoTLA.qryOrgHosting.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-AMSHosting#local.strDepoTLA.qryOrgHosting.scheduleID#Tab" role="tabpanel" aria-labelledby="AMSHosting#local.strDepoTLA.qryOrgHosting.scheduleID#">
					<form name="frmAMSHosting#local.strDepoTLA.qryOrgHosting.scheduleID#" id="frmAMSHosting#local.strDepoTLA.qryOrgHosting.scheduleID#">
						<div id="err_AMSHosting_#local.strDepoTLA.qryOrgHosting.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgHosting.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.futureBillingSchedule>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="AMSHostingEffectiveDate_#local.strDepoTLA.qryOrgHosting.scheduleID#_Month" id="AMSHostingEffectiveDate_#local.strDepoTLA.qryOrgHosting.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgHosting.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgHosting.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="AMSHostingEffectiveDate_#local.strDepoTLA.qryOrgHosting.scheduleID#_Year" id="AMSHostingEffectiveDate_#local.strDepoTLA.qryOrgHosting.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgHosting.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelAMSHostingSched_#local.strDepoTLA.qryOrgHosting.scheduleID#" id="btnDelAMSHostingSched_#local.strDepoTLA.qryOrgHosting.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('AMSHosting',#local.strDepoTLA.qryOrgHosting.scheduleID#,'mcbilling');" style="margin-left:auto;">
										<span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgHosting.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="AMSHostingInContract_#local.strDepoTLA.qryOrgHosting.scheduleID#" id="AMSHostingInContract_#local.strDepoTLA.qryOrgHosting.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgHosting.inContract> checked</cfif>> 
								<label for="AMSHostingInContract_#local.strDepoTLA.qryOrgHosting.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="AMSHostingBillRow_#local.strDepoTLA.qryOrgHosting.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="AMSHostingAdminRate_#local.strDepoTLA.qryOrgHosting.scheduleID#" name="AMSHostingAdminRate_#local.strDepoTLA.qryOrgHosting.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgHosting.adminRate))#" class="AMSHostingMoneyField_#local.strDepoTLA.qryOrgHosting.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> for up to <input type="text" id="AMSHostingAdminRateInc_#local.strDepoTLA.qryOrgHosting.scheduleID#" name="AMSHostingAdminRateInc_#local.strDepoTLA.qryOrgHosting.scheduleID#" value="#val(local.strDepoTLA.qryOrgHosting.adminRateInc)#" class="AMSHostingCountField_#local.strDepoTLA.qryOrgHosting.scheduleID# form-control-sm no-border-radius" size="8" maxlength="6"> admins. Additional Admins at <input type="text" id="AMSHostingAdminRateAddtl_#local.strDepoTLA.qryOrgHosting.scheduleID#" name="AMSHostingAdminRateAddtl_#local.strDepoTLA.qryOrgHosting.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgHosting.adminRateAddtl))#" class="AMSHostingMoneyField_#local.strDepoTLA.qryOrgHosting.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> each.
							</li>
							<li class="AMSHostingBillRow_#local.strDepoTLA.qryOrgHosting.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="AMSHostingMonthlyMin_#local.strDepoTLA.qryOrgHosting.scheduleID#" name="AMSHostingMonthlyMin_#local.strDepoTLA.qryOrgHosting.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgHosting.monthlyMin))#" class="AMSHostingMoneyField_#local.strDepoTLA.qryOrgHosting.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> monthly minimum.
							</li>
							<li class="AMSHostingBillRow_#local.strDepoTLA.qryOrgHosting.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="AMSHostingMonthlyFeeAMS_#local.strDepoTLA.qryOrgHosting.scheduleID#" name="AMSHostingMonthlyFeeAMS_#local.strDepoTLA.qryOrgHosting.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgHosting.monthlyFeeAMS))#" class="AMSHostingMoneyField_#local.strDepoTLA.qryOrgHosting.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month for AMS Hosting (flat fee).
							</li>
							<li class="AMSHostingBillRow_#local.strDepoTLA.qryOrgHosting.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="AMSHostingMonthlyFeeWeb_#local.strDepoTLA.qryOrgHosting.scheduleID#" name="AMSHostingMonthlyFeeWeb_#local.strDepoTLA.qryOrgHosting.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgHosting.monthlyFeeWeb))#" class="AMSHostingMoneyField_#local.strDepoTLA.qryOrgHosting.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month for Website Hosting (flat fee).
							</li>
							<li>
								<input type="checkbox" id="AMSHostingNoFee_#local.strDepoTLA.qryOrgHosting.scheduleID#" name="AMSHostingNoFee_#local.strDepoTLA.qryOrgHosting.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgHosting.noFee> checked</cfif> onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgHosting.scheduleID#','AMSHosting')"> <label for="AMSHostingNoFee_#local.strDepoTLA.qryOrgHosting.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_AMSHosting_#local.strDepoTLA.qryOrgHosting.scheduleID#" name="btn_AMSHosting_#local.strDepoTLA.qryOrgHosting.scheduleID#" class="btn btn-sm btn-primary" onclick="validateAndSaveAMSHostingInfo(#local.strDepoTLA.qryOrgHosting.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-AMSHostingXTab" role="tabpanel" aria-labelledby="AMSHostingX">
				<form name="frmAMSHosting0" id="frmAMSHosting0">
					<div id="err_AMSHosting_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								Effective 
								<select name="AMSHostingEffectiveDate_0_Month" id="AMSHostingEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month" class="form-control-sm no-border-radius"  >
									<option value="">Month</option>
									<cfloop from="1" to="12" index="thisMonth">
										<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
									</cfloop>
								</select>
								<cfset local.yearStart = Year(now()) - 5>
								<cfset local.yearEnd = local.yearStart + 20>
								<select name="AMSHostingEffectiveDate_0_Year" id="AMSHostingEffectiveDate_0_Year" class="form-control-sm no-border-radius" >
									<cfloop from="#local.yearStart#" to="#local.yearEnd#" index="thisYear">
										<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
									</cfloop>
								</select>
							</li>
							<li>
								<input type="checkbox" name="AMSHostingInContract_0" id="AMSHostingInContract_0" value="1"> 
								<label for="AMSHostingInContract_0">Fees outlined here are in the current contract.</label>
							</li>
							<li class="AMSHostingBillRow_0">
								<input type="text" id="AMSHostingAdminRate_0" name="AMSHostingAdminRate_0" value="#dollarFormat(0)#" class="AMSHostingMoneyField_0 form-control-sm no-border-radius"  size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> for up to <input type="text" id="AMSHostingAdminRateInc_0" name="AMSHostingAdminRateInc_0" value="0" class="AMSHostingCountField_0 form-control-sm no-border-radius"  size="8" maxlength="6"> admins. 
								Additional Admins at <input type="text" id="AMSHostingAdminRateAddtl_0" name="AMSHostingAdminRateAddtl_0" value="#dollarFormat(0)#" class="AMSHostingMoneyField_0 form-control-sm no-border-radius"  size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> each.
							</li>
							<li class="AMSHostingBillRow_0">
								<input type="text" id="AMSHostingMonthlyMin_0" name="AMSHostingMonthlyMin_0" value="#dollarFormat(0)#" class="AMSHostingMoneyField_0 form-control-sm no-border-radius"  size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> monthly minimum.
							</li>
							<li class="AMSHostingBillRow_0">
								<input type="text" id="AMSHostingMonthlyFeeAMS_0" name="AMSHostingMonthlyFeeAMS_0" value="#dollarFormat(0)#" class="AMSHostingMoneyField_0  form-control-sm no-border-radius"  size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month for AMS Hosting (flat fee).
							</li>
							<li class="AMSHostingBillRow_0">
								<input type="text" id="AMSHostingMonthlyFeeWeb_0" name="AMSHostingMonthlyFeeWeb_0" value="#dollarFormat(0)#" class="AMSHostingMoneyField_0 form-control-sm no-border-radius"  size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month for Website Hosting (flat fee).
							</li>
							<li>
								<input type="checkbox" id="AMSHostingNoFee_0" name="AMSHostingNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'AMSHosting')"> 
								<label for="AMSHostingNoFee_0">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_AMSHosting_0" name="btn_AMSHosting_0" onclick="validateAndSaveAMSHostingInfo(0);" class="btn btn-sm btn-primary ">Save Schedule</button>
							<span></span>
						</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="Fundraising">Fundraising</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgFundraising">
				<cfset local.thisTabName = "Fundraising#local.strDepoTLA.qryOrgFundraising.scheduleID#">
				<cfset local.thisTabID = "Fundraising#local.strDepoTLA.qryOrgFundraising.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgFundraising.effectiveDate eq local.strDepoTLA.qryDepoTLA.FundraisingEffectiveDate OR (local.strDepoTLA.qryDepoTLA.FundraisingEffectiveDate eq "" and local.strDepoTLA.qryOrgFundraising.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgFundraising.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "FundraisingX">
			<cfset local.thisTabID = "FundraisingXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgFundraising.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
				   + Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgFundraising">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgFundraising.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgFundraising.effectiveDate eq local.strDepoTLA.qryDepoTLA.FundraisingEffectiveDate OR (local.strDepoTLA.qryDepoTLA.FundraisingEffectiveDate eq "" and local.strDepoTLA.qryOrgFundraising.currentrow is 1)> active show</cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-Fundraising#local.strDepoTLA.qryOrgFundraising.scheduleID#Tab" role="tabpanel" aria-labelledby="Fundraising#local.strDepoTLA.qryOrgFundraising.scheduleID#">
					<form name="frmFundraising#local.strDepoTLA.qryOrgFundraising.scheduleID#" id="frmFundraising#local.strDepoTLA.qryOrgFundraising.scheduleID#">
						<div id="err_Fundraising_#local.strDepoTLA.qryOrgFundraising.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgFundraising.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgFundraising.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="FundraisingEffectiveDate_#local.strDepoTLA.qryOrgFundraising.scheduleID#_Month" id="FundraisingEffectiveDate_#local.strDepoTLA.qryOrgFundraising.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month" class="form-control-sm no-border-radius">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgFundraising.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgFundraising.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="FundraisingEffectiveDate_#local.strDepoTLA.qryOrgFundraising.scheduleID#_Year" id="FundraisingEffectiveDate_#local.strDepoTLA.qryOrgFundraising.scheduleID#_Year" class="form-control-sm no-border-radius">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgFundraising.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelFundraisingSched_#local.strDepoTLA.qryOrgFundraising.scheduleID#" id="btnDelFundraisingSched_#local.strDepoTLA.qryOrgFundraising.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('Fundraising',#local.strDepoTLA.qryOrgFundraising.scheduleID#,'mcbilling');" style="margin-left:auto;">
											Delete Schedule	
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgFundraising.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="FundraisingInContract_#local.strDepoTLA.qryOrgFundraising.scheduleID#" id="FundraisingInContract_#local.strDepoTLA.qryOrgFundraising.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgFundraising.inContract> checked</cfif>> 
								<label for="FundraisingInContract_#local.strDepoTLA.qryOrgFundraising.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="FundraisingBillRow_#local.strDepoTLA.qryOrgFundraising.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="FundraisingMonthlyFee_#local.strDepoTLA.qryOrgFundraising.scheduleID#" name="FundraisingMonthlyFee_#local.strDepoTLA.qryOrgFundraising.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgFundraising.monthlyFee))#" class="FundraisingMoneyField_#local.strDepoTLA.qryOrgFundraising.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="FundraisingNoFee_#local.strDepoTLA.qryOrgFundraising.scheduleID#" name="FundraisingNoFee_#local.strDepoTLA.qryOrgFundraising.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgFundraising.noFee> checked</cfif> onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgFundraising.scheduleID#','Fundraising')"> <label for="FundraisingNoFee_#local.strDepoTLA.qryOrgFundraising.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_Fundraising_#local.strDepoTLA.qryOrgFundraising.scheduleID#" name="btn_Fundraising_#local.strDepoTLA.qryOrgFundraising.scheduleID#"  class="btn btn-sm btn-primary " onclick="validateAndSaveFundraisingInfo(#local.strDepoTLA.qryOrgFundraising.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-FundraisingXTab" role="tabpanel" aria-labelledby="FundraisingX">
				<form name="frmFundraising0" id="frmFundraising0">
					<div id="err_Fundraising_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Effective 
							<select name="FundraisingEffectiveDate_0_Month"  id="FundraisingEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month" class="form-control-sm no-border-radius">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="FundraisingEffectiveDate_0_Year" id="FundraisingEffectiveDate_0_Year" class="form-control-sm no-border-radius">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="FundraisingInContract_0" id="FundraisingInContract_0" value="1"> 
							<label for="FundraisingInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="FundraisingBillRow_0">
							<input type="text" id="FundraisingMonthlyFee_0" name="FundraisingMonthlyFee_0" value="#dollarFormat(0)#" class="FundraisingMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li>
							<input type="checkbox" id="FundraisingNoFee_0" name="FundraisingNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'Fundraising')"> <label for="FundraisingNoFee_0">Contract specifies no monthly fee.</label>
						</li>
					</ul>
					<div class="col">
						<button type="button" id="btn_Fundraising_0" id="btn_Fundraising_0" class="btn btn-sm btn-primary " onclick="validateAndSaveFundraisingInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="lris">Referrals</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgLRIS">
				<cfset local.thisTabName = "LRIS#local.strDepoTLA.qryOrgLRIS.scheduleID#">
				<cfset local.thisTabID = "LRIS#local.strDepoTLA.qryOrgLRIS.scheduleID#Tab">
			   <li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgLRIS.effectiveDate eq local.strDepoTLA.qryDepoTLA.LRISEffectiveDate OR (local.strDepoTLA.qryDepoTLA.LRISEffectiveDate eq "" and local.strDepoTLA.qryOrgLRIS.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgLRIS.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "LRISX">
			<cfset local.thisTabID = "LRISXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgLRIS.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
				   + Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgLRIS">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgLRIS.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgLRIS.effectiveDate eq local.strDepoTLA.qryDepoTLA.LRISEffectiveDate OR (local.strDepoTLA.qryDepoTLA.LRISEffectiveDate eq "" and local.strDepoTLA.qryOrgLRIS.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-LRIS#local.strDepoTLA.qryOrgLRIS.scheduleID#Tab" role="tabpanel" aria-labelledby="LRIS#local.strDepoTLA.qryOrgLRIS.scheduleID#">
					<form name="frmLRIS#local.strDepoTLA.qryOrgLRIS.scheduleID#" id="frmLRIS#local.strDepoTLA.qryOrgLRIS.scheduleID#">
						<div id="err_LRIS_#local.strDepoTLA.qryOrgLRIS.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgLRIS.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgLRIS.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="LRISEffectiveDate_#local.strDepoTLA.qryOrgLRIS.scheduleID#_Month" id="LRISEffectiveDate_#local.strDepoTLA.qryOrgLRIS.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgLRIS.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgLRIS.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="LRISEffectiveDate_#local.strDepoTLA.qryOrgLRIS.scheduleID#_Year" id="LRISEffectiveDate_#local.strDepoTLA.qryOrgLRIS.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgLRIS.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelLRISSched_#local.strDepoTLA.qryOrgLRIS.scheduleID#" id="btnDelLRISSched_#local.strDepoTLA.qryOrgLRIS.scheduleID#" class="btn btn-danger"  onclick="deleteTSBillingSchedule('LRIS',#local.strDepoTLA.qryOrgLRIS.scheduleID#,'mcbilling');" style="margin-left:auto;">
											Delete Schedule
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgLRIS.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="LRISInContract_#local.strDepoTLA.qryOrgLRIS.scheduleID#" id="LRISInContract_#local.strDepoTLA.qryOrgLRIS.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgLRIS.inContract> checked</cfif>> 
								<label for="LRISInContract_#local.strDepoTLA.qryOrgLRIS.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="LRISBillRow_#local.strDepoTLA.qryOrgLRIS.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="LRISMonthlyFee_#local.strDepoTLA.qryOrgLRIS.scheduleID#" name="LRISMonthlyFee_#local.strDepoTLA.qryOrgLRIS.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgLRIS.monthlyFee))#" class="LRISMoneyField_#local.strDepoTLA.qryOrgLRIS.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="LRISNoFee_#local.strDepoTLA.qryOrgLRIS.scheduleID#" name="LRISNoFee_#local.strDepoTLA.qryOrgLRIS.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgLRIS.noFee> checked</cfif> onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgLRIS.scheduleID#','LRIS')"> <label for="LRISNoFee_#local.strDepoTLA.qryOrgLRIS.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_LRIS_#local.strDepoTLA.qryOrgLRIS.scheduleID#" name="btn_LRIS_#local.strDepoTLA.qryOrgLRIS.scheduleID#" class="btn btn-sm btn-primary "onclick="validateAndSaveLRISInfo(#local.strDepoTLA.qryOrgLRIS.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-LRISXTab" role="tabpanel" aria-labelledby="LRISX">
				<form name="frmLRIS0" id="frmLRIS0">
					<div id="err_LRIS_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Effective 
							<select name="LRISEffectiveDate_0_Month"  id="LRISEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="LRISEffectiveDate_0_Year" id="LRISEffectiveDate_0_Year">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="LRISInContract_0" id="LRISInContract_0" value="1"> 
							<label for="LRISInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="LRISBillRow_0">
							<input type="text" id="LRISMonthlyFee_0" name="LRISMonthlyFee_0" value="#dollarFormat(0)#" class="LRISMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li>
							<input type="checkbox" id="LRISNoFee_0" name="LRISNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'LRIS')"> <label for="LRISNoFee_0">Contract specifies no monthly fee.</label>
						</li>
					</ul>
					<div class="col">
						<button type="button" id="btn_LRIS_0" name="btn_LRIS_0" class="btn btn-sm btn-primary "  onclick="validateAndSaveLRISInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="publications">Publications</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgPublications">
				<cfset local.thisTabName = "Publications#local.strDepoTLA.qryOrgPublications.scheduleID#">
				<cfset local.thisTabID = "Publications#local.strDepoTLA.qryOrgPublications.scheduleID#Tab">
			   <li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgPublications.effectiveDate eq local.strDepoTLA.qryDepoTLA.PublicationsEffectiveDate OR (local.strDepoTLA.qryDepoTLA.PublicationsEffectiveDate eq "" and local.strDepoTLA.qryOrgPublications.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgPublications.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "PublicationsX">
			<cfset local.thisTabID = "PublicationsXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgPublications.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
				   + Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgPublications">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgPublications.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgPublications.effectiveDate eq local.strDepoTLA.qryDepoTLA.PublicationsEffectiveDate OR (local.strDepoTLA.qryDepoTLA.PublicationsEffectiveDate eq "" and local.strDepoTLA.qryOrgPublications.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-Publications#local.strDepoTLA.qryOrgPublications.scheduleID#Tab" role="tabpanel" aria-labelledby="Publications#local.strDepoTLA.qryOrgPublications.scheduleID#">
					<form name="frmPublications#local.strDepoTLA.qryOrgPublications.scheduleID#" id="frmPublications#local.strDepoTLA.qryOrgPublications.scheduleID#">
						<div id="err_Publications_#local.strDepoTLA.qryOrgPublications.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgPublications.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgPublications.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="PublicationsEffectiveDate_#local.strDepoTLA.qryOrgPublications.scheduleID#_Month" id="PublicationsEffectiveDate_#local.strDepoTLA.qryOrgPublications.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgPublications.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgPublications.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="PublicationsEffectiveDate_#local.strDepoTLA.qryOrgPublications.scheduleID#_Year" id="PublicationsEffectiveDate_#local.strDepoTLA.qryOrgPublications.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgPublications.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelPublicationsSched_#local.strDepoTLA.qryOrgPublications.scheduleID#" id="btnDelPublicationsSched_#local.strDepoTLA.qryOrgPublications.scheduleID#" class="btn btn-danger"  onclick="deleteTSBillingSchedule('Publications',#local.strDepoTLA.qryOrgPublications.scheduleID#,'mcbilling');" style="margin-left:auto;">
										   Delete Schedule
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgPublications.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="PublicationsInContract_#local.strDepoTLA.qryOrgPublications.scheduleID#" id="PublicationsInContract_#local.strDepoTLA.qryOrgPublications.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgPublications.inContract> checked</cfif>> 
								<label for="PublicationsInContract_#local.strDepoTLA.qryOrgPublications.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="PublicationsBillRow_#local.strDepoTLA.qryOrgPublications.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="PublicationsMonthlyFee_#local.strDepoTLA.qryOrgPublications.scheduleID#" name="PublicationsMonthlyFee_#local.strDepoTLA.qryOrgPublications.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgPublications.monthlyFee))#" class="PublicationsMoneyField_#local.strDepoTLA.qryOrgPublications.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="PublicationsNoFee_#local.strDepoTLA.qryOrgPublications.scheduleID#" name="PublicationsNoFee_#local.strDepoTLA.qryOrgPublications.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgPublications.noFee> checked</cfif> onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgPublications.scheduleID#','Publications')"> <label for="PublicationsNoFee_#local.strDepoTLA.qryOrgPublications.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_Publications_#local.strDepoTLA.qryOrgPublications.scheduleID#" name="btn_Publications_#local.strDepoTLA.qryOrgPublications.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSavePublicationsInfo(#local.strDepoTLA.qryOrgPublications.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-PublicationsXTab" role="tabpanel" aria-labelledby="PublicationsX">
				<form name="frmPublications0" id="frmPublications0">
					<div id="err_Publications_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Effective 
							<select name="PublicationsEffectiveDate_0_Month" id="PublicationsEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="PublicationsEffectiveDate_0_Year" id="PublicationsEffectiveDate_0_Year">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="PublicationsInContract_0" id="PublicationsInContract_0" value="1"> 
							<label for="PublicationsInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="PublicationsBillRow_0">
							<input type="text" id="PublicationsMonthlyFee_0" name="PublicationsMonthlyFee_0" value="#dollarFormat(0)#" class="PublicationsMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li>
							<input type="checkbox" id="PublicationsNoFee_0" name="PublicationsNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'Publications')"> <label for="PublicationsNoFee_0">Contract specifies no monthly fee.</label>
						</li>
					</ul>
					<div class="col">
						<button type="button" id="btn_Publications_0" name="btn_Publications_0" class="btn btn-sm btn-primary "  onclick="validateAndSavePublicationsInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="Solicitations">Solicitations</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgSolicitations">
				<cfset local.thisTabName = "Solicitations#local.strDepoTLA.qryOrgSolicitations.scheduleID#">
				<cfset local.thisTabID = "Solicitations#local.strDepoTLA.qryOrgSolicitations.scheduleID#Tab">
			   <li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgSolicitations.effectiveDate eq local.strDepoTLA.qryDepoTLA.SolicitationsEffectiveDate OR (local.strDepoTLA.qryDepoTLA.SolicitationsEffectiveDate eq "" and local.strDepoTLA.qryOrgSolicitations.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgSolicitations.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "SolicitationsX">
			<cfset local.thisTabID = "SolicitationsXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgSolicitations.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
				   + Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgSolicitations">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgSolicitations.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgSolicitations.effectiveDate eq local.strDepoTLA.qryDepoTLA.SolicitationsEffectiveDate OR (local.strDepoTLA.qryDepoTLA.SolicitationsEffectiveDate eq "" and local.strDepoTLA.qryOrgSolicitations.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-Solicitations#local.strDepoTLA.qryOrgSolicitations.scheduleID#Tab" role="tabpanel" aria-labelledby="Solicitations#local.strDepoTLA.qryOrgSolicitations.scheduleID#">
					<form name="frmSolicitations#local.strDepoTLA.qryOrgSolicitations.scheduleID#" id="frmSolicitations#local.strDepoTLA.qryOrgSolicitations.scheduleID#">
						<div id="err_Solicitations_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgSolicitations.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgSolicitations.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="SolicitationsEffectiveDate_#local.strDepoTLA.qryOrgSolicitations.scheduleID#_Month" id="SolicitationsEffectiveDate_#local.strDepoTLA.qryOrgSolicitations.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgSolicitations.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgSolicitations.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="SolicitationsEffectiveDate_#local.strDepoTLA.qryOrgSolicitations.scheduleID#_Year" id="SolicitationsEffectiveDate_#local.strDepoTLA.qryOrgSolicitations.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgSolicitations.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										
										<button type="button" name="btnDelSolicitationsSched_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" id="btnDelSolicitationsSched_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" class="btn btn-danger"  onclick="deleteTSBillingSchedule('Solicitations',#local.strDepoTLA.qryOrgSolicitations.scheduleID#,'mcbilling');" style="margin-left:auto;">
											<span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgSolicitations.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="SolicitationsInContract_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" id="SolicitationsInContract_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgSolicitations.inContract> checked</cfif>> 
								<label for="SolicitationsInContract_#local.strDepoTLA.qryOrgSolicitations.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="SolicitationsBillRow_#local.strDepoTLA.qryOrgSolicitations.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="SolicitationsMonthlyFee_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" name="SolicitationsMonthlyFee_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgSolicitations.monthlyFee))#" class="SolicitationsMoneyField_#local.strDepoTLA.qryOrgSolicitations.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="SolicitationsNoFee_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" name="SolicitationsNoFee_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgSolicitations.noFee> checked</cfif>  onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgSolicitations.scheduleID#','Solicitations')"> <label for="SolicitationsNoFee_#local.strDepoTLA.qryOrgSolicitations.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_Solicitations_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" name="btn_Solicitations_#local.strDepoTLA.qryOrgSolicitations.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveSolicitationsInfo(#local.strDepoTLA.qryOrgSolicitations.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-SolicitationsXTab" role="tabpanel" aria-labelledby="SolicitationsX">
				<form name="frmSolicitations0" id="frmSolicitations0">
					<div id="err_Solicitations_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								Effective 
								<select name="SolicitationsEffectiveDate_0_Month" id="SolicitationsEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
									<option value="">Month</option>
									<cfloop from="1" to="12" index="thisMonth">
										<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
									</cfloop>
								</select>
								<cfset yearStart = Year(now()) - 5>
								<cfset yearEnd = yearStart + 20>
								<select name="SolicitationsEffectiveDate_0_Year" id="SolicitationsEffectiveDate_0_Year">
									<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
										<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
									</cfloop>
								</select>
							</li>
							<li>
								<input type="checkbox" name="SolicitationsInContract_0" id="SolicitationsInContract_0" value="1"> 
								<label for="SolicitationsInContract_0">Fees outlined here are in the current contract.</label>
							</li>
							<li class="SolicitationsBillRow_0">
								<input type="text" id="SolicitationsMonthlyFee_0" name="SolicitationsMonthlyFee_0" value="#dollarFormat(0)#" class="SolicitationsMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="SolicitationsNoFee_0" name="SolicitationsNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'Solicitations')"> <label for="SolicitationsNoFee_0">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_Solicitations_0" name="btn_Solicitations_0" class="btn btn-sm btn-primary " onclick="validateAndSaveSolicitationsInfo(0);">Save Schedule</button>
							<span></span>
						</div>
					
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="APIAccess">API Access</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			We run a monthly automated job that considers the fee schedules below.<br/>
			Sites with API Access enabled who have API activity as well as any site with API Access enabled that has defined monthly fees in these schedules will be included in billing.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgAPIAccess">
				<cfset local.thisTabName = "APIAccess#local.strDepoTLA.qryOrgAPIAccess.scheduleID#">
				<cfset local.thisTabID = "APIAccess#local.strDepoTLA.qryOrgAPIAccess.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgAPIAccess.effectiveDate eq local.strDepoTLA.qryDepoTLA.APIAccessEffectiveDate OR (local.strDepoTLA.qryDepoTLA.APIAccessEffectiveDate eq "" and local.strDepoTLA.qryOrgAPIAccess.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgAPIAccess.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "APIAccessX">
			<cfset local.thisTabID = "APIAccessXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgAPIAccess.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
				   + Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgAPIAccess">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgAPIAccess.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgAPIAccess.effectiveDate eq local.strDepoTLA.qryDepoTLA.APIAccessEffectiveDate OR (local.strDepoTLA.qryDepoTLA.APIAccessEffectiveDate eq "" and local.strDepoTLA.qryOrgAPIAccess.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-APIAccess#local.strDepoTLA.qryOrgAPIAccess.scheduleID#Tab" role="tabpanel" aria-labelledby="APIAccess#local.strDepoTLA.qryOrgAPIAccess.scheduleID#">
					<form name="frmAPIAccess#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" id="frmAPIAccess#local.strDepoTLA.qryOrgAPIAccess.scheduleID#">
						<div id="err_APIAccess_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgAPIAccess.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgAPIAccess.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="APIAccessEffectiveDate_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#_Month" id="APIAccessEffectiveDate_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgAPIAccess.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgAPIAccess.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="APIAccessEffectiveDate_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#_Year" id="APIAccessEffectiveDate_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgAPIAccess.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelAPIAccessSched_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" id="btnDelAPIAccessSched_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" class="btn btn-danger"  onclick="deleteTSBillingSchedule('APIAccess',#local.strDepoTLA.qryOrgAPIAccess.scheduleID#,'mcbilling');" style="margin-left:auto;">
											<span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgAPIAccess.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="APIAccessInContract_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" id="APIAccessInContract_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgAPIAccess.inContract> checked</cfif>> 
								<label for="APIAccessInContract_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="APIAccessBillRow_#local.strDepoTLA.qryOrgAPIAccess.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="APIAccessMonthlyFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" name="APIAccessMonthlyFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgAPIAccess.monthlyFee))#" class="APIAccessMoneyField_#local.strDepoTLA.qryOrgAPIAccess.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>							
								Fee includes <input type="text" id="APIAccessNoofCallIncFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" name="APIAccessNoofCallIncFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" value="#val(local.strDepoTLA.qryOrgAPIAccess.noofCallIncFee)#" class="APIAccessCountField_#local.strDepoTLA.qryOrgAPIAccess.scheduleID# form-control-sm no-border-radius" size="8" maxlength="6"> of API calls
							</li>
							<li>
								<input type="text" id="APIAccessOverageFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" name="APIAccessOverageFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" value="$#numberFormat(val(local.strDepoTLA.qryOrgAPIAccess.overageFee),'9.999999')#" class="APIAccessMoneyField_#local.strDepoTLA.qryOrgAPIAccess.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20"> every <input type="text" id="APIAccessNoofCallsInOverageFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" name="APIAccessNoofCallsInOverageFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" value="#val(local.strDepoTLA.qryOrgAPIAccess.noofCallsInOverageFee)#" class="APIAccessCountField_#local.strDepoTLA.qryOrgAPIAccess.scheduleID# form-control-sm no-border-radius" size="8" maxlength="6"> calls for the previous month the billing is run.
							</li>
							<li>
								<input type="checkbox" id="APIAccessNoFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" name="APIAccessNoFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgAPIAccess.noFee> checked</cfif> onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgAPIAccess.scheduleID#','APIAccess')"> <label for="APIAccessNoFee_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button"  id="btn_APIAccess_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" name="btn_APIAccess_#local.strDepoTLA.qryOrgAPIAccess.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveAPIAccessInfo(#local.strDepoTLA.qryOrgAPIAccess.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-APIAccessXTab" role="tabpanel" aria-labelledby="APIAccessX">
				<form name="frmAPIAccess0" id="frmAPIAccess0">
					<div id="err_APIAccess_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								Effective 
								<select name="APIAccessEffectiveDate_0_Month" id="APIAccessEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
									<option value="">Month</option>
									<cfloop from="1" to="12" index="thisMonth">
										<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
									</cfloop>
								</select>
								<cfset yearStart = Year(now()) - 5>
								<cfset yearEnd = yearStart + 20>
								<select name="APIAccessEffectiveDate_0_Year" id="APIAccessEffectiveDate_0_Year">
									<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
										<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
									</cfloop>
								</select>
							</li>
							<li>
								<input type="checkbox" name="APIAccessInContract_0" id="APIAccessInContract_0" value="1"> 
								<label for="APIAccessInContract_0">Fees outlined here are in the current contract.</label>
							</li>
							<li class="APIAccessBillRow_0">
								<input type="text" id="APIAccessMonthlyFee_0" name="APIAccessMonthlyFee_0" value="#dollarFormat(0)#" class="APIAccessMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>							
								Fee includes <input type="text" id="APIAccessNoofCallIncFee_0" name="APIAccessNoofCallIncFee_0" value="0" class="APIAccessCountField_0 form-control-sm no-border-radius" size="8" maxlength="6"> of API calls
							</li>
							<li>							
								<input type="text" id="APIAccessOverageFee_0" name="APIAccessOverageFee_0" value="#dollarFormat(0)#" class="APIAccessMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20"> every <input type="text" id="APIAccessNoofCallsInOverageFee_0" name="APIAccessNoofCallsInOverageFee_0" value="0" class="APIAccessCountField_0 form-control-sm no-border-radius" size="8" maxlength="6"> calls for the previous month the billing is run.
							</li>
							<li>
								<input type="checkbox" id="APIAccessNoFee_0" name="APIAccessNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'APIAccess')"> <label for="APIAccessNoFee_0">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button"  id="btn_APIAccess_0" name="btn_APIAccess_0" class="btn btn-sm btn-primary " onclick="validateAndSaveAPIAccessInfo(0);">Save Schedule</button>
							<span></span>
						</div>
						
					
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="emailblast">Email Blast</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			We run a monthly automated job that considers the fee schedules below.<br/>
			All sites who have email activity as well as any site that has defined monthly fees in these schedules will be included in billing.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgEmailBlast">
				<cfset local.thisTabName = "EmailBlast#local.strDepoTLA.qryOrgEmailBlast.scheduleID#">
				<cfset local.thisTabID = "EmailBlast#local.strDepoTLA.qryOrgEmailBlast.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgEmailBlast.effectiveDate eq local.strDepoTLA.qryDepoTLA.EmailBlastEffectiveDate OR (local.strDepoTLA.qryDepoTLA.EmailBlastEffectiveDate eq "" and local.strDepoTLA.qryOrgEmailBlast.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgEmailBlast.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "EmailBlastX">
			<cfset local.thisTabID = "EmailBlastXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgEmailBlast.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
				   + Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgEmailBlast">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgEmailBlast.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgEmailBlast.effectiveDate eq local.strDepoTLA.qryDepoTLA.EmailBlastEffectiveDate OR (local.strDepoTLA.qryDepoTLA.EmailBlastEffectiveDate eq "" and local.strDepoTLA.qryOrgEmailBlast.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-EmailBlast#local.strDepoTLA.qryOrgEmailBlast.scheduleID#Tab" role="tabpanel" aria-labelledby="EmailBlast#local.strDepoTLA.qryOrgEmailBlast.scheduleID#">
					<form name="frmEmailBlast#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" id="frmEmailBlast#local.strDepoTLA.qryOrgEmailBlast.scheduleID#">
						<div id="err_EmailBlast_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<table style="width:100%">
							<tr>
								<td>
									<cfif local.strDepoTLA.qryOrgEmailBlast.effectiveDate GT now()>
										<div style="display:flex;margin-bottom:10px;">
											<span>
												Effective 
												<select name="EmailBlastEffectiveDate_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#_Month" id="EmailBlastEffectiveDate_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
													<option value="">Month</option>
													<cfloop from="1" to="12" index="thisMonth">
														<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgEmailBlast.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
													</cfloop>
												</select>
												<cfset yearStart = Year(local.strDepoTLA.qryOrgEmailBlast.effectiveDate) - 5>
												<cfset yearEnd = yearStart + 20>
												<select name="EmailBlastEffectiveDate_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#_Year" id="EmailBlastEffectiveDate_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#_Year">
													<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
														<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgEmailBlast.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
													</cfloop>
												</select>
											</span>
											<button type="button" name="btnDelEmailBlastSched_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" id="btnDelEmailBlastSched_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#"  class="btn btn-danger" onclick="deleteTSBillingSchedule('EmailBlast',#local.strDepoTLA.qryOrgEmailBlast.scheduleID#,'mcbilling');" style="margin-left:auto;">
												 <span style="vertical-align:top;">Delete Schedule</span>
											</button>
										</div>
									<cfelse>
										<b>Effective #dateformat(local.strDepoTLA.qryOrgEmailBlast.effectiveDate,"mmm yyyy")#</b>
									</cfif>
								</td>
							</tr>
							<tr>
								<td>
									<input type="checkbox" name="EmailBlastInContract_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" id="EmailBlastInContract_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgEmailBlast.inContract> checked</cfif>> 
									<label for="EmailBlastInContract_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#">Fees outlined here are in the current contract.</label>
								</td>
							</tr>
							<tbody id="tbody_EmailBlast_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" <cfif local.strDepoTLA.qryOrgEmailBlast.noFee is 1>class="d-none"</cfif>>
								<tr>
									<td>Monthly Fee: <input type="Text" id="EmailBlastMonthlyFee_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" name="EmailBlastMonthlyFee_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgEmailBlast.monthlyFee))#" class="form-control-sm no-border-radius"   size="10" maxlength="21" onBlur="this.value=formatCurrency(this.value);"> for the month billing is run</td>
								</tr>
								<tr>
									<td>Fee Includes ## Emails: <input type="Text" id="EmailBlastMonthlyComp_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" name="EmailBlastMonthlyComp_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" value="#val(local.strDepoTLA.qryOrgEmailBlast.monthlyComp)#" class="form-control-sm no-border-radius"  size="8" maxlength="6"></td>
								</tr>
								<tr>
									<td>Fee Includes ## Emails Per Admin: <input type="Text" id="EmailBlastMonthlyPerAdminComp_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" name="EmailBlastMonthlyPerAdminComp_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" value="#val(local.strDepoTLA.qryOrgEmailBlast.monthlyPerAdminComp)#" class="form-control-sm no-border-radius"  size="8" maxlength="6"></td>
								</tr>
								<tr>
									<td>Overage Fee Per Email: <input type="Text" id="EmailBlastBillingRate_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" name="EmailBlastBillingRate_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" value="$#numberFormat(val(local.strDepoTLA.qryOrgEmailBlast.billingRate),'9.999999')#"  class="form-control-sm no-border-radius" size="10" maxlength="21"> for the previous month the month billing is run</td>
								</tr>
								<tr>
									<td>
										<input type="checkbox" name="EmailBlastIncOtherApps_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" id="EmailBlastIncOtherApps_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgEmailBlast.includeOtherApps> checked</cfif>> 
										<label for="EmailBlastIncOtherApps_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#">Include Emailing Groups, Announcements, and Publications.</label>
									</td>
								</tr>
							</tbody>
							<tr>
								<td><input type="checkbox" id="EmailBlastNoFee_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" name="EmailBlastNoFee_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgEmailBlast.noFee> checked</cfif>  onClick="clearEmailBlastSchedule('#local.strDepoTLA.qryOrgEmailBlast.scheduleID#')">
									Contract specifies no monthly fee.
								</td>
							</tr>
						</table>
						<div class="col">
							<button type="button" id="btn_EmailBlast_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" name="btn_EmailBlast_#local.strDepoTLA.qryOrgEmailBlast.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveEmailBlastInfo(#local.strDepoTLA.qryOrgEmailBlast.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-EmailBlastXTab" role="tabpanel" aria-labelledby="EmailBlastX">
				<form name="frmEmailBlast0" id="frmEmailBlast0">
					<div id="err_EmailBlast_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<table class="tblInside">
						<tr>
							<td>
								Effective 
								<select name="EmailBlastEffectiveDate_0_Month" id="EmailBlastEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
									<option value="">Month</option>
									<cfloop from="1" to="12" index="thisMonth">
										<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
									</cfloop>
								</select>
								<cfset yearStart = Year(now()) - 5>
								<cfset yearEnd = yearStart + 20>
								<select name="EmailBlastEffectiveDate_0_Year" id="EmailBlastEffectiveDate_0_Year">
									<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
										<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr>
							<td>
								<input type="checkbox" name="EmailBlastInContract_0" id="EmailBlastInContract_0" value="1"> 
								<label for="EmailBlastInContract_0">Fees outlined here are in the current contract.</label>
							</td>
						</tr>
						<tbody id="tbody_EmailBlast_0">
							<tr>
								<td>Monthly Fee: <input type="Text" id="EmailBlastMonthlyFee_0" name="EmailBlastMonthlyFee_0" value="#dollarFormat(0)#" class="form-control-sm no-border-radius"  size="10" maxlength="21" onBlur="this.value=formatCurrency(this.value);"> for the month billing is run</td>
							</tr>
							<tr>
								<td>Fee Includes ## Emails: <input type="Text" id="EmailBlastMonthlyComp_0" name="EmailBlastMonthlyComp_0" value="0"  class="form-control-sm no-border-radius" size="8" maxlength="6"></td>
							</tr>
							<tr>
								<td>Fee Includes ## Emails Per Admin: <input type="Text" id="EmailBlastMonthlyPerAdminComp_0" name="EmailBlastMonthlyPerAdminComp_0" value="0" class="form-control-sm no-border-radius"  size="8" maxlength="6"></td>
							</tr>
							<tr>
								<td>Overage Fee Per Email: <input type="Text" id="EmailBlastBillingRate_0" name="EmailBlastBillingRate_0" value="#dollarFormat(0)#" class="form-control-sm no-border-radius"  size="10" maxlength="21"> for the previous month the month billing is run</td>
							</tr>
							<tr>
								<td>
									<input type="checkbox" name="EmailBlastIncOtherApps_0" id="EmailBlastIncOtherApps_0" value="1">
									<label for="EmailBlastIncOtherApps_0">Include Emailing Groups, Announcements, and Publications.</label>
								</td>
							</tr>
						</tbody>
						<tr>
							<td><input type="checkbox" id="EmailBlastNoFee_0" name="EmailBlastNoFee_0" value="1" onClick="clearEmailBlastSchedule(0)">
								Contract specifies no monthly fee.
							</td>
						</tr>
					</table>
					<div class="col">
						<button type="button" id="btn_EmailBlast_0" name="btn_EmailBlast_0" class="btn btn-sm btn-primary "  onclick="validateAndSaveEmailBlastInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="districtmatching">District Matching</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			We run a monthly automated job that considers the fee schedules below.<br/>
			Only sites who have district matching activity will be included in billing.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgDistrictMatching">
				<cfset local.thisTabName = "District#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#">
				<cfset local.thisTabID = "District#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgDistrictMatching.effectiveDate eq local.strDepoTLA.qryDepoTLA.DistrictEffectiveDate OR (local.strDepoTLA.qryDepoTLA.DistrictEffectiveDate eq "" and local.strDepoTLA.qryOrgDistrictMatching.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgDistrictMatching.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "DistrictX">
			<cfset local.thisTabID = "DistrictXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgDistrictMatching.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
					+ Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgDistrictMatching">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgDistrictMatching.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgDistrictMatching.effectiveDate eq local.strDepoTLA.qryDepoTLA.DistrictEffectiveDate OR (local.strDepoTLA.qryDepoTLA.DistrictEffectiveDate eq "" and local.strDepoTLA.qryOrgDistrictMatching.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-District#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#Tab" role="tabpanel" aria-labelledby="District#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#">
					<form name="frmDistrict#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" id="frmDistrict#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#">
						<div id="err_District_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<table style="width:100%;">
							<tr>
								<td>
									<cfif local.strDepoTLA.qryOrgDistrictMatching.effectiveDate GT now()>
										<div style="display:flex;margin-bottom:10px;">
											<span>
												Effective 
												<select name="DistrictEffectiveDate_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#_Month" id="DistrictEffectiveDate_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
													<option value="">Month</option>
													<cfloop from="1" to="12" index="thisMonth">
														<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgDistrictMatching.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
													</cfloop>
												</select>
												<cfset yearStart = Year(local.strDepoTLA.qryOrgDistrictMatching.effectiveDate) - 5>
												<cfset yearEnd = yearStart + 20>
												<select name="DistrictEffectiveDate_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#_Year" id="DistrictEffectiveDate_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#_Year">
													<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
														<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgDistrictMatching.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
													</cfloop>
												</select>
											</span>
											<button type="button" name="btnDelDistrictMatchingSched_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" id="btnDelDistrictMatchingSched_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('DistrictMatching',#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#,'mcbilling');" style="margin-left:auto;">
												<span style="vertical-align:top;">Delete Schedule</span>
											</button>
										</div>
									<cfelse>
										<b>Effective #dateformat(local.strDepoTLA.qryOrgDistrictMatching.effectiveDate,"mmm yyyy")#</b>
									</cfif>
								</td>
							</tr>
							<tr>
								<td>
									<input type="checkbox" name="DistrictMatchingInContract_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" id="DistrictMatchingInContract_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgDistrictMatching.inContract> checked</cfif>> 
									<label for="DistrictMatchingInContract_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#">Fees outlined here are in the current contract.</label>
								</td>
							</tr>
							<tbody id="tbody_District_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" <cfif local.strDepoTLA.qryOrgDistrictMatching.noFee is 1>class="d-none"</cfif>>
								<tr>
									<td>Fee Per Update: <input type="Text" id="DistrictBillingRate_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" name="DistrictBillingRate_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" value="$#numberFormat(val(local.strDepoTLA.qryOrgDistrictMatching.billingRate),'9.999999')#" class="form-control-sm no-border-radius" size="10" maxlength="21"> for the previous month the month billing is run</td>
								</tr>
							</tbody>
							<tr>
								<td><input type="checkbox" id="DistrictNoFee_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" name="DistrictNoFee_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgDistrictMatching.noFee> checked</cfif> onClick="clearDistrictSchedule('#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#')">
									Contract specifies no monthly fee.
								</td>
							</tr>
						</table>
						<div class="col">
							<button type="button" id="btn_District_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" name="btn_District_#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveDistrictInfo(#local.strDepoTLA.qryOrgDistrictMatching.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-DistrictXTab" role="tabpanel" aria-labelledby="DistrictX">
				<form name="frmDistrict0" id="frmDistrict0">
					<div id="err_District_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<table class="tblInside">
						<tr>
							<td>
								Effective 
								<select name="DistrictEffectiveDate_0_Month" id="DistrictEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
									<option value="">Month</option>
									<cfloop from="1" to="12" index="thisMonth">
										<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
									</cfloop>
								</select>
								<cfset yearStart = Year(now()) - 5>
								<cfset yearEnd = yearStart + 20>
								<select name="DistrictEffectiveDate_0_Year" id="DistrictEffectiveDate_0_Year">
									<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
										<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr>
							<td>
								<input type="checkbox" name="DistrictMatchingInContract_0" id="DistrictMatchingInContract_0" value="1"> 
								<label for="DistrictMatchingInContract_0">Fees outlined here are in the current contract.</label>
							</td>
						</tr>
						<tbody id="tbody_District_0">
							<tr>
								<td>Fee Per Update: <input type="Text" id="DistrictBillingRate_0" name="DistrictBillingRate_0" value="#dollarFormat(0)#" class="form-control-sm no-border-radius" size="10" maxlength="21"> for the previous month the month billing is run</td>
							</tr>
						</tbody>
						<tr>
							<td>
								<input type="checkbox" id="DistrictNoFee_0" name="DistrictNoFee_0" value="1" onClick="clearDistrictSchedule(0)">
								Contract specifies no monthly fee.
							</td>
						</tr>
					</table>
					<div class="col">
						<button type="button" id="btn_District_0" name="btn_District_0" class="btn btn-sm btn-primary "  onclick="validateAndSaveDistrictInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="addressupdate">Address Updates</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			We run a monthly automated job that considers the fee schedules below.<br/>
			All sites who have address updating activity as well as any site that has defined monthly fees in these schedules will be included in billing.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgAddressUpdate">
				<cfset local.thisTabName = "AddressUpdate#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#">
				<cfset local.thisTabID = "AddressUpdate#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgAddressUpdate.effectiveDate eq local.strDepoTLA.qryDepoTLA.AddressUpdateEffectiveDate OR (local.strDepoTLA.qryDepoTLA.AddressUpdateEffectiveDate eq "" and local.strDepoTLA.qryOrgAddressUpdate.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgAddressUpdate.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "AddressUpdateX">
			<cfset local.thisTabID = "AddressUpdateXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgAddressUpdate.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
					+ Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgAddressUpdate">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgAddressUpdate.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgAddressUpdate.effectiveDate eq local.strDepoTLA.qryDepoTLA.AddressUpdateEffectiveDate OR (local.strDepoTLA.qryDepoTLA.AddressUpdateEffectiveDate eq "" and local.strDepoTLA.qryOrgAddressUpdate.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-AddressUpdate#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#Tab" role="tabpanel" aria-labelledby="AddressUpdate#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#">
					<form name="frmAddressUpdate#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" id="frmAddressUpdate#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#">
						<div id="err_AddressUpdate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<table style="width:100%;">
							<tr>
								<td>
									<cfif local.strDepoTLA.qryOrgAddressUpdate.effectiveDate GT now()>
										<div style="display:flex;margin-bottom:10px;">
											<span>
												Effective 
												<select name="AddressUpdateEffectiveDate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#_Month" id="AddressUpdateEffectiveDate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
													<option value="">Month</option>
													<cfloop from="1" to="12" index="thisMonth">
														<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgAddressUpdate.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
													</cfloop>
												</select>
												<cfset yearStart = Year(local.strDepoTLA.qryOrgAddressUpdate.effectiveDate) - 5>
												<cfset yearEnd = yearStart + 20>
												<select name="AddressUpdateEffectiveDate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#_Year" id="AddressUpdateEffectiveDate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#_Year">
													<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
														<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgAddressUpdate.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
													</cfloop>
												</select>
											</span>
											<button type="button" name="btnDelAddressUpdateSched_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" id="btnDelAddressUpdateSched_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('AddressUpdate',#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#,'mcbilling');" style="margin-left:auto;">
												<span style="vertical-align:top;">Delete Schedule</span>
											</button>
										</div>
									<cfelse>
										<b>Effective #dateformat(local.strDepoTLA.qryOrgAddressUpdate.effectiveDate,"mmm yyyy")#</b>
									</cfif>
								</td>
							</tr>
							<tr>
								<td>
									<input type="checkbox" name="AddressUpdateInContract_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" id="AddressUpdateInContract_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgAddressUpdate.inContract> checked</cfif>> 
									<label for="AddressUpdateInContract_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#">Fees outlined here are in the current contract.</label>
								</td>
							</tr>
							<tbody id="tbody_AddressUpdate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" <cfif local.strDepoTLA.qryOrgAddressUpdate.noFee is 1>class="d-none"</cfif>>
								<tr>
									<td>Monthly Fee: <input type="Text" id="AddressUpdateMonthlyFee_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" name="AddressUpdateMonthlyFee_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgAddressUpdate.monthlyFee))#" class="form-control-sm no-border-radius" size="10" maxlength="21" onBlur="this.value=formatCurrency(this.value);"> for the month billing is run</td>
								</tr>
								<tr>
									<td>Fee Per Update: <input type="Text" id="AddressUpdateBillingRate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" name="AddressUpdateBillingRate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" value="$#numberFormat(val(local.strDepoTLA.qryOrgAddressUpdate.billingRate),'9.999999')#" class="form-control-sm no-border-radius" size="10" maxlength="21"> for the previous month the month billing is run</td>
								</tr>
							</tbody>
							<tr>
								<td><input type="checkbox" id="AddressUpdateNoFee_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" name="AddressUpdateNoFee_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgAddressUpdate.noFee> checked</cfif>  onClick="clearAddressUpdateSchedule('#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#')">
									Contract specifies no monthly fee.
								</td>
							</tr>
						</table>
						<div class="col">
							<button type="button" id="btn_AddressUpdate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" name="btn_AddressUpdate_#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveAddressUpdateInfo(#local.strDepoTLA.qryOrgAddressUpdate.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-AddressUpdateXTab" role="tabpanel" aria-labelledby="AddressUpdateX">
				<form name="frmAddressUpdate0" id="frmAddressUpdate0">
					<div id="err_AddressUpdate_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<table class="tblInside">
						<tr>
							<td>
								Effective 
								<select name="AddressUpdateEffectiveDate_0_Month" id="AddressUpdateEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
									<option value="">Month</option>
									<cfloop from="1" to="12" index="thisMonth">
										<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
									</cfloop>
								</select>
								<cfset yearStart = Year(now()) - 5>
								<cfset yearEnd = yearStart + 20>
								<select name="AddressUpdateEffectiveDate_0_Year" id="AddressUpdateEffectiveDate_0_Year">
									<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
										<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
									</cfloop>
								</select>
							</td>
						</tr>
						<tr>
							<td>
								<input type="checkbox" name="AddressUpdateInContract_0" id="AddressUpdateInContract_0" value="1"> 
								<label for="AddressUpdateInContract_0">Fees outlined here are in the current contract.</label>
							</td>
						</tr>
						<tbody id="tbody_AddressUpdate_0">
							<tr>
								<td>Monthly Fee: <input type="Text" id="AddressUpdateMonthlyFee_0" name="AddressUpdateMonthlyFee_0" value="#dollarFormat(0)#" class="form-control-sm no-border-radius" size="10" maxlength="21" onBlur="this.value=formatCurrency(this.value);"> for the month billing is run</td>
							</tr>
							<tr>
								<td>Fee Per Update: <input type="Text" id="AddressUpdateBillingRate_0" name="AddressUpdateBillingRate_0" value="#dollarFormat(0)#" class="form-control-sm no-border-radius" size="10" maxlength="21"> for the previous month the month billing is run</td>
							</tr>
						</tbody>
						<tr>
							<td><input type="checkbox" id="AddressUpdateNoFee_0" name="AddressUpdateNoFee_0" value="1" onClick="clearAddressUpdateSchedule(0)">
								Contract specifies no monthly fee.
							</td>
						</tr>
					</table>
					<div class="col">
						<button type="button" id="btn_AddressUpdate_0" name="btn_AddressUpdate_0" class="btn btn-sm btn-primary "  onclick="validateAndSaveAddressUpdateInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="dedicatedservicemgr">Dedicated Service Manager</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgDedicatedServiceMgr">
				<cfset local.thisTabName = "DedicatedServiceMgr#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#">
				<cfset local.thisTabID = "DedicatedServiceMgr#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgDedicatedServiceMgr.effectiveDate eq local.strDepoTLA.qryDepoTLA.DedicatedServiceMgrEffectiveDate OR (local.strDepoTLA.qryDepoTLA.DedicatedServiceMgrEffectiveDate eq "" and local.strDepoTLA.qryOrgDedicatedServiceMgr.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgDedicatedServiceMgr.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "DedicatedServiceMgrX">
			<cfset local.thisTabID = "DedicatedServiceMgrXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgDedicatedServiceMgr.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
					+ Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgDedicatedServiceMgr">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgDedicatedServiceMgr.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgDedicatedServiceMgr.effectiveDate eq local.strDepoTLA.qryDepoTLA.DedicatedServiceMgrEffectiveDate OR (local.strDepoTLA.qryDepoTLA.DedicatedServiceMgrEffectiveDate eq "" and local.strDepoTLA.qryOrgDedicatedServiceMgr.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-DedicatedServiceMgr#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#Tab" role="tabpanel" aria-labelledby="DedicatedServiceMgr#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#">
					<form name="frmDedicatedServiceMgr#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" id="frmDedicatedServiceMgr#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#">
						<div id="err_DedicatedServiceMgr_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgDedicatedServiceMgr.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgDedicatedServiceMgr.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="DedicatedServiceMgrEffectiveDate_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#_Month" id="DedicatedServiceMgrEffectiveDate_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgDedicatedServiceMgr.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgDedicatedServiceMgr.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="DedicatedServiceMgrEffectiveDate_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#_Year" id="DedicatedServiceMgrEffectiveDate_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgDedicatedServiceMgr.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelDedicatedServiceMgrSched_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" id="btnDelDedicatedServiceMgrSched_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('DedicatedServiceMgr',#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#,'mcbilling');" style="margin-left:auto;">
											<span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgDedicatedServiceMgr.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="DedicatedServiceMgrInContract_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" id="DedicatedServiceMgrInContract_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgDedicatedServiceMgr.inContract> checked</cfif>> 
								<label for="DedicatedServiceMgrInContract_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="DedicatedServiceMgrBillRow_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="DedicatedServiceMgrMonthlyFee_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" name="DedicatedServiceMgrMonthlyFee_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgDedicatedServiceMgr.monthlyFee))#" class="DedicatedServiceMgrMoneyField_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="DedicatedServiceMgrNoFee_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" name="DedicatedServiceMgrNoFee_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgDedicatedServiceMgr.noFee> checked</cfif>   onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#','DedicatedServiceMgr')"> <label for="DedicatedServiceMgrNoFee_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_DedicatedServiceMgr_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" name="btn_DedicatedServiceMgr_#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveDedicatedServiceMgrInfo(#local.strDepoTLA.qryOrgDedicatedServiceMgr.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-DedicatedServiceMgrXTab" role="tabpanel" aria-labelledby="DedicatedServiceMgrX">
				<form name="frmDedicatedServiceMgr0" id="frmDedicatedServiceMgr0">
					<div id="err_DedicatedServiceMgr_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Effective 
							<select name="DedicatedServiceMgrEffectiveDate_0_Month" id="DedicatedServiceMgrEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="DedicatedServiceMgrEffectiveDate_0_Year" id="DedicatedServiceMgrEffectiveDate_0_Year">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="DedicatedServiceMgrInContract_0" id="DedicatedServiceMgrInContract_0" value="1"> 
							<label for="DedicatedServiceMgrInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="DedicatedServiceMgrBillRow_0">
							<input type="text" id="DedicatedServiceMgrMonthlyFee_0" name="DedicatedServiceMgrMonthlyFee_0" value="#dollarFormat(0)#" class="DedicatedServiceMgrMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li>
							<input type="checkbox" id="DedicatedServiceMgrNoFee_0" name="DedicatedServiceMgrNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'DedicatedServiceMgr')"> <label for="DedicatedServiceMgrNoFee_0">Contract specifies no monthly fee.</label>
						</li>
					</ul>
					<div class="col">
						<button type="button" id="btn_DedicatedServiceMgr_0" name="btn_DedicatedServiceMgr_0" class="btn btn-sm btn-primary "  onclick="validateAndSaveDedicatedServiceMgrInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="emailhosting">Email Hosting</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgEmailHosting">
				<cfset local.thisTabName = "EmailHosting#local.strDepoTLA.qryOrgEmailHosting.scheduleID#">
				<cfset local.thisTabID = "EmailHosting#local.strDepoTLA.qryOrgEmailHosting.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgEmailHosting.effectiveDate eq local.strDepoTLA.qryDepoTLA.EmailHostingEffectiveDate OR (local.strDepoTLA.qryDepoTLA.EmailHostingEffectiveDate eq "" and local.strDepoTLA.qryOrgEmailHosting.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgEmailHosting.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "EmailHostingX">
			<cfset local.thisTabID = "EmailHostingXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgEmailHosting.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
					+ Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgEmailHosting">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgEmailHosting.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgEmailHosting.effectiveDate eq local.strDepoTLA.qryDepoTLA.EmailHostingEffectiveDate OR (local.strDepoTLA.qryDepoTLA.EmailHostingEffectiveDate eq "" and local.strDepoTLA.qryOrgEmailHosting.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-EmailHosting#local.strDepoTLA.qryOrgEmailHosting.scheduleID#Tab" role="tabpanel" aria-labelledby="EmailHosting#local.strDepoTLA.qryOrgEmailHosting.scheduleID#">
					<form name="frmEmailHosting#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" id="frmEmailHosting#local.strDepoTLA.qryOrgEmailHosting.scheduleID#">
						<div id="err_EmailHosting_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgEmailHosting.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgEmailHosting.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="EmailHostingEffectiveDate_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#_Month" id="EmailHostingEffectiveDate_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgEmailHosting.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgEmailHosting.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="EmailHostingEffectiveDate_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#_Year" id="EmailHostingEffectiveDate_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgEmailHosting.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelEmailHostingSched_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" id="btnDelEmailHostingSched_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('EmailHosting',#local.strDepoTLA.qryOrgEmailHosting.scheduleID#,'mcbilling');" style="margin-left:auto;">
											<span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgEmailHosting.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="EmailHostingInContract_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" id="EmailHostingInContract_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgEmailHosting.inContract> checked</cfif>> 
								<label for="EmailHostingInContract_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="EmailHostingBillRow_#local.strDepoTLA.qryOrgEmailHosting.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="EmailHostingMonthlyFee_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" name="EmailHostingMonthlyFee_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgEmailHosting.monthlyFee))#" class="EmailHostingMoneyField_#local.strDepoTLA.qryOrgEmailHosting.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="EmailHostingNoFee_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" name="EmailHostingNoFee_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgEmailHosting.noFee> checked</cfif>  onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgEmailHosting.scheduleID#','EmailHosting')"> <label for="EmailHostingNoFee_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_EmailHosting_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" name="btn_EmailHosting_#local.strDepoTLA.qryOrgEmailHosting.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveEmailHostingInfo(#local.strDepoTLA.qryOrgEmailHosting.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-EmailHostingXTab" role="tabpanel" aria-labelledby="EmailHostingX">
				<form name="frmEmailHosting0" id="frmEmailHosting0">
					<div id="err_EmailHosting_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Effective 
							<select name="EmailHostingEffectiveDate_0_Month" id="EmailHostingEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="EmailHostingEffectiveDate_0_Year" id="EmailHostingEffectiveDate_0_Year">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="EmailHostingInContract_0" id="EmailHostingInContract_0" value="1"> 
							<label for="EmailHostingInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="EmailHostingBillRow_0">
							<input type="text" id="EmailHostingMonthlyFee_0" name="EmailHostingMonthlyFee_0" value="#dollarFormat(0)#" class="EmailHostingMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li>
							<input type="checkbox" id="EmailHostingNoFee_0" name="EmailHostingNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'EmailHosting')"> <label for="EmailHostingNoFee_0">Contract specifies no monthly fee.</label>
						</li>
					</ul>
					<div class="col">
						<button type="button" id="btn_EmailHosting_0" name="btn_EmailHosting_0" class="btn btn-sm btn-primary "  onclick="validateAndSaveEmailHostingInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="privatelistserverdomain">Privatized List Server Domain</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgPrivateListServerDomain">
				<cfset local.thisTabName = "PrivateListServerDomain#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#">
				<cfset local.thisTabID = "PrivateListServerDomain#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgPrivateListServerDomain.effectiveDate eq local.strDepoTLA.qryDepoTLA.PrivateListServerDomainEffectiveDate OR (local.strDepoTLA.qryDepoTLA.PrivateListServerDomainEffectiveDate eq "" and local.strDepoTLA.qryOrgPrivateListServerDomain.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgPrivateListServerDomain.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "PrivateListServerDomainX">
			<cfset local.thisTabID = "PrivateListServerDomainXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgPrivateListServerDomain.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
					+ Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgPrivateListServerDomain">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgPrivateListServerDomain.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgPrivateListServerDomain.effectiveDate eq local.strDepoTLA.qryDepoTLA.PrivateListServerDomainEffectiveDate OR (local.strDepoTLA.qryDepoTLA.PrivateListServerDomainEffectiveDate eq "" and local.strDepoTLA.qryOrgPrivateListServerDomain.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-PrivateListServerDomain#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#Tab" role="tabpanel" aria-labelledby="PrivateListServerDomain#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#">
					<form name="frmPrivateListServerDomain#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" id="frmPrivateListServerDomain#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#">
						<div id="err_PrivateListServerDomain_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgPrivateListServerDomain.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgPrivateListServerDomain.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="PrivateListServerDomainEffectiveDate_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#_Month" id="PrivateListServerDomainEffectiveDate_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgPrivateListServerDomain.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgPrivateListServerDomain.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="PrivateListServerDomainEffectiveDate_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#_Year" id="PrivateListServerDomainEffectiveDate_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgPrivateListServerDomain.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelPrivateListServerDomainSched_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" id="btnDelPrivateListServerDomainSched_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('PrivateListServerDomain',#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#,'mcbilling');" style="margin-left:auto;">
											<span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgPrivateListServerDomain.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="PrivateListServerDomainInContract_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" id="PrivateListServerDomainInContract_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgPrivateListServerDomain.inContract> checked</cfif>> 
								<label for="PrivateListServerDomainInContract_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="PrivateListServerDomainBillRow_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="PrivateListServerDomainMonthlyFee_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" name="PrivateListServerDomainMonthlyFee_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgPrivateListServerDomain.monthlyFee))#" class="PrivateListServerDomainMoneyField_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="PrivateListServerDomainNoFee_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" name="PrivateListServerDomainNoFee_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgPrivateListServerDomain.noFee> checked</cfif>  onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#','PrivateListServerDomain')"> <label for="PrivateListServerDomainNoFee_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_PrivateListServerDomain_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" name="btn_PrivateListServerDomain_#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSavePrivateListServerDomainInfo(#local.strDepoTLA.qryOrgPrivateListServerDomain.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-PrivateListServerDomainXTab" role="tabpanel" aria-labelledby="PrivateListServerDomainX">
				<form name="frmPrivateListServerDomain0" id="frmPrivateListServerDomain0">
					<div id="err_PrivateListServerDomain_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Effective 
							<select name="PrivateListServerDomainEffectiveDate_0_Month" id="PrivateListServerDomainEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="PrivateListServerDomainEffectiveDate_0_Year" id="PrivateListServerDomainEffectiveDate_0_Year">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="PrivateListServerDomainInContract_0" id="PrivateListServerDomainInContract_0" value="1"> 
							<label for="PrivateListServerDomainInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="PrivateListServerDomainBillRow_0">
							<input type="text" id="PrivateListServerDomainMonthlyFee_0" name="PrivateListServerDomainMonthlyFee_0" value="#dollarFormat(0)#" class="PrivateListServerDomainMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li>
							<input type="checkbox" id="PrivateListServerDomainNoFee_0" name="PrivateListServerDomainNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'PrivateListServerDomain')"> <label for="PrivateListServerDomainNoFee_0">Contract specifies no monthly fee.</label>
						</li>
					</ul>
					<div class="col">
						<button type="button" id="btn_PrivateListServerDomain_0" name="btn_PrivateListServerDomain_0" class="btn btn-sm btn-primary "  onclick="validateAndSavePrivateListServerDomainInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>


<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="PrivateEmailSendingDomain">Privatized Email Sending Domain</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgPrivateEmailSendingDomain">
				<cfset local.thisTabName = "PrivateEmailSendingDomain#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#">
				<cfset local.thisTabID = "PrivateEmailSendingDomain#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgPrivateEmailSendingDomain.effectiveDate eq local.strDepoTLA.qryDepoTLA.PrivateEmailSendingDomainEffectiveDate OR (local.strDepoTLA.qryDepoTLA.PrivateEmailSendingDomainEffectiveDate eq "" and local.strDepoTLA.qryOrgPrivateEmailSendingDomain.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgPrivateEmailSendingDomain.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "PrivateEmailSendingDomainX">
			<cfset local.thisTabID = "PrivateEmailSendingDomainXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgPrivateEmailSendingDomain.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
					+ Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgPrivateEmailSendingDomain">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgPrivateEmailSendingDomain.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgPrivateEmailSendingDomain.effectiveDate eq local.strDepoTLA.qryDepoTLA.PrivateEmailSendingDomainEffectiveDate OR (local.strDepoTLA.qryDepoTLA.PrivateEmailSendingDomainEffectiveDate eq "" and local.strDepoTLA.qryOrgPrivateEmailSendingDomain.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-PrivateEmailSendingDomain#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#Tab" role="tabpanel" aria-labelledby="PrivateEmailSendingDomain#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#">
					<form name="frmPrivateEmailSendingDomain#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" id="frmPrivateEmailSendingDomain#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#">
						<div id="err_PrivateEmailSendingDomain_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgPrivateEmailSendingDomain.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgPrivateEmailSendingDomain.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="PrivateEmailSendingDomainEffectiveDate_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#_Month" id="PrivateEmailSendingDomainEffectiveDate_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgPrivateEmailSendingDomain.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgPrivateEmailSendingDomain.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="PrivateEmailSendingDomainEffectiveDate_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#_Year" id="PrivateEmailSendingDomainEffectiveDate_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgPrivateEmailSendingDomain.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelPrivateEmailSendingDomainSched_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" id="btnDelPrivateEmailSendingDomainSched_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('PrivateEmailSendingDomain',#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#,'mcbilling');" style="margin-left:auto;">
											<span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgPrivateEmailSendingDomain.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="PrivateEmailSendingDomainInContract_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" id="PrivateEmailSendingDomainInContract_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgPrivateEmailSendingDomain.inContract> checked</cfif>> 
								<label for="PrivateEmailSendingDomainInContract_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="PrivateEmailSendingDomainBillRow_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="PrivateEmailSendingDomainMonthlyFee_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" name="PrivateEmailSendingDomainMonthlyFee_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgPrivateEmailSendingDomain.monthlyFee))#" class="PrivateEmailSendingDomainMoneyField_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="PrivateEmailSendingDomainNoFee_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" name="PrivateEmailSendingDomainNoFee_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgPrivateEmailSendingDomain.noFee> checked</cfif>  onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#','PrivateEmailSendingDomain')"> <label for="PrivateEmailSendingDomainNoFee_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_PrivateEmailSendingDomain_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" name="btn_PrivateEmailSendingDomain_#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSavePrivateEmailSendingDomainInfo(#local.strDepoTLA.qryOrgPrivateEmailSendingDomain.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-PrivateEmailSendingDomainXTab" role="tabpanel" aria-labelledby="PrivateEmailSendingDomainX">
				<form name="frmPrivateEmailSendingDomain0" id="frmPrivateEmailSendingDomain0">
					<div id="err_PrivateEmailSendingDomain_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Effective 
							<select name="PrivateEmailSendingDomainEffectiveDate_0_Month" id="PrivateEmailSendingDomainEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="PrivateEmailSendingDomainEffectiveDate_0_Year" id="PrivateEmailSendingDomainEffectiveDate_0_Year">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="PrivateEmailSendingDomainInContract_0" id="PrivateEmailSendingDomainInContract_0" value="1"> 
							<label for="PrivateEmailSendingDomainInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="PrivateEmailSendingDomainBillRow_0">
							<input type="text" id="PrivateEmailSendingDomainMonthlyFee_0" name="PrivateEmailSendingDomainMonthlyFee_0" value="#dollarFormat(0)#" class="PrivateEmailSendingDomainMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li>
							<input type="checkbox" id="PrivateEmailSendingDomainNoFee_0" name="PrivateEmailSendingDomainNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'PrivateEmailSendingDomain')"> <label for="PrivateEmailSendingDomainNoFee_0">Contract specifies no monthly fee.</label>
						</li>
					</ul>
					<div class="col">
						<button type="button" id="btn_PrivateEmailSendingDomain_0" name="btn_PrivateEmailSendingDomain_0" class="btn btn-sm btn-primary "  onclick="validateAndSavePrivateEmailSendingDomainInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
 

<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="EntPlatformSecurity">Enterprise Platform Security</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			We run a monthly automated job that considers the fee schedules below.<br/>
			Only sites with defined schedules will be included in billing.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgEntPlatformSecurity">
				<cfset local.thisTabName = "EntPlatformSecurity#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#">
				<cfset local.thisTabID = "EntPlatformSecurity#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgEntPlatformSecurity.effectiveDate eq local.strDepoTLA.qryDepoTLA.EntPlatformSecurityEffectiveDate OR (local.strDepoTLA.qryDepoTLA.EntPlatformSecurityEffectiveDate eq "" and local.strDepoTLA.qryOrgEntPlatformSecurity.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgEntPlatformSecurity.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "EntPlatformSecurityX">
			<cfset local.thisTabID = "EntPlatformSecurityXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgEntPlatformSecurity.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
					+ Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgEntPlatformSecurity">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgEntPlatformSecurity.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgEntPlatformSecurity.effectiveDate eq local.strDepoTLA.qryDepoTLA.EntPlatformSecurityEffectiveDate OR (local.strDepoTLA.qryDepoTLA.EntPlatformSecurityEffectiveDate eq "" and local.strDepoTLA.qryOrgEntPlatformSecurity.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-EntPlatformSecurity#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#Tab" role="tabpanel" aria-labelledby="EntPlatformSecurity#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#">
					<form name="frmEntPlatformSecurity#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" id="frmEntPlatformSecurity#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#">
						<div id="err_EntPlatformSecurity_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgEntPlatformSecurity.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgEntPlatformSecurity.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="EntPlatformSecurityEffectiveDate_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#_Month" id="EntPlatformSecurityEffectiveDate_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgEntPlatformSecurity.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgEntPlatformSecurity.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="EntPlatformSecurityEffectiveDate_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#_Year" id="EntPlatformSecurityEffectiveDate_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgEntPlatformSecurity.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelEntPlatformSecuritySched_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" id="btnDelEntPlatformSecuritySched_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('EntPlatformSecurity',#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#,'mcbilling');" style="margin-left:auto;">
											 <span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgEntPlatformSecurity.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="EntPlatformSecurityInContract_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" id="EntPlatformSecurityInContract_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgEntPlatformSecurity.inContract> checked</cfif>> 
								<label for="EntPlatformSecurityInContract_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="EntPlatformSecurityBillRow_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="EntPlatformSecurityMonthlyFee_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" name="EntPlatformSecurityMonthlyFee_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgEntPlatformSecurity.monthlyFee))#" class="EntPlatformSecurityMoneyField_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>
								<input type="checkbox" id="EntPlatformSecurityNoFee_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" name="EntPlatformSecurityNoFee_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgEntPlatformSecurity.noFee> checked</cfif>  onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#','EntPlatformSecurity')"> <label for="EntPlatformSecurityNoFee_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_EntPlatformSecurity_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" name="btn_EntPlatformSecurity_#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveEntPlatformSecurityInfo(#local.strDepoTLA.qryOrgEntPlatformSecurity.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-EntPlatformSecurityXTab" role="tabpanel" aria-labelledby="EntPlatformSecurityX">
				<form name="frmEntPlatformSecurity0" id="frmEntPlatformSecurity0">
					<div id="err_EntPlatformSecurity_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Effective 
							<select name="EntPlatformSecurityEffectiveDate_0_Month" id="EntPlatformSecurityEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="EntPlatformSecurityEffectiveDate_0_Year" id="EntPlatformSecurityEffectiveDate_0_Year">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="EntPlatformSecurityInContract_0" id="EntPlatformSecurityInContract_0" value="1"> 
							<label for="EntPlatformSecurityInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="EntPlatformSecurityBillRow_0">
							<input type="text" id="EntPlatformSecurityMonthlyFee_0" name="EntPlatformSecurityMonthlyFee_0" value="#dollarFormat(50)#" class="EntPlatformSecurityMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li>
							<input type="checkbox" id="EntPlatformSecurityNoFee_0" name="EntPlatformSecurityNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'EntPlatformSecurity')"> <label for="EntPlatformSecurityNoFee_0">Contract specifies no monthly fee.</label>
						</li>
					</ul>
					<div class="col">
						<button type="button" id="btn_EntPlatformSecurity_0" name="btn_EntPlatformSecurity_0" class="btn btn-sm btn-primary "  onclick="validateAndSaveEntPlatformSecurityInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="BadgePrinting">Badge Printing</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			We run a monthly automated job that considers the fee schedules below.<br/>
			Sites with Badge Printing enabled who have badge activity as well as any site with Badge Printing enabled that has defined monthly fees in these schedules will be included in billing.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgBadgePrinting">
				<cfset local.thisTabName = "BadgePrinting#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#">
				<cfset local.thisTabID = "BadgePrinting#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgBadgePrinting.effectiveDate eq local.strDepoTLA.qryDepoTLA.BadgePrintingEffectiveDate OR (local.strDepoTLA.qryDepoTLA.BadgePrintingEffectiveDate eq "" and local.strDepoTLA.qryOrgBadgePrinting.currentrow is 1)> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgBadgePrinting.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "BadgePrintingX">
			<cfset local.thisTabID = "BadgePrintingXTab">
			<li class="nav-item">
				<a class="nav-link" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
					+ Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgBadgePrinting">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgBadgePrinting.effectiveDate GT now()>
				<div class="tab-pane fade <cfif local.strDepoTLA.qryOrgBadgePrinting.effectiveDate eq local.strDepoTLA.qryDepoTLA.BadgePrintingEffectiveDate OR (local.strDepoTLA.qryDepoTLA.BadgePrintingEffectiveDate eq "" and local.strDepoTLA.qryOrgBadgePrinting.currentrow is 1)>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-BadgePrinting#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#Tab" role="tabpanel" aria-labelledby="BadgePrinting#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#">
					<form name="frmBadgePrinting#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" id="frmBadgePrinting#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#">
						<div id="err_BadgePrinting_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<cfset local.extraBillRowClass = local.strDepoTLA.qryOrgBadgePrinting.noFee is 1 ? "d-none" : "">
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgBadgePrinting.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="BadgePrintingEffectiveDate_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#_Month" id="BadgePrintingEffectiveDate_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgBadgePrinting.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgBadgePrinting.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="BadgePrintingEffectiveDate_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#_Year" id="BadgePrintingEffectiveDate_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#_Year">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgBadgePrinting.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelBadgePrintingSched_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" id="btnDelBadgePrintingSched_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('BadgePrinting',#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#,'mcbilling');" style="margin-left:auto;">
											 <span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<b>Effective #dateformat(local.strDepoTLA.qryOrgBadgePrinting.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="BadgePrintingInContract_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" id="BadgePrintingInContract_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgBadgePrinting.inContract> checked</cfif>> 
								<label for="BadgePrintingInContract_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="BadgePrintingBillRow_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID# #local.extraBillRowClass#">
								<input type="text" id="BadgePrintingMonthlyFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" name="BadgePrintingMonthlyFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgBadgePrinting.monthlyFee))#" class="BadgePrintingMoneyField_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li>							
								Fee includes <input type="text" id="BadgePrintingNoofCallIncFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" name="BadgePrintingNoofCallIncFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" value="#val(local.strDepoTLA.qryOrgBadgePrinting.noofCallIncFee)#" class="BadgePrintingCountField_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID# form-control-sm no-border-radius" size="8" maxlength="6"> of badge printer calls
							</li>
							<li>							
								<input type="text" id="BadgePrintingOverageFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" name="BadgePrintingOverageFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgBadgePrinting.overageFee))#" class="BadgePrintingMoneyField_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID# form-control-sm no-border-radius" size="10" maxlength="20"> every <input type="text" id="BadgePrintingNoofCallsInOverageFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" name="BadgePrintingNoofCallsInOverageFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" value="#val(local.strDepoTLA.qryOrgBadgePrinting.noofCallsInOverageFee)#" class="BadgePrintingCountField_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID# form-control-sm no-border-radius" size="8" maxlength="6"> badge printer calls for the previous month the billing is run.
							</li>
							
							<li>
								<input type="checkbox" id="BadgePrintingNoFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" name="BadgePrintingNoFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" value="1" <cfif local.strDepoTLA.qryOrgBadgePrinting.noFee> checked</cfif>  onClick="clearMCBillingSectionSchedule('#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#','BadgePrinting')"> <label for="BadgePrintingNoFee_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#">Contract specifies no monthly fee.</label>
							</li>
						</ul>
						<div class="col">
							<button type="button" id="btn_BadgePrinting_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" name="btn_BadgePrinting_#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveBadgePrintingInfo(#local.strDepoTLA.qryOrgBadgePrinting.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-BadgePrintingXTab" role="tabpanel" aria-labelledby="BadgePrintingX">
				<form name="frmBadgePrinting0" id="frmBadgePrinting0">
					<div id="err_BadgePrinting_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Effective 
							<select name="BadgePrintingEffectiveDate_0_Month" id="BadgePrintingEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="BadgePrintingEffectiveDate_0_Year" id="BadgePrintingEffectiveDate_0_Year">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="BadgePrintingInContract_0" id="BadgePrintingInContract_0" value="1"> 
							<label for="BadgePrintingInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="BadgePrintingBillRow_0">
							<input type="text" id="BadgePrintingMonthlyFee_0" name="BadgePrintingMonthlyFee_0" value="#dollarFormat(0)#" class="BadgePrintingMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li>							
							Fee includes <input type="text" id="BadgePrintingNoofCallIncFee_0" name="BadgePrintingNoofCallIncFee_0" value="0" class="BadgePrintingCountField_0 form-control-sm no-border-radius" size="8" maxlength="6"> of badge printer calls
						</li>
						<li>							
							<input type="text" id="BadgePrintingOverageFee_0" name="BadgePrintingOverageFee_0" value="#dollarFormat(0)#" class="BadgePrintingMoneyField_0 form-control-sm no-border-radius" size="10" maxlength="20"> every <input type="text" id="BadgePrintingNoofCallsInOverageFee_0" name="BadgePrintingNoofCallsInOverageFee_0" value="0" class="BadgePrintingCountField_0 form-control-sm no-border-radius" size="8" maxlength="6"> badge printer calls for the previous month the billing is run.
						</li>
						<li>
							<input type="checkbox" id="BadgePrintingNoFee_0" name="BadgePrintingNoFee_0" value="1" onClick="clearMCBillingSectionSchedule(0,'BadgePrinting')"> <label for="BadgePrintingNoFee_0">Contract specifies no monthly fee.</label>
						</li>
					</ul>
					<div class="col">
						<button type="button" id="btn_BadgePrinting_0" name="btn_BadgePrinting_0" class="btn btn-sm btn-primary "  onclick="validateAndSaveBadgePrintingInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<div class="card m-2 p-2">
	<div class="card-header p-1 d-block clearfix">
		<h5 data-schedtype="discretionary">Discretionary Billing</h5>
	</div>
	<div class="card-body">
		<div class="alert alert-info mb-3">
			No automatic job considers the fee schedules below.
		</div>
		<ul class="nav nav-line nav-line-alt">
			<cfloop query="local.strDepoTLA.qryOrgDiscretionary">
				<cfset local.thisTabName = "Discretionary#local.strDepoTLA.qryOrgDiscretionary.scheduleID#">
				<cfset local.thisTabID = "Discretionary#local.strDepoTLA.qryOrgDiscretionary.scheduleID#Tab">
				<li class="nav-item">
					<a class="nav-link<cfif local.strDepoTLA.qryOrgDiscretionary.currentrow is 1> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						Eff. #dateformat(local.strDepoTLA.qryOrgDiscretionary.effectiveDate,"mmm yyyy")#
						<div class="divider"></div>
					</a>
				</li>
			</cfloop>
			<cfset local.thisTabName = "DiscretionaryX">
			<cfset local.thisTabID = "DiscretionaryXTab">
			<li class="nav-item">
				<a class="nav-link<cfif local.strDepoTLA.qryOrgDiscretionary.recordcount is 0> active</cfif>" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
					+ Add Schedule
					<div class="divider"></div>
				</a>
			</li>
		</ul>

		<div class="tab-content pt-2">
			<cfloop query="local.strDepoTLA.qryOrgDiscretionary">
				<cfset local.futureBillingSchedule = local.strDepoTLA.qryOrgDiscretionary.effectiveDate GT now()>
				<div class="tab-pane fade <cfif  local.strDepoTLA.qryOrgDiscretionary.currentrow is 1>active show </cfif> #local.futureBillingSchedule ? 'FutureBillSchedTabContent' : 'PastBillSchedTabContent'#" id="tab-Discretionary#local.strDepoTLA.qryOrgDiscretionary.scheduleID#Tab" role="tabpanel" aria-labelledby="Discretionary#local.strDepoTLA.qryOrgDiscretionary.scheduleID#">
					<form name="frmDiscretionary#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" id="frmDiscretionary#local.strDepoTLA.qryOrgDiscretionary.scheduleID#">
						<div id="err_Discretionary_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" class="alert alert-danger mb-2 mt-2 d-none"></div>
						<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
							<li>
								<cfif local.strDepoTLA.qryOrgDiscretionary.effectiveDate GT now()>
									<div style="display:flex;margin-bottom:10px;">
										<span>
											Effective 
											<select name="DiscretionaryEffectiveDate_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#_Month" id="DiscretionaryEffectiveDate_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#_Month" data-isrequired="1" data-reqdmessage="Select a Month" class="DiscretionaryFeeField" data-scheduleID="#local.strDepoTLA.qryOrgDiscretionary.scheduleID#">
												<option value="">Month</option>
												<cfloop from="1" to="12" index="thisMonth">
													<option value="#thisMonth#"<cfif Month(local.strDepoTLA.qryOrgDiscretionary.effectiveDate) EQ thisMonth> selected</cfif>>#MonthAsString(thisMonth)#</option>
												</cfloop>
											</select>
											<cfset yearStart = Year(local.strDepoTLA.qryOrgDiscretionary.effectiveDate) - 5>
											<cfset yearEnd = yearStart + 20>
											<select name="DiscretionaryEffectiveDate_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#_Year" id="DiscretionaryEffectiveDate_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#_Year" class="DiscretionaryFeeField" data-scheduleID="#local.strDepoTLA.qryOrgDiscretionary.scheduleID#">
												<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
													<option value="#thisYear#"<cfif Year(local.strDepoTLA.qryOrgDiscretionary.effectiveDate) EQ thisYear> selected</cfif>>#thisYear#</option>
												</cfloop>
											</select>
										</span>
										<button type="button" name="btnDelDiscretionarySched_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" id="btnDelDiscretionarySched_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" class="btn btn-danger" onclick="deleteTSBillingSchedule('Discretionary',#local.strDepoTLA.qryOrgDiscretionary.scheduleID#,'mcbilling');" style="margin-left:auto;">
											<span style="vertical-align:top;">Delete Schedule</span>
										</button>
									</div>
								<cfelse>
									<input type="hidden" name="DiscretionaryEffectiveDate_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#_Month" id="DiscretionaryEffectiveDate_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#_Month" value="#Month(local.strDepoTLA.qryOrgDiscretionary.effectiveDate)#">
									<input type="hidden" name="DiscretionaryEffectiveDate_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#_Year" id="DiscretionaryEffectiveDate_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#_Year" value="#Year(local.strDepoTLA.qryOrgDiscretionary.effectiveDate)#">
									<b>Effective #dateformat(local.strDepoTLA.qryOrgDiscretionary.effectiveDate,"mmm yyyy")#</b>
								</cfif>
							</li>
							<li>
								<input type="checkbox" name="DiscretionaryInContract_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" id="DiscretionaryInContract_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" value="1"<cfif local.strDepoTLA.qryOrgDiscretionary.inContract> checked</cfif>> 
								<label for="DiscretionaryInContract_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#">Fees outlined here are in the current contract.</label>
							</li>
							<li class="DiscretionaryBillRow_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#">
								Fee Description: <input type="text" name="DiscretionaryFeeDesc_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" id="DiscretionaryFeeDesc_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" value="#local.strDepoTLA.qryOrgDiscretionary.feeDesc#" class="DiscretionaryFeeDescField form-control-sm no-border-radius" data-scheduleID="#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" maxlength="1000" size="60">
							</li>
							<li class="DiscretionaryBillRow_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#">
								<input type="text" id="DiscretionaryMonthlyFee_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" name="DiscretionaryMonthlyFee_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" value="#dollarFormat(val(local.strDepoTLA.qryOrgDiscretionary.monthlyFee))#" class="DiscretionaryFeeField form-control-sm no-border-radius" data-scheduleID="#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
							</li>
							<li class="DiscretionaryBillRow_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#">
								## of Months <input type="text" id="DiscretionaryNumMonths_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" name="DiscretionaryNumMonths_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" value="#local.strDepoTLA.qryOrgDiscretionary.numMonths#" class="DiscretionaryFeeField form-control-sm no-border-radius" data-scheduleID="#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" size="10" maxlength="4">
								<span id="DiscretionaryTotalFee_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" class="b"></span>
							</li>
							<li id="DiscretionaryBillEndDate_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#"></li>
						</ul>
						<div class="col">
							<button type="button" id="btn_Discretionary_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" name="btn_Discretionary_#local.strDepoTLA.qryOrgDiscretionary.scheduleID#" class="btn btn-sm btn-primary " onclick="validateAndSaveDiscretionaryInfo(#local.strDepoTLA.qryOrgDiscretionary.scheduleID#);">Save Schedule</button>
							<span></span>
						</div>
					</form>
				</div>
			</cfloop>
			<div class="tab-pane fade" id="tab-DiscretionaryXTab" role="tabpanel" aria-labelledby="DiscretionaryX">
				<form name="frmDiscretionary0" id="frmDiscretionary0">
					<div id="err_Discretionary_0" class="alert alert-danger mb-2 mt-2 d-none"></div>
					<ul style="list-style:none;line-height:2.25em;padding-left:5px;margin:5px 0;">
						<li>
							Starting 
							<select name="DiscretionaryEffectiveDate_0_Month" id="DiscretionaryEffectiveDate_0_Month" data-isrequired="1" data-reqdmessage="Select a Month" class="DiscretionaryFeeDescField" data-scheduleID="X">
								<option value="">Month</option>
								<cfloop from="1" to="12" index="thisMonth">
									<option value="#thisMonth#">#MonthAsString(thisMonth)#</option>
								</cfloop>
							</select>
							<cfset yearStart = Year(now()) - 5>
							<cfset yearEnd = yearStart + 20>
							<select name="DiscretionaryEffectiveDate_0_Year" id="DiscretionaryEffectiveDate_0_Year" class="DiscretionaryFeeDescField" data-scheduleID="X">
								<cfloop from="#yearStart#" to="#yearEnd#" index="thisYear">
									<option value="#thisYear#"<cfif Year(now()) EQ thisYear> selected</cfif>>#thisYear#</option>
								</cfloop>
							</select>
						</li>
						<li>
							<input type="checkbox" name="DiscretionaryInContract_0" id="DiscretionaryInContract_0" value="1"> 
							<label for="DiscretionaryInContract_0">Fees outlined here are in the current contract.</label>
						</li>
						<li class="DiscretionaryBillRow_0">
							Fee Description: <input type="text" name="DiscretionaryFeeDesc_0" id="DiscretionaryFeeDesc_0" value="" class="DiscretionaryFeeDescField form-control-sm no-border-radius" data-scheduleID="X" maxlength="1000" size="60">
						</li>
						<li class="DiscretionaryBillRow_0">
							<input type="text" id="DiscretionaryMonthlyFee_0" name="DiscretionaryMonthlyFee_0" value="#dollarFormat(0)#" class="DiscretionaryFeeField form-control-sm no-border-radius" data-scheduleID="X" size="10" maxlength="20" onBlur="this.value=formatCurrency(this.value);"> per month.
						</li>
						<li class="DiscretionaryBillRow_0">
							## of Months <input type="text" id="DiscretionaryNumMonths_0" name="DiscretionaryNumMonths_0" value="" class="DiscretionaryFeeField form-control-sm no-border-radius" data-scheduleID="X" size="10" maxlength="4">
							<span id="DiscretionaryTotalFee_0" class="b"></span>
						</li>
						<li id="DiscretionaryBillEndDate_0"></li>
					</ul>
					<div class="col">
						<button type="button" id="btn_Discretionary_0" name="btn_Discretionary_0" class="btn btn-sm btn-primary "  onclick="validateAndSaveDiscretionaryInfo(0);">Save Schedule</button>
						<span></span>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
</cfoutput>