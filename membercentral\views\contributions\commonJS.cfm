<cfsavecontent variable="local.commonJS">
	<cfoutput>
	<script type="text/javascript">
		var #toScript(dataStruct.arrMatrixRates, "objMatrixRates")#
		var programID = Number(#dataStruct.programID#);
		var campaignID = Number(#dataStruct.campaignID#);
		<cfif len(dataStruct.displayedCurrencyType)>
			var dispCurrencyType = ' #dataStruct.displayedCurrencyType#';
		<cfelse>
			var dispCurrencyType = '';
		</cfif>

		if (!Array.prototype.find) {
			Array.prototype.find = function(predicate) {
				if (this == null) {
					throw new TypeError('Array.prototype.find called on null or undefined');
				}
				if (typeof predicate !== 'function') {
					throw new TypeError('predicate must be a function');
				}
				var list = Object(this);
				var length = list.length >>> 0;
				var thisArg = arguments[1];
				var value;
				
				for (var i = 0; i < length; i++) {
					value = list[i];
					if (predicate.call(thisArg, value, i, list)) {
						return value;
					}
				}
				return undefined;
			};
		}

		function onKeyUpTillPmtAmount(val) {
			hideMaxAmtErrMsg();
			$('##divContribUntilOptions .radioPmtDuration ##stopDate').val('');
			if($.trim(val).length > 0){
				$('input[type="radio"][name="paymentDuration"][value="amount"]').prop('checked',true);
			}else{
				$('input[type="radio"][name="paymentDuration"][value="amount"]').prop('checked',false);
			}
		}
		function onStopDateChange(val) {
			hideMaxAmtErrMsg();
			$('##divContribUntilOptions .radioPmtDuration ##tillPmtAmount').val('');
			if($.trim(val).length > 0){
				$('input[type="radio"][name="paymentDuration"][value="stopdate"]').prop('checked',true);
			}else{
				$('input[type="radio"][name="paymentDuration"][value="stopdate"]').prop('checked',false);
			}
		}
		function loadIncomingVars() {
			<cfif structKeyExists(attributes.data.incomingVars,"rateID")>
				doRateBtn(#attributes.data.incomingVars.rateID#);
			</cfif>
			<cfif structKeyExists(attributes.data.incomingVars,"progFrequencyID")>
				doFreqBtn(#attributes.data.incomingVars.progFrequencyID#);
			</cfif>
		}
		function loadDateControl() {
			if ($('##startDate').length)  {
				mca_setupDatePickerField('startDate','#DateFormat(now(),"m/d/yyyy")#');
			}

			mca_setupMultipleDatePickerFields($('.additionalOptions'),'MCCPDateControl');

			$('.additionalOptions').find('.MCCPDateControlClearLink').click(function(event){
				var linkedDateControlID = $(this).data('linkeddatecontrol');
				$('##' + linkedDateControlID).val('').change();
				event.preventDefault();
			});
		}
		function doRateBtn(rid) {
			$('button.contributeBtn, button.freqBtn').removeClass('btnSelected').removeAttr('aria-pressed');
			$('##btnRateID' + rid).addClass('btnSelected').attr('aria-pressed',true);
			$('##rateID').val(rid);
			$('.hideuntilrate').hide();
			$('##progFreqID').val('');

			var thisRateFreqs = objMatrixRates.find(function (r) { return r.rateid == rid; }).arrfreq;
			if(thisRateFreqs.length > 1){
				$('##divMTXRateFreq' + rid).show();
				$('##divHowOften').show();
			}
			else if(thisRateFreqs.length == 1) {
				doFreqBtn(thisRateFreqs[0].progfreqid);
			}
		}
		function doFreqBtn(fid) {
			var rid = $('##rateID').val();
			$('button.freqBtn').removeClass('btnSelected').removeAttr('aria-pressed');
			$('##progFreqID').val(fid);

			var hasInstallSetAmt = false;
			var #toScript(dataStruct.qryProgramDistributions.recordCount gt 1 and not listFindNoCase("NODISPLAY,DISPONLY", dataStruct.programDistribSettingCode), "canEditContributionSplit")#
			var #toScript(dataStruct.allowSetStartDate eq 1, "allowSetStartDate")#
			var #toScript(dataStruct.strProgramFields.hasFields or dataStruct.strCrossProgramFields.hasFields, "hasCustomFields")#
			var #toScript(dataStruct.allowAnonymous eq 1, "allowAnonymous")#
			
			if (rid > 0) {
				$('##divMTXRateFreq'+rid+' .btnProgFreqID' + fid).addClass('btnSelected').attr('aria-pressed',true);

				var f = objMatrixRates.find(function (r) { return r.rateid == rid; }).arrfreq.find(function (f) { return f.progfreqid == fid; });

				$('##installmentAmt').attr('data-min',f.installminamt).attr('data-max',f.installmaxamt).val(formatCurrency(f.installdefamt));
				if (f.installsetamt.toString().length) {
					$('##installmentAmtDispVal').html(formatCurrency(f.installsetamt));
					$('##installmentAmt').hide();
					hasInstallSetAmt = true;
				} else {
					$('##installmentAmtDispVal').html('');
					$('##installmentAmt').show();
				}

				$('##installmentAmtDesc').text('(' + f.label + ')');

				if (f.isrecurring == 1) {
					doRecurringSetup(f);
				} else {
					$('##startContribLabel').text('Contribute on:');
					$('##divContribUntilOptions').html('');
					$('##divContribUntil').hide();
				}

				if (f.isrange == 1 && f.installminamt > 0 && f.installmaxamt > 0) {
					$('##installmentAmtAllowedVals').text('(between $' + formatCurrency(f.installminamt) + ' - $' + formatCurrency(f.installmaxamt) + ')');
				} else if (f.isrange == 1 && f.installminamt > 0) {
					$('##installmentAmtAllowedVals').text('($' + formatCurrency(f.installminamt) + ' or more)');
				} else if (f.isrange == 1 && f.installmaxamt > 0) {
					$('##installmentAmtAllowedVals').text('(up to $' + formatCurrency(f.installmaxamt) + ')');
				} else {
					$('##installmentAmtAllowedVals').text('');
				}

				verifyInstallmentAmt(f.installdefamt);
			}
			
			var hidePmtDurationSection = allowSetStartDate == "false" && hasInstallSetAmt && (f.isrecurring != 1 || (Number(f.allowperpetual) == 1 && Number(f.allowfixedinstallments) == 0));
			var autoContinue = rid > 0 && hidePmtDurationSection && canEditContributionSplit == "false" && hasCustomFields == "false" && allowAnonymous == "false";
			
			$('##divPmtDuration').toggle(!hidePmtDurationSection);
			$('##divAdditionalOptionsHolder').toggle(!autoContinue);

			if(autoContinue) $("##frmContribution :submit").trigger('click');
		}
		function doRecurringSetup(f) {
			$('##startContribLabel').text('Start contributing on:');
			$('##divContribUntilOptions').html('');
			
			if (Number(f.allowperpetual) == 1 && Number(f.allowfixedinstallments) == 0) {
				$('##divContribUntilOptions').html('<div class="radioPmtDuration"><input type="hidden" name="paymentDuration" value="perpetual">This is an ongoing contribution.</div>').show();
			} else {
				if (Number(f.allowperpetual) == 1) {
					$('##divContribUntilOptions').append('<div class="radioPmtDuration"><input type="radio" name="paymentDuration" value="perpetual"> This is an ongoing contribution.</div>');

					if (Number(f.perpetualasdefoption) == 1)
						$('input[type="radio"][name="paymentDuration"][value="perpetual"]').prop('checked',true);
				}
				if (Number(f.allowfixedinstallments) == 1) {
					$('##divContribUntilOptions').append('<div class="radioPmtDuration"><input type="radio" name="paymentDuration" value="stopdate"> Until &nbsp; <input type="text" class="amountBox" name="stopDate" id="stopDate" value="" onkeyup="onStopDateChange(this.value)" onblur="onStopDateChange(this.value);"></div>');
					$('##divContribUntilOptions').append('<div class="radioPmtDuration"><input type="radio" name="paymentDuration" value="amount"> Until I\'ve given &nbsp;$ <input type="text" class="amountBox" name="tillPmtAmount" id="tillPmtAmount" value="" data-mininstallments="'+f.mininstallments+'" onkeyup="onKeyUpTillPmtAmount(this.value)" onblur="verifyTillPmtAmount();" autocomplete="off">'+dispCurrencyType+'<div id="maxAmtErrMsg" class="alert" style="display:none;"></div>');

					if (Number(f.fixedinstallmentsasdefoption) == 1)
						$('input[type="radio"][name="paymentDuration"][value="stopdate"]').prop('checked',true);
						
					mca_setupDatePickerRangeFields('startDate','stopDate');

					var calcTillPmtAmt = parseFloat(Number(f.installdefamt) * Number(f.mininstallments)).toFixed(2);
					$('##tillPmtAmount').val(calcTillPmtAmt).attr('data-mininstallments',f.mininstallments);
					getFinalInstallmentDate();
				}
			}
			$('##divContribUntil').show();
		}
		function getFinalInstallmentDate() {
			var findDateResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') { 
					$('##stopDate').val(r.finalinstallmentdate);	
					mca_setupDatePickerField('stopDate',r.finalinstallmentdate);					
				} else { 
					alert('We were unable to load payment duration settings.'); 
				}
			};

			/* calculate final installment date only if the current settings allow fixed installments */
			if ($('##divContribUntil').is(':visible') && $('##installmentAmt').val() != '' && $('##tillPmtAmount').length && $('##tillPmtAmount').val() != '') {
				var amt = Number(parseFloat(stripCurrency($('##installmentAmt').val())).toFixed(2));
				var tillPmtAmt = Number(parseFloat(stripCurrency($('##tillPmtAmount').val())).toFixed(2));

				var objParams = { programID:programID, campaignID:campaignID, rateID:$('##rateID').val(), progFreqID:$('##progFreqID').val(), startDate:$('##startDate').val(), 
									installmentAmount:amt, pledgedAmount:tillPmtAmt };
				TS_AJX('ADMINCONTRIBUTION','getFinalInstallmentDate',objParams,findDateResult,findDateResult,10000,findDateResult);
			}
		}
		function getAmt(e) {
			return Number(parseFloat(stripCurrency($('##' + e).val())).toFixed(2));
		}
		function verifyInstallmentAmt(a) {
			var ia = $('##installmentAmt');
			var amt = Number(parseFloat(stripCurrency(a)).toFixed(2));
			var min = Number(parseFloat(ia.attr('data-min')).toFixed(2));
			var max = Number(parseFloat(ia.attr('data-max')).toFixed(2));
			if (!isNaN(amt) && amt > 0) {
				ia.val(formatCurrency(amt));
				if (!isNaN(min) && min > 0 && amt < min) {
					alert('Enter a contribution amount greater than $' + formatCurrency(min) + '.');
				} else if (!isNaN(max) && max > 0 && amt > max) {
					alert('Enter a contribution amount less than $' + formatCurrency(max) + '.');
				} else if (!isNaN(min) && !isNaN(max) && min > 0 && max > 0 && (amt < min || amt > max)) {
					alert('Enter a contribution amount between $' + formatCurrency(min) + ' and $' + formatCurrency(max) + '.');
				}
			} else {
				if (!isNaN(min) && min > 0) {
					ia.val(formatCurrency(min));
				} else if (!isNaN(max) && max > 0) {
					ia.val(formatCurrency(max));
				} else {
					ia.val(formatCurrency(1));
				}
				alert('Enter a valid contribution amount.');
			}

			if ($('input[type="radio"][name="paymentDuration"][value="amount"]').is(':checked')) {
				verifyTillPmtAmount();
			} else if ($('##tillPmtAmount').length) {
				$('##tillPmtAmount').val('');
			}

			setDistributionSplits(amt);
			
			$(document).ready(function() {
				$('input[type="radio"][name="paymentDuration"]').on('change', function() {
					hideMaxAmtErrMsg();
					$('##divContribUntilOptions .radioPmtDuration .amountBox').val('');
				});
			});
		}
		function setDistributionSplits(a) {
			<cfif dataStruct.programDistribSettingCode neq "NODISPLAY" and dataStruct.qryProgramDistributions.recordCount>
				var totalAmt = 0;
				var remainingAmt = 0;
				<cfif dataStruct.qryProgramMeta.distribSettingCode eq "DISPCHDEF0">
					$('input.splitAmtBox').val(0);
				<cfelse>
					<cfloop query="dataStruct.qryProgramDistributions">
						var currRowAmt = Number(parseFloat((a * #val(dataStruct.qryProgramDistributions.distPct)#)/100).toFixed(2));
						currRowAmt = isNaN(currRowAmt) ? 0 : currRowAmt; 
						totalAmt += currRowAmt;
						$('.distribAmt_#dataStruct.qryProgramDistributions.distribID#').val(formatCurrency(currRowAmt));
					</cfloop>
				</cfif>
				remainingAmt = a - totalAmt;
				if (remainingAmt < 0 && Math.abs(remainingAmt.toFixed(2)) != 0) {
					$('##totalSplitAmount').html('You\'ve distributed $' + Math.abs(remainingAmt.toFixed(2)) + ' too much.');
				} else if (remainingAmt > 0) {
					$('##totalSplitAmount').html('$' + remainingAmt.toFixed(2) + ' left to distribute');
				} else {
					$('##totalSplitAmount').html('');
				}

				if($('##divSplitInstructions').is(':hidden') && $('##divContributionSplit').is(':hidden')) {
					$('##divSplitInstructions').show();
				}
			</cfif>
		}
		function calculateSplitAmt() {
			var contribAmt = getAmt('installmentAmt');
			var totalAmt = 0;
			var remainingAmt = 0;
			<cfloop query="dataStruct.qryProgramDistributions">
				var currRowAmt = Number(parseFloat(stripCurrency($('##cpd_split_#dataStruct.qryProgramDistributions.distribID#').val())).toFixed(2));
				currRowAmt = isNaN(currRowAmt) ? 0 : currRowAmt; 
				totalAmt += currRowAmt;
				$('.distribAmt_#dataStruct.qryProgramDistributions.distribID#').val(formatCurrency(currRowAmt));
			</cfloop>

			contribAmt = isNaN(contribAmt) ? 0 : contribAmt; 
			remainingAmt = contribAmt - totalAmt;
			
			if (remainingAmt < 0 && Math.abs(remainingAmt.toFixed(2)) != 0) {
				$('##totalSplitAmount').html('You\'ve distributed $' + Math.abs(remainingAmt.toFixed(2)) + ' too much.');
			} else if (remainingAmt > 0) {
				$('##totalSplitAmount').html('$' + remainingAmt.toFixed(2) + ' left to distribute');
			} else {
				$('##totalSplitAmount').html('');
			}
		}
		function hideMaxAmtErrMsg() {
			$('##maxAmtErrMsg').html('').hide();
		}
		function setTillPmtAmount(a) {
			$('##tillPmtAmount').val(formatCurrency(a));
			hideMaxAmtErrMsg();
		}
		function verifyTillPmtAmount() {
			var msg = '';
			var ia = getAmt('installmentAmt');
			var maxAmt = getAmt('tillPmtAmount');
			var minInstallments = Number($('##tillPmtAmount').attr('data-mininstallments'));
			var minAmt = Number(parseFloat(minInstallments * ia).toFixed(2));
			maxAmt = isNaN(maxAmt) ? 0 : maxAmt;
			var minAmtInCents = Number(minAmt.toString().replace('.',''));
			var maxAmtInCents = Number(maxAmt.toString().replace('.',''));
			var installAmtInCents = Number(ia.toString().replace('.',''));
			
			$('##tillPmtAmount').val(formatCurrency(maxAmt));

			if (!isNaN(ia) && ia > 0) {
				if (maxAmt < minAmt) {
					msg = 'Amount must be at least <a href="javascript:setTillPmtAmount(' + minAmt + ');">$' + formatCurrency(minAmt) + '</a> since minimum number of installments under this selected rate/frequency is ' + minInstallments + '.';
				} else if (ia > maxAmt) {
					msg = 'Amount must be at least <a href="javascript:setTillPmtAmount(' + ia + ');">$' + formatCurrency(ia) + '</a>';
				} else if (parseFloat(maxAmtInCents/installAmtInCents) != parseInt(maxAmtInCents/installAmtInCents)) {
					var count = parseInt(maxAmt/ia);
					var amtA = 0;
					var amtB = 0;
					if (count == 1) {
						amtA = ia;
						amtB = ia * 2;
					} else if (count > 0) {
						amtA = ia * count;
						amtB = ia * (count + 1);
					}
					msg = 'Amount must be a multiple of $' + formatCurrency(ia) + '.<br/>Select <a href="javascript:setTillPmtAmount(' + amtA + ');">$' + formatCurrency(amtA) + '</a> or <a href="javascript:setTillPmtAmount(' + amtB + ');">$' + formatCurrency(amtB) + '</a> or enter a new amount.';
				}
			}
			if (msg.length) {
				$('##tillPmtAmount').val('');
				$('##maxAmtErrMsg').html(msg).show();
			} else {
				$('##maxAmtErrMsg').html('').hide();
			}
		}
		function showContributionSplit() {
			$('##divSplitInstructions').hide();
			$('##divContributionSplit').show();
		}
		function hideContributionSplit() {
			$('##divSplitInstructions').show();
			$('##divContributionSplit').hide();
		}
		function formatCurrency(num) {
			num = num.toString().replace(/\$|\,/g,'');
			if(isNaN(num)) num = "0";
			num = Math.abs(num);
			sign = (num == (num = Math.abs(num)));
			num = Math.floor(num*100+0.50000000001);
			cents = num%100;
			num = Math.floor(num/100).toString();
			if(cents<10) cents = "0" + cents;
			for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
			return (((sign)?'':'-') + num + '.' + cents);
		}
		function stripCurrency(str){
			str += '';  
			var rgx = /^\d|\.|-$/;  
			var out = '';  
			for( var i = 0; i < str.length; i++ ){ if( rgx.test( str.charAt(i) ) ){ if( !( ( str.charAt(i) == '.' && out.indexOf( '.' ) != -1 ) || ( str.charAt(i) == '-' && out.length != 0 ) ) ){ out += str.charAt(i); } } }
			return parseFloat(out);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.commonJS#">