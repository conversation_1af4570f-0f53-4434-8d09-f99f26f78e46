ALTER PROC dbo.queue_paperStatements_download
@status varchar(60),
@csvfilename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @selectsql varchar(max) = '';

	SELECT @selectsql = 'SELECT ''paperStatements'' as queueType, qs.queueStatus, qi.itemID, qi.recordedByMemberID, 
		qi.orgID, qi.siteID, qi.title, qi.topContent, qi.showZeroDollarAddons, qi.showRenewOnlineLink, qi.bottomContent, 
		qi.invoiceheaderimgurl, qi.renewalsuburl, qi.downloadMode, qi.queuePriority, psd.memberID, psd.dateAdded, psd.dateUpdated, 
		ROW_NUMBER() OVER(order by psd.dateAdded, qi.itemID) as mcCSVorder 
		*FROM* platformQueue.dbo.queue_paperStatements as qi
		INNER JOIN platformQueue.dbo.queue_paperStatementsDetail AS psd ON psd.itemID = qi.itemID
		INNER JOIN platformQueue.dbo.tblQueueStatuses AS qs ON qs.queueStatusID = psd.queueStatusID and qs.queueStatus = ''' + @status + '''';

	EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
