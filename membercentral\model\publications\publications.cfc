<cfcomponent extends="model.AppLoader" output="no">

	<cfset variables.defaultEvent = "controller">
	<cfset variables.applicationReservedURLParams = "pubAction,pubIssueID,pubIssueItemID,pos">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.returnStruct.data = {
			mainurl = '/?#getBaseQueryString(false)#'
		}>

		<!--- get app instance settings --->
		<cfset variables.instanceSettings = getInstanceSettings(this.appInstanceID)>
		<cfset variables.instanceSettings.appRightsStruct = buildRightAssignments(siteResourceID=variables.instanceSettings.siteResourceID, memberid=getOrgMemberIDForPublication(), siteid=variables.instanceSettings.siteID)>
		<cfset buildAppRightsStruct(memberid=getOrgMemberIDForPublication(),siteid=variables.instanceSettings.siteid)>

		<!--- validate params. if fail, send back to pg= if that is defined. Dont use mainurl because we could get into a recursive cflocation loop --->
		<cfif NOT validateParameters(event=arguments.event)>
			<cflocation url="/?pg=#arguments.event.getValue('pg','main')#" addtoken="false">
		</cfif>

		<cfset arguments.event.setValue('mainurl',local.returnStruct.data.mainurl)>

		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
		</cfif>

		<cfswitch expression="#arguments.event.getValue('pubAction')#">
			<cfcase value="downloadIssue">
				<cfset local.actionStruct = downloadIssue(event=arguments.event)>
			</cfcase>
			<cfcase value="viewIssue">
				<cfset local.actionStruct = viewIssue(event=arguments.event)>
			</cfcase>
			<cfcase value="viewLatestIssue">
				<cfset local.actionStruct = viewLatestIssue(event=arguments.event)>
			</cfcase>
			<cfdefaultcase>
				<!--- List Recent Issues --->
				<cfset local.actionStruct = listPublishedIssues(event=arguments.event)>
			</cfdefaultcase>
		</cfswitch>
		<cfset local.returnStruct.view = local.actionStruct.view>
		<cfset local.returnStruct.data.append(local.actionStruct.data, true)>

		<!--- record app hit --->
		<cfset application.objPlatformStats.recordAppHit(appname="Publications", appsection="")>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="validateParameters" access="private" output="false" returntype="boolean">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();

		arguments.event.paramValue('pubAction','');

		// pubAction needs to be a simple value. URLs like   pubAction..id= will result in pubAction being a struct
		if (NOT isSimpleValue(arguments.event.getValue('pubAction')))
			return false;

		// pubAction must be a valid option
		if (len(arguments.event.getValue('pubAction')) and NOT listFindNoCase("downloadIssue,viewIssue,viewArchive,viewLatestIssue", arguments.event.getValue('pubAction')))
			return false;

		// pubAction specific validations
		switch(arguments.event.getValue('pubAction')){
			case 'downloadIssue':
				arguments.event.paramValue('pubIssueID',0);
				// id needs to be a simple value. URLs like   id..id= will result in id being a struct
				if (NOT isSimpleValue(arguments.event.getValue('pubIssueID')))
					return false;

				// ID must be valid positive integer
				arguments.event.setValue('pubIssueID',int(val(arguments.event.getValue('pubIssueID'))));

				if (NOT isValid("integer",arguments.event.getValue('pubIssueID')) or arguments.event.getValue('pubIssueID') lt 1)
					return false;
				if (NOT isValidPublicationIssue(siteID=variables.instanceSettings.siteID, publicationID=variables.instanceSettings.publicationID, issueID=arguments.event.getValue('pubIssueID'), issueItemID=0))
					return false;
				break;
			case 'viewIssue':
				arguments.event.paramValue('pubIssueID',0);
				arguments.event.paramValue('pubIssueItemID',0);
				// id needs to be a simple value. URLs like   id..id= will result in id being a struct
				if (NOT isSimpleValue(arguments.event.getValue('pubIssueID')))
					return false;
				if (NOT isSimpleValue(arguments.event.getValue('pubIssueItemID')))
					return false;

				// ID must be valid positive integer
				arguments.event.setValue('pubIssueID',int(val(arguments.event.getValue('pubIssueID'))));
				arguments.event.setValue('pubIssueItemID',int(val(arguments.event.getValue('pubIssueItemID'))));

				if (NOT isValid("integer",arguments.event.getValue('pubIssueID')) or arguments.event.getValue('pubIssueID') lt 1)
					return false;
				if (NOT isValid("integer",arguments.event.getValue('pubIssueItemID')) or arguments.event.getValue('pubIssueItemID') lt 0)
					return false;
				if (NOT isValidPublicationIssue(siteID=variables.instanceSettings.siteID, publicationID=variables.instanceSettings.publicationID, issueID=arguments.event.getValue('pubIssueID'), issueItemID=arguments.event.getValue('pubIssueItemID')))
					return false;
				break;
			case '':
				arguments.event.paramValue('pos',1);
				// id needs to be a simple value. URLs like   id..id= will result in id being a struct
				if (NOT isSimpleValue(arguments.event.getValue('pos')))
					return false;
				if (NOT isValid("integer",arguments.event.getValue('pos')) or arguments.event.getValue('pos') lt 1)
					return false;
				break;
		}

		return true;
		</cfscript>
	</cffunction>

	<cffunction name="listPublishedIssues" access="private" output="no" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>
		<cfset local.dataStruct = {} >
		<cfset local.dataStruct.siteID = variables.instanceSettings.siteid>
		<cfset local.dataStruct.publicationName = variables.instanceSettings.applicationInstanceName>
		<cfset local.dataStruct.qryPublicationDetails = variables.instanceSettings>
		<cfset local.dataStruct.numEntries = 10>
		<cfset local.dataStruct.qryPublishedIssues = getPublishedIssuesByVolumes(publicationID=variables.instanceSettings.publicationID, pos=arguments.event.getValue('pos',1), numEntries=local.dataStruct.numEntries)>
		<cfset local.dataStruct.baseQueryString = variables.instanceSettings.baseQueryString>
		<cfset local.dataStruct.viewOnlineEditionsPermission = variables.instanceSettings.appRightsStruct.viewOnlineEditions>
		<cfset local.dataStruct.showFeaturedImage = val(variables.instanceSettings.featureImageConfigID) gt 0 and val(variables.instanceSettings.archiveFeatureImageSizeID) gt 0>
		<cfif local.dataStruct.showFeaturedImage>
			<cfset local.dataStruct.archiveFeatureImageSizeID = val(variables.instanceSettings.archiveFeatureImageSizeID)>
			<cfset local.dataStruct.archiveFeatureImageFileExt = variables.instanceSettings.archiveFeatureImageFileExt>
			<cfset local.dataStruct.imageUUID = createUUID()>
			<cfset local.dataStruct.featuredImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(arguments.event.getValue('mc_siteinfo.orgcode'))#/#LCASE(arguments.event.getValue('mc_siteinfo.sitecode'))#/featuredimages/thumbnails/">
			<cfset local.dataStruct.featuredImageRootPath = "/userassets/#LCASE(arguments.event.getValue('mc_siteinfo.orgcode'))#/#LCASE(arguments.event.getValue('mc_siteinfo.sitecode'))#/featuredimages/">
			<cfset local.dataStruct.featuredThumbImageRootPath = "#local.dataStruct.featuredImageRootPath#thumbnails/">
			<cfif val(variables.instanceSettings.featureImageID) gt 0 and fileExists("#local.dataStruct.featuredImageFullRootPath##variables.instanceSettings.featureImageID#-#variables.instanceSettings.archiveFeatureImageSizeID#.#variables.instanceSettings.archiveFeatureImageFileExt#")>
				<cfset local.dataStruct.defaultFeaturedThumbImage = "#local.dataStruct.featuredImageRootPath#thumbnails/#variables.instanceSettings.featureImageID#-#variables.instanceSettings.archiveFeatureImageSizeID#.#variables.instanceSettings.archiveFeatureImageFileExt#">
			<cfelse>
				<cfset local.dataStruct.defaultFeaturedThumbImage = "">
			</cfif>
		</cfif>

		<cfset local.viewName = "dsp_listPublishedIssues">
		<cfreturn returnAppStruct(local.dataStruct,"publications/#local.viewDirectory#/#local.viewName#")>
	</cffunction>

	<cffunction name="viewIssue" access="private" output="no" returntype="struct">
		<cfargument name="event" type="any">
				
		<cfset var local = structNew()>
		<cfset local.objPublications = createObject("component","model.admin.publications.publication")>
		<cfset local.objResourceTemplate = CreateObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>

		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>
		<cfset local.dataStruct = {}>
		<cfset local.dataStruct.siteID = variables.instanceSettings.siteid>
		<cfset local.dataStruct.publicationName = variables.instanceSettings.applicationInstanceName>
		<cfset local.dataStruct.orgMemberID = getOrgMemberIDForPublication()>
		<cfset local.dataStruct.qryPublicationDetails = variables.instanceSettings>
		<cfset local.dataStruct.issueID = arguments.event.getValue('pubIssueID')>
		<cfset local.dataStruct.issueItemID = arguments.event.getValue('pubIssueItemID')>
		<cfset local.qryIssueDetails = local.objPublications.getIssueDetails(publicationID=variables.instanceSettings.publicationID, issueID=local.dataStruct.issueID)>
		<cfset local.dataStruct.issueType = local.qryIssueDetails.issueType>
		<cfset local.dataStruct.renderedHTML = {
			header = "",
			body = "",
			footer = "",
			sidebar = ""
		}>

		<!--- permissions --->
		<cfset local.dataStruct.viewingAllowed = hasViewIssuePermission(issueID=local.dataStruct.issueID)>
		<cfif not local.dataStruct.viewingAllowed and not application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cflocation url="/?pg=login" addtoken="false">
		<cfelseif not local.dataStruct.viewingAllowed>
			<cfset local.noRightsContentID = arguments.event.getValue('mc_siteinfo.noRightsContentID')>
			<cfset local.tmpContent = application.objCMS.getStaticContent(local.noRightsContentID,1)>
			<cfif len(local.tmpContent.rawContent)>
				<cfset local.dataStruct.noRightsContent = local.tmpContent.rawContent>
			<cfelse>
				<cfset local.dataStruct.noRightsContent = "<p>You are not allowed to view this Issue</p>">
			</cfif>
		<!--- redirect --->
		<cfelseif (local.qryIssueDetails.issueType EQ "I" OR (NOT val(variables.instanceSettings.webHomeContentTemplateID) AND local.dataStruct.issueItemID eq 0))
			AND arguments.event.getValue('mode','') NEQ 'stream'>
			<cflocation url="#arguments.event.getValue('mainurl')#&pubAction=viewIssue&pubIssueID=#local.dataStruct.issueID#&mode=stream" addtoken="false">
		<cfelseif local.qryIssueDetails.issueType EQ "I">
			<cfquery name="local.getImportedHTML" datasource="#application.dsn.membercentral.dsn#">
				select rawContent
				from dbo.fn_getContent(<cfqueryparam value="#local.qryIssueDetails.HTMLcontentID#" cfsqltype="CF_SQL_INTEGER">,1)
			</cfquery>
			<cfset local.dataStruct.renderedHTML.body = local.getImportedHTML.rawContent>
		<cfelseif local.qryIssueDetails.issueType EQ "P" AND (NOT(val(variables.instanceSettings.webHomeContentTemplateID) or val(variables.instanceSettings.htmlEmailTemplateID)) OR NOT local.qryIssueDetails.includedItemsCount)>
			<cfif val(local.qryIssueDetails.pdfEditionDocumentID)>
				<cflocation url="#arguments.event.getValue('mainurl')#&pubAction=downloadIssue&pubIssueID=#local.qryIssueDetails.issueID#" addtoken="false">
			<cfelse>
				<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="false">
			</cfif>
		<cfelseif not val(variables.instanceSettings.webHomeContentTemplateID)>
			<cfif local.dataStruct.issueItemID gt 0>
				<!--- show blog post --->
				<cfset local.dataStruct.renderedHTML.body = getIssueItemBlogEntryView(siteID=local.dataStruct.siteID, issueItemID=local.dataStruct.issueItemID, orgMemberID=local.dataStruct.orgMemberID, viewDirectory=local.viewDirectory)>
			<cfelse>
				<!--- show email version online --->
				<cfset local.mainEmail = application.objMember.getMainEmail(memberID=local.dataStruct.orgMemberID).email>
				<cfset local.emailIssue = createObject("component","model.admin.publications.publication").previewIssueEmailEdition(siteID=variables.instanceSettings.siteID, 
							publicationID=variables.instanceSettings.publicationID, issueID=local.dataStruct.issueID, memberID=local.dataStruct.orgMemberID, 
							emailConsentListID=variables.instanceSettings.emailConsentListID, email=local.mainEmail)>
				<cfset local.dataStruct.renderedHTML.body = local.emailIssue.content>
			</cfif>
		<cfelse>
			<cfif local.dataStruct.issueItemID gt 0>
				<cfset local.dataStruct.renderedHTML.body = getIssueItemBlogEntryView(siteID=local.dataStruct.siteID, issueItemID=local.dataStruct.issueItemID, orgMemberID=local.dataStruct.orgMemberID, viewDirectory=local.viewDirectory)>
				<cfset local.templateModeList = "header,footer,sidebar">
			<cfelse>
				<cfset local.templateModeList = "header,body,footer,sidebar">
			</cfif>

			<cfset local.pubIssueJSON = local.objPublications.getIssueItemsInfo(siteID=local.dataStruct.siteID, publicationID=variables.instanceSettings.publicationID, issueID=local.dataStruct.issueID)>

			<cfset local.qryPubTemplateDetails = getPublicationTemplateDetails(siteID=local.dataStruct.siteID,
						webHeaderTemplateID=val(local.dataStruct.qryPublicationDetails.webHeaderTemplateID),
						webHomeContentTemplateID=val(local.dataStruct.qryPublicationDetails.webHomeContentTemplateID),
						webSidebarTemplateID=val(local.dataStruct.qryPublicationDetails.webSidebarTemplateID),
						webFooterTemplateID=val(local.dataStruct.qryPublicationDetails.webFooterTemplateID))>

			<cfloop list="#local.templateModeList#" index="local.thisMode">
				<cfquery name="local.qryThisTemplate" dbtype="query">
					select templateContent, templateFormat
					from [local].qryPubTemplateDetails
					where templateMode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisMode#">
				</cfquery>
				
				<cfif local.qryThisTemplate.recordCount>
					<cfset local.dataStruct.renderedHTML[local.thisMode] = local.objResourceTemplate.doRenderResourceTemplate(
							template = local.qryThisTemplate.templateContent,
							model = local.pubIssueJSON.issueData,
							templateFormat = local.qryThisTemplate.templateFormat).content>
				</cfif>
			</cfloop>
		</cfif>

		<cfset local.viewName = "dsp_viewIssue">

		<cfreturn returnAppStruct(local.dataStruct,"publications/#local.viewDirectory#/#local.viewName#")>
	</cffunction>

	<cffunction name="getIssueItemBlogEntryView" access="private" output="no" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="issueItemID" type="numeric" required="yes">
		<cfargument name="orgMemberID" type="numeric" required="yes">
		<cfargument name="viewDirectory" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = "">
		<cfset local.strIssueItem = {}>
		<cfset local.strIssueItem.qryIssueItemDetails = getPublicationIssueItemDetails(issueItemID=arguments.issueItemID)>
		
		<cfif local.strIssueItem.qryIssueItemDetails.recordCount>
			<cfset local.strIssueItem.siteID = variables.instanceSettings.siteID>
			<cfset local.strIssueItem.orgID = variables.instanceSettings.orgID>
			<cfset local.strIssueItem.siteCode = variables.instanceSettings.siteCode>
			<cfset local.strIssueItem.orgCode = variables.instanceSettings.orgCode>
			<cfset local.strIssueItem.orgMemberID = arguments.orgMemberID>
		
			<cfset local.strIssueItem.strItemRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.strIssueItem.qryIssueItemDetails.blogSiteResourceID, 
														memberID=arguments.orgMemberID, siteID=arguments.siteID)>
			<cfset local.strIssueItem.appLocation = getAppLocation(applicationInstanceID=local.strIssueItem.qryIssueItemDetails.applicationInstanceID, siteID=arguments.siteID)>
			<cfset local.strIssueItem.appBaseLink = getAppBaseLink(applicationInstanceID=local.strIssueItem.qryIssueItemDetails.applicationInstanceID)>
			<cfset local.strIssueItem.qryPostTypeFieldData = createObject("component","model.blog.blog").getPostTypeFieldData(blogEntryID=local.strIssueItem.qryIssueItemDetails.blogEntryID)>
			<cfset local.strIssueItem.showFeaturedImage = val(local.strIssueItem.qryIssueItemDetails.issueItemFeatureImageConfigID) gt 0 
																and val(local.strIssueItem.qryIssueItemDetails.issueItemFeatureImageSizeID) gt 0>
			
			<cfset local.strIssueItem.issueItemFeaturedImageSRC = "">"
			<cfif local.strIssueItem.showFeaturedImage>
				<cfset local.strIssueItem.imageUUID = createUUID()>
				<cfset local.strIssueItem.featuredImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.strIssueItem.orgCode)#/#LCASE(local.strIssueItem.siteCode)#/featuredimages/thumbnails/">
				<cfset local.strIssueItem.featuredImageRootPath = "/userassets/#LCASE(local.strIssueItem.orgCode)#/#LCASE(local.strIssueItem.siteCode)#/featuredimages/">
				<cfset local.strIssueItem.featuredThumbImageRootPath = "#local.strIssueItem.featuredImageRootPath#thumbnails/">
				<cfif val(local.strIssueItem.qryIssueItemDetails.issueItemFeatureImageID) AND fileExists("#local.strIssueItem.featuredImageFullRootPath##local.strIssueItem.qryIssueItemDetails.issueItemFeatureImageID#-#local.strIssueItem.qryIssueItemDetails.issueItemFeatureImageSizeID#.#local.strIssueItem.qryIssueItemDetails.issueItemFeatureImageFileExt#")>
					<cfset local.strIssueItem.issueItemFeaturedImageSRC = "#local.strIssueItem.featuredImageRootPath#thumbnails/#local.strIssueItem.qryIssueItemDetails.issueItemFeatureImageID#-#local.strIssueItem.qryIssueItemDetails.issueItemFeatureImageSizeID#.#local.strIssueItem.qryIssueItemDetails.issueItemFeatureImageFileExt#">
				</cfif>

				<cfif NOT len(local.strIssueItem.issueItemFeaturedImageSRC)>
					<cfset local.strIssueItem.showFeaturedImage = false>
				</cfif>
			</cfif>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<cfmodule template="/views/publications/#arguments.viewDirectory#/dsp_viewItemBlogEntry.cfm" data="#local.strIssueItem#">
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasViewIssuePermission" access="private" output="no" returntype="boolean">
		<cfargument name="issueID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.success = false>

		<cfif variables.instanceSettings.appRightsStruct.viewOnlineEditions>
			<cfset local.success = true>
		<cfelseif variables.instanceSettings.appRightsStruct.previewOnlineEditions and variables.instanceSettings.onlineEditionPreviewableEditionCount gt 0>
			<cfset local.qryPreviewableIssues = getPreviewableIssues(publicationID=variables.instanceSettings.publicationID, numEntries=variables.instanceSettings.onlineEditionPreviewableEditionCount)>
			<cfif local.qryPreviewableIssues.recordCount>
				<cfset local.success = listFindNoCase(valueList(local.qryPreviewableIssues.issueID), arguments.issueID)>
			</cfif>
		</cfif>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="viewLatestIssue" access="private" output="no" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var latestIssueID = getLatestPublishedIssue(publicationID=variables.instanceSettings.publicationID).issueID>
		<cfset arguments.event.setValue('pubIssueID', val(latestIssueID))>
		<cfset arguments.event.setValue('pubIssueItemID', 0)>

		<cfreturn viewIssue(event=arguments.event)>
	</cffunction>

	<cffunction name="downloadIssue" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.dataStruct = {}>

		<cfquery name="local.qryIssuePDF" datasource="#application.dsn.memberCentral.dsn#">
			select pdfEditionDocumentID
			from dbo.pub_issues
			where issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pubIssueID')#">
		</cfquery>
		
		<cfset local.dataStruct.hasViewPermission = hasViewIssuePermission(issueID=arguments.event.getValue('pubIssueID'))>

		<cfif local.dataStruct.hasViewPermission and val(local.qryIssuePDF.pdfEditionDocumentID) gt 0>
			<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
			<cfset local.documentFound = 0>
			<cfset local.allowed = 0>

			<cfset local.getDocument = local.objDocument.getDocumentData(documentID=local.qryIssuePDF.pdfEditionDocumentID, documentVersionID=0, activeOnly=0, lang=session.mcstruct.languageCode)>
			
			<cfif local.getDocument.siteResourceID gt 0 and arguments.event.getValue('mc_siteinfo.orgcode') eq local.getDocument.orgcode>
				<cfset local.documentFound = 1>

				<!--- this supports MemberKey --->
				<cfset local.orgMemberID = getOrgMemberIDForPublication()>

				<cfset local.viewRFID = application.objSiteResource.getResourceFunctionIDbyResourceID(resourceTypeID=local.getDocument.resourceTypeID, functionName="view")>

				<cfquery name="local.getDocumentRights" datasource="#application.dsn.memberCentral.dsn#">
					set nocount on;

					declare @FID int, @groupPrintID int, @siteID int;
					SET @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.viewRFID#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.getDocument.siteID#">;
					SELECT @groupPrintID = groupPrintID FROM dbo.ams_members WHERE memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.orgMemberID#">;

					IF @groupPrintID IS NULL
						select @groupPrintID = o.publicGroupPrintID
						from dbo.organizations as o
						inner join dbo.sites as s on s.orgID = o.orgID 
							and s.siteID = @siteID;

					SELECT 
					CASE 
						WHEN EXISTS (
							SELECT gprp.groupRightPrintID
							FROM dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp
							INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp 
								ON gprp.siteID = @siteID
								AND gprp.rightPrintID = srfrp.rightPrintID
								AND gprp.groupPrintID = @groupPrintID
							WHERE srfrp.siteID = @siteID
							AND srfrp.siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.getDocument.siteResourceID#">
							AND srfrp.functionID = @FID
						)
						THEN 1
						ELSE 0
					END AS access;
				</cfquery>
				<cfset local.allowed = local.getDocumentRights.access>
			</cfif>

			<cfif local.documentFound eq 1 and local.allowed eq 1>
				<cfset application.objPlatformStats.recordDocHit(documentVersionID=local.getDocument.documentVersionID, allowed=1)>

				<cfset local.theFile = "#application.paths.RAIDSiteDocuments.path##local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#">
				<cfset local.keyMod = numberFormat(local.getdocument.documentVersionID mod 1000,"0000")>
				<cfset local.objectKey = lcase("sitedocuments/#local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.keyMod#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#")>

				<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.theFile, displayName=local.getDocument.fileName,
					forceDownload=0, s3bucket="membercentralcdn", s3objectKey=local.objectKey)>
				<cfif not local.docResult>
					<cfset arguments.event.setValue("mc_trigger404page",1)>
				</cfif>
			<cfelse>
				<cfset arguments.event.setValue("mc_trigger404page",1)>
			</cfif>
		<cfelse>
			<cfset arguments.event.setValue("mc_trigger404page",1)>
		</cfif>

		<cfreturn returnAppStruct(local.dataStruct,"")>
	</cffunction>

	<cffunction name="getOrgMemberIDForPublication" access="private" output="false" returntype="numeric">
		<cfreturn application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID)>
	</cffunction>

	<cffunction name="getInstanceSettings" access="private" output="false" returntype="struct">
		<cfargument name="applicationInstanceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryPublicationSettings" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select o.orgID, s.siteID, o.orgCode, s.siteCode, ai.siteresourceID, p.publicationID, 
				ai.applicationInstanceID, ai.applicationInstanceName, ai.applicationInstanceDesc,
				p.supportsEmailEditions, p.supportsOnlineEditions, p.supportsPDFEditions, p.emailConsentListID,
				ficu.featureImageConfigID, fics_arch.width as archiveFeatureImageWidth, fics_arch.height as archiveFeatureImageHeight,
				fics_arch.featureImageSizeID as archiveFeatureImageSizeID, fics_arch.fileExtension as archiveFeatureImageFileExt,
				isnull(p.onlineEditionPreviewableEditionCount,0) as onlineEditionPreviewableEditionCount, p.webHeaderTemplateIsFullWidth, p.webFooterTemplateIsFullWidth,
				pe_html.templateID as htmlEmailTemplateID, pe_text.templateID as textEmailTemplateID, 
				pw_head.templateID as webHeaderTemplateID, pw_home.templateID as webHomeContentTemplateID, 
				pw_foot.templateID as webFooterTemplateID, pw_side.templateID as webSidebarTemplateID,
				fiu.featureImageID
			from dbo.pub_publications as p
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID 
				and ai.applicationInstanceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.applicationInstanceID#">
			inner join dbo.sites as s on ai.siteID = s.siteID
			inner join dbo.organizations as o on s.orgID = o.orgID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
			left outer join dbo.cms_featuredImageConfigUsages as ficu on ficu.referenceID = p.publicationID
				and ficu.referenceType = 'publicationIssue'
			left outer join dbo.cms_featuredImageConfigUsagesAndSizes as ficus_arch
				inner join dbo.cms_featuredImageConfigSizes as fics_arch on fics_arch.featureImageSizeID = ficus_arch.featureImageSizeID
				on ficus_arch.featureImageConfigUsageID = ficu.featureImageConfigUsageID
				and ficus_arch.referenceType = 'publicationIssueArchive'
			left outer join dbo.cms_featuredImageUsages as fiu 
					inner join dbo.cms_featuredImages as fi on fi.featureImageID = fiu.featureImageID
				on fiu.featureImageConfigID = ficu.featureImageConfigID and fiu.referenceID = p.publicationID and fiu.referenceType = 'DefaultPublicationIssue'
	
			left outer join dbo.template_usages as pe_html on pe_html.referenceID = p.publicationID and pe_html.referenceType = 'PubEmailHtml'
			left outer join dbo.template_usages as pe_text on pe_text.referenceID = p.publicationID and pe_text.referenceType = 'PubEmailText'
			left outer join dbo.template_usages as pw_home on pw_home.referenceID = p.publicationID and pw_home.referenceType = 'PubWebHomepage'
			left outer join dbo.template_usages as pw_head on pw_head.referenceID = p.publicationID and pw_head.referenceType = 'PubWebHeader'
			left outer join dbo.template_usages as pw_foot on pw_foot.referenceID = p.publicationID and pw_foot.referenceType = 'PubWebFooter'
			left outer join dbo.template_usages as pw_side on pw_side.referenceID = p.publicationID and pw_side.referenceType = 'PubWebSidebar'
			where srs.siteResourceStatusDesc = 'Active'
			and (p.supportsOnlineEditions = 1 or p.supportsPDFEditions = 1);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.instanceSettings = structNew()>
		<cfloop list="#local.qryPublicationSettings.columnList#" index="local.thisField">
			<cfset local.instanceSettings[local.thisField] = local.qryPublicationSettings[local.thisField]>
		</cfloop>
		<cfset local.instanceSettings.baseQueryString = getBaseQueryString(false,true)>		
		
		<cfreturn local.instanceSettings>
	</cffunction>

	<cffunction name="getLatestPublishedIssue" access="private" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="yes">
		
		<cfset var qryPublishedIssues = "">

		<cfquery name="qryPublishedIssues" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT TOP 1 i.issueID
			FROM dbo.pub_issues as i
			INNER JOIN dbo.pub_volumes as v on v.volumeID = i.volumeID AND v.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			INNER JOIN dbo.pub_publications as p on p.publicationID = v.publicationID
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.pub_statuses AS s ON s.issueStatusID = i.issueStatusID
			WHERE s.statusName = 'Published'
			ORDER BY i.issueDate DESC;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryPublishedIssues>
	</cffunction>

	<cffunction name="getPublishedIssuesByVolumes" access="private" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="yes">
		<cfargument name="pos" type="numeric" required="no" default="1">
		<cfargument name="numEntries" type="numeric" required="no" default="10">
		
		<cfset var qryPublishedIssues = "">

		<cfquery name="qryPublishedIssues" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @publicationID INT, @totalCount INT, @numEntries INT, @pos INT, @featureImageConfigID INT;
			SET @publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;
			SET @pos = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pos#">;
			SET @numEntries = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.numEntries#">;

			SELECT @featureImageConfigID = featureImageConfigID
			FROM dbo.cms_featuredImageConfigUsages
			WHERE referenceID = @publicationID
			AND referenceType = 'publicationIssue';

			IF OBJECT_ID('tempdb..##tmpVolumes') IS NOT NULL 
				DROP TABLE ##tmpVolumes;

			SELECT v.volumeID, v.volumeName, MAX(i.issueDate) AS maxIssueDate, MIN(i.issueDate) AS minIssueDate, COUNT(i.issueID) AS issueCount, ROW_NUMBER() OVER (ORDER BY MAX(i.issueDate) DESC) AS [row]
			INTO ##tmpVolumes
			FROM dbo.pub_issues AS i
			INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID AND v.publicationID = @publicationID
			INNER JOIN dbo.pub_publications AS p ON p.publicationID = v.publicationID
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.pub_statuses AS s ON s.issueStatusID = i.issueStatusID AND s.statusName = 'Published'			
			GROUP BY v.volumeID, v.volumeName;

			SELECT @totalCount = @@ROWCOUNT;

			DELETE FROM ##tmpVolumes
			WHERE [row] NOT BETWEEN @pos AND (@pos + @numEntries - 1);

			SELECT i.issueID, i.issueNumber, i.issueTitle, i.issueType, i.htmlContentID, i.issueDate, i.pdfEditionDocumentID, tmp.volumeID, tmp.volumeName, 
				tmp.issueCount AS volumeIssuesCount, tmp.minIssueDate AS volumeMinIssueDate, tmp.maxIssueDate AS volumeMaxIssueDate, fiu.featureImageID,
				tmp.row, COUNT(it.itemSiteResourceID) AS itemCount, @totalCount AS totalCount 
			FROM ##tmpVolumes AS tmp 
			INNER JOIN dbo.pub_issues AS i ON i.volumeID = tmp.volumeID 
			INNER JOIN dbo.pub_statuses AS s ON s.issueStatusID = i.issueStatusID AND s.statusName = 'Published'
			LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu 
				INNER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
				ON fiu.featureImageConfigID = @featureImageConfigID
				AND fiu.referenceID = i.issueID
				AND fiu.referenceType = 'publicationIssue'
			LEFT OUTER JOIN dbo.pub_issueItems AS it ON it.issueID = i.issueID
			GROUP BY i.issueID, i.issueNumber, i.issueTitle, i.issueType, i.htmlContentID, i.issueDate, i.pdfEditionDocumentID, tmp.volumeID, tmp.volumeName, 
				tmp.issueCount, tmp.minIssueDate, tmp.maxIssueDate, fiu.featureImageID, tmp.row
			ORDER BY tmp.[row], i.issueDate DESC;

			IF OBJECT_ID('tempdb..##tmpVolumes') IS NOT NULL 
				DROP TABLE ##tmpVolumes;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>	
		
		<cfreturn qryPublishedIssues>
	</cffunction>

	<cffunction name="getPreviewableIssues" access="private" output="false" returntype="query">
		<cfargument name="publicationID" type="numeric" required="yes">
		<cfargument name="numEntries" type="numeric" required="yes">
		
		<cfset var qryPublishedIssues = "">

		<cfquery name="qryPreviewableIssues" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @publicationID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">;

			WITH recentVolumes AS (
				SELECT v.volumeID, ROW_NUMBER() OVER (ORDER BY MAX(i.issueDate) DESC) AS [row]
				FROM dbo.pub_issues AS i
				INNER JOIN dbo.pub_volumes AS v ON v.volumeID = i.volumeID AND v.publicationID = @publicationID
				INNER JOIN dbo.pub_publications AS p ON p.publicationID = v.publicationID
				INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
					AND srs.siteResourceStatusDesc = 'Active'
				INNER JOIN dbo.pub_statuses AS s ON s.issueStatusID = i.issueStatusID AND s.statusName = 'Published'			
				GROUP BY v.volumeID
			)
			SELECT TOP #arguments.numEntries# i.issueID
			FROM recentVolumes AS tmp
			INNER JOIN dbo.pub_issues AS i ON i.volumeID = tmp.volumeID
			INNER JOIN dbo.pub_statuses AS s ON s.issueStatusID = i.issueStatusID AND s.statusName = 'Published'
			ORDER BY tmp.[row], i.issueDate DESC;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>	
		
		<cfreturn qryPreviewableIssues>
	</cffunction>

	<cffunction name="isValidPublicationIssue" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="publicationID" type="numeric" required="yes">
		<cfargument name="issueID" type="numeric" required="yes">
		<cfargument name="issueItemID" type="numeric" required="yes">
		
		<cfset var qryPubIssue = "">

		<cfquery name="qryPubIssue" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select i.issueID
			from dbo.pub_publications as p
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = p.applicationInstanceID
				and p.publicationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.publicationID#">
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.pub_volumes as v on v.publicationID = p.publicationID
			inner join dbo.pub_issues as i on i.volumeID = v.volumeID
				and i.issueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueID#">
			<cfif arguments.issueItemID gt 0>
				inner join dbo.pub_issueItems as it on it.issueID = i.issueID
					and it.issueItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueItemID#">
			</cfif>
			inner join dbo.sites as s on s.siteID = ai.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
			where s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryPubIssue.recordCount is 1>
	</cffunction>

	<cffunction name="getPublicationTemplateDetails" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="webHeaderTemplateID" type="numeric" required="true">
		<cfargument name="webHomeContentTemplateID" type="numeric" required="true">
		<cfargument name="webSidebarTemplateID" type="numeric" required="true">
		<cfargument name="webFooterTemplateID" type="numeric" required="true">

		<cfset var qryTemplateDetails = "">

		<cfquery name="qryTemplateDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @resourceTypeID int, @webHeaderTemplateID int, @webHomeContentTemplateID int, @webSidebarTemplateID int, @webFooterTemplateID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SELECT @resourceTypeID = dbo.fn_getResourceTypeID('publicationAdmin');
			SET @webHeaderTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webHeaderTemplateID#">;
			SET @webHomeContentTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webHomeContentTemplateID#">;
			SET @webSidebarTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webSidebarTemplateID#">;
			SET @webFooterTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.webFooterTemplateID#">;

			select tt.templateTypeCode, tf.templateFormat, t.templateName, templateContent.rawContent as templateContent, 'header' as templateMode
			from dbo.template_templates as t
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = t.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.template_typeFormats as ttf on ttf.templateTypeFormatID = t.templateTypeFormatID
				and ttf.editorID = t.editorID
			inner join dbo.template_types as tt on tt.templateTypeID = ttf.templateTypeID
			inner join dbo.template_formats as tf on tf.templateFormatID = ttf.templateFormatID
				and tt.controllingSiteResourceTypeID = @resourceTypeID
			inner join dbo.template_libraries as tl on tl.libraryID = t.libraryID
			left outer join dbo.networks as n
				inner join dbo.networkSites ns on ns.networkID = n.networkID
					and ns.siteID = @siteID
				on n.networkID = tl.syndicationNetworkID
			cross apply dbo.fn_getContent(t.contentID,1) as templateContent
			where (t.siteID = @siteID or ns.siteID is not null)
			and t.templateID = @webHeaderTemplateID
				union
			select tt.templateTypeCode, tf.templateFormat, t.templateName, templateContent.rawContent as templateContent, 'body' as templateMode
			from dbo.template_templates as t
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = t.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.template_typeFormats as ttf on ttf.templateTypeFormatID = t.templateTypeFormatID
				and ttf.editorID = t.editorID
			inner join dbo.template_types as tt on tt.templateTypeID = ttf.templateTypeID
			inner join dbo.template_formats as tf on tf.templateFormatID = ttf.templateFormatID
				and tt.controllingSiteResourceTypeID = @resourceTypeID
			inner join dbo.template_libraries as tl on tl.libraryID = t.libraryID
			left outer join dbo.networks as n
				inner join dbo.networkSites ns on ns.networkID = n.networkID
					and ns.siteID = @siteID
				on n.networkID = tl.syndicationNetworkID
			cross apply dbo.fn_getContent(t.contentID,1) as templateContent
			where (t.siteID = @siteID or ns.siteID is not null)
			and t.templateID = @webHomeContentTemplateID
				union
			select tt.templateTypeCode, tf.templateFormat, t.templateName, templateContent.rawContent as templateContent, 'sidebar' as templateMode
			from dbo.template_templates as t
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = t.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.template_typeFormats as ttf on ttf.templateTypeFormatID = t.templateTypeFormatID
				and ttf.editorID = t.editorID
			inner join dbo.template_types as tt on tt.templateTypeID = ttf.templateTypeID
			inner join dbo.template_formats as tf on tf.templateFormatID = ttf.templateFormatID
				and tt.controllingSiteResourceTypeID = @resourceTypeID
			inner join dbo.template_libraries as tl on tl.libraryID = t.libraryID
			left outer join dbo.networks as n
				inner join dbo.networkSites ns on ns.networkID = n.networkID
					and ns.siteID = @siteID
				on n.networkID = tl.syndicationNetworkID
			cross apply dbo.fn_getContent(t.contentID,1) as templateContent
			where (t.siteID = @siteID or ns.siteID is not null)
			and t.templateID = @webSidebarTemplateID
				union
			select tt.templateTypeCode, tf.templateFormat, t.templateName, templateContent.rawContent as templateContent, 'footer' as templateMode
			from dbo.template_templates as t
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = t.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.template_typeFormats as ttf on ttf.templateTypeFormatID = t.templateTypeFormatID
				and ttf.editorID = t.editorID
			inner join dbo.template_types as tt on tt.templateTypeID = ttf.templateTypeID
			inner join dbo.template_formats as tf on tf.templateFormatID = ttf.templateFormatID
				and tt.controllingSiteResourceTypeID = @resourceTypeID
			inner join dbo.template_libraries as tl on tl.libraryID = t.libraryID
			left outer join dbo.networks as n
				inner join dbo.networkSites ns on ns.networkID = n.networkID
					and ns.siteID = @siteID
				on n.networkID = tl.syndicationNetworkID
			cross apply dbo.fn_getContent(t.contentID,1) as templateContent
			where (t.siteID = @siteID or ns.siteID is not null)
			and t.templateID = @webFooterTemplateID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryTemplateDetails>
	</cffunction>

	<cffunction name="getPublicationIssueItemDetails" access="private" output="false" returntype="query">
		<cfargument name="issueItemID" type="numeric" required="true">
	
		<cfset var qryIssueItemDetails = "">

		<cfquery name="qryIssueItemDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @issueItemID int, @memberID int;
			SET @issueItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.issueItemID#">;

			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;
			<cfelse>
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID)#">;
			</cfif>
			
			select be.blogEntryID, be.blogID, be.siteResourceID as entrySiteResourceID, sr.resourceTypeID, be.blogTitle, be.dateCreated, be.articleDate, be.postDate, be.expirationDate, be.isSticky,
				be.blogURL, bs.statusName, b_ai.applicationInstanceID, b_ai.siteResourceID as blogSiteResourceID, bl.blogAuthorSupport, bl.showPostedBy, bl.showPhoto, bl.showSummary, bl.showPostedOnDate,
				entryContent.rawContent as mainContent, entrySummaryContent.rawContent as summaryContent, afterPostContent.rawContent as afterPostContent,
				case when it.sourceIssueItemID is not null and p_ai.siteID <> b_ai.siteID then 1 else 0 end as isCrossSiteArticle,
				authors = stuff((
							SELECT '^~~~^' + cast(mactive.memberID as varchar(10)) + '|' + mActive.firstname + '|' + mActive.lastname + '|' + mActive.memberNumber + '|' + cast(mactive.hasMemberPhotoThumb as varchar(1))
							from dbo.bl_authors as ba
							inner join dbo.ams_members as m on m.memberID = ba.memberID and m.orgID = s.orgID
							inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
							where ba.blogEntryID = be.blogEntryID
							FOR XML PATH ('')
					),1,5,''), 
				authorMemberIDList = stuff((
							SELECT ',' + cast(mactive.memberID as varchar(10))
							from dbo.bl_authors as ba
							inner join dbo.ams_members as m on m.memberID = ba.memberID and m.orgID = s.orgID
							inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
							where ba.blogEntryID = be.blogEntryID
							FOR XML PATH ('')
					),1,1,''),
					ficu.featureImageConfigID as issueItemFeatureImageConfigID, 
					fiu.featureImageID as issueItemFeatureImageID, 
					p.featuredImageLocation,
					ficus.featureImageSizeID as issueItemFeatureImageSizeID,
					fics_be.fileExtension as issueItemFeatureImageFileExt
			from dbo.pub_issueItems as it 
			inner join dbo.pub_issues as i on i.issueID = it.issueID
			inner join dbo.pub_volumes as v on v.volumeID = i.volumeID
			inner join dbo.pub_publications as p on p.publicationID = v.publicationID
			inner join dbo.cms_applicationInstances as p_ai on p_ai.applicationInstanceID = p.applicationInstanceID
			inner join dbo.bl_entry as be on be.siteResourceID = it.itemSiteResourceID
				and it.issueItemID = @issueItemID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = be.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.bl_statuses as bs on bs.statusID = be.statusID
			inner join dbo.bl_blog as bl on bl.blogID = be.blogID
			inner join dbo.cms_applicationInstances as b_ai on b_ai.applicationInstanceID = bl.applicationInstanceID
			inner join dbo.sites as s on s.siteID = b_ai.siteID
			inner join dbo.cms_siteResources as sr2 on sr2.siteResourceID = b_ai.siteResourceID
				and sr2.siteResourceStatusID = 1
			cross apply dbo.fn_getContent(be.blogContentID,1) as entryContent
			cross apply dbo.fn_getContent(be.summaryContentID,1) as entrySummaryContent
			cross apply dbo.fn_getContent(bl.afterPostContentID,1) as afterPostContent


			LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu on ficu.referenceID = p.publicationID AND ficu.referenceType = 'publicationIssueItem'
					LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu on fiu.referenceID = it.issueItemID AND fiu.referenceType = 'publicationIssueItem'
						AND fiu.featureImageConfigID = ficu.featureImageConfigID
			left outer join dbo.cms_featuredImageConfigUsagesAndSizes as ficus 
				inner join dbo.cms_featuredImageConfigSizes as fics_be on fics_be.featureImageSizeID = ficus.featureImageSizeID
			on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
				and ficus.referenceType = 'publicationIssueItem'
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryIssueItemDetails>
	</cffunction>

	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">
		<cfargument name="returnToAppAdmin" type="boolean" required="false" default="0">

		<cfset var local = structNew()>

		<cfsetting requesttimeout="300">

		<cfscript>
			// SET EVENT SPECIFICATION ----------------------------------------------
			local.appInfo = arguments.appInfo;
			arguments.event.paramValue('appTypeID','0');
			// LOAD OBJECTS ---------------------------------------------------------
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			// call the contruct to do all the page validation and form params ------			
			contructAppInstanceForm(arguments.event,local.appInfo);
		</cfscript>

		<cfif cgi.request_method eq "POST" AND NOT arguments.event.getValue('error.formErrors')>
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
					SET NOCOUNT ON;
				
					DECLARE @siteID	int, @languageID int, @sectionID int, @isVisible bit, @pageName varchar(50), @pageTitle varchar(200), 
						@pagedesc varchar(400), @zoneID int, @pageTemplateID int, @pageModeID int, @pgResourceTypeID int,
						@pgParentResourceID int, @allowReturnAfterLogin bit, @applicationInstanceName varchar(100), @applicationInstanceDesc varchar(200),
						@applicationInstanceID int, @siteResourceID int, @pageID int, @masterSiteID int;
					
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					SET @languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('lid')#">;
					SET @isVisible = 1;
					SELECT @pageName = dbo.fn_regexReplace(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('pageName')#">,'[^A-Z0-9\-]+','');
					SET @pageTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('pageTitle')#">;
					SET @pagedesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('pageDesc')#">;
					SET @pageTemplateID = NULL;
					SELECT @zoneID = dbo.fn_getZoneID('Main');
					SET @pageModeID = <cfif arguments.event.getValue('pageModeID','0') EQ 0>NULL<cfelse><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pageModeID')#"></cfif>;
					SET @allowReturnAfterLogin = 1;
					SET @applicationInstanceName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('appInstanceName')#">;
					SET @applicationInstanceDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('appInstanceDesc')#">;
					SET @sectionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sectionID')#">;
					SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage');
					SET @pgParentResourceID = NULL;

					EXEC dbo.cms_createApplicationInstancePublication @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @isVisible=@isVisible,
							@pageName=@pageName, @pageTitle=@pageTitle, @pagedesc=@pagedesc, @zoneID=@zoneID, @pageTemplateID=@pageTemplateID,
							@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@pgParentResourceID,
							@allowReturnAfterLogin=@allowReturnAfterLogin, @applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc,
							@applicationInstanceID=@applicationInstanceID OUTPUT, @siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT;
	
					SELECT @applicationInstanceID as applicationInstanceID, @siteResourceID as siteResourceID, @pageID as pageID;
				</cfquery>
				<cfset local.message = 1>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.message = 2>
				</cfcatch>
			</cftry>
			<cfif arguments.returnToAppAdmin>
				<cfoutput>
					<script language="javascript">
						top.reloadPublicationTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			<cfelse>
				<cfoutput>
					<script language="javascript">
						top.reloadPageTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			</cfif>	
		<cfelse>
			<cfoutput>
				<!--- CAN PRE SET DEFAULT VARIABLES FOR FORM --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getMode">
					select dbo.fn_getmodeId('Full') as pageModeID
				</cfquery>
				<cfset arguments.event.setValue('pageModeID',local.getMode.pageModeID)>
				<cfset showAppInstanceForm(arguments.event,local.appInfo)>
			</cfoutput>
		</cfif>
	</cffunction>
</cfcomponent>