ALTER PROC dbo.sw_getBundleByBundleID
@bundleID INT,
@orgcode VARCHAR(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT TOP 1 b.bundleID, b.isSWOD, p.orgcode, b.bundleName, b.bundleSubTitle, b.bundleDesc, b.programCode, b.isPriceBasedOnActual,
		b.allowSyndication, b.priceSyndication, b.dateCatalogStart, b.dateCatalogEnd, b.[status], b.includeSpeakers, p.orgcode AS publisherOrgCode,
		b.revenueGLAccountID, b.pushDefaultPricingToOptIns, b.allowOptInRateChange, p.handlesOwnPayment, b.freeRateDisplay, b.dateActivated, b.lockSettings,
		b.customTextEnabled, b.customTextContent,
		mActive.memberID as submittedByMemberID, mActive.firstname AS submitterFirstName, mActive.firstname + ' ' + mActive.lastname AS submittedByMember, me.email as submittedByEmail, mcs.siteID AS participantSiteID
	FROM dbo.tblBundles AS b 
	INNER JOIN dbo.tblParticipants AS p ON b.participantID = p.participantID
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	LEFT JOIN memberCentral.dbo.ams_members as m on m.memberID = b.submittedByMemberID
	LEFT JOIN memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	LEFT JOIN memberCentral.dbo.ams_memberEmails as me on me.memberID = mActive.memberID AND me.orgID in (mcs.orgID,1)
	LEFT JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = m.orgID AND metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
	LEFT JOIN memberCentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m.orgID AND metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.[State] = p.orgcode
	WHERE b.bundleID = @bundleID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
