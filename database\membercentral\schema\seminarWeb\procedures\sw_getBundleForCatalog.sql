ALTER PROC dbo.sw_getBundleForCatalog
@bundleID int,
@catalogOrgCode varchar(10),
@billingState varchar(5),
@billingZip varchar(15),
@MCMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	DECLARE @publisherSiteID INT, @dateStart DATETIME, @dateEnd DATETIME, @bundleOrderID INT;

	SELECT @publisherSiteID = mcs.siteID
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = b.participantID
	INNER JOIN memberCentral.dbo.sites AS mcs ON mcs.siteCode = p.orgcode
	WHERE b.bundleID = @bundleID;

	IF EXISTS(SELECT 1 FROM dbo.tblBundles WHERE bundleID = @bundleID AND isSWOD = 0)
		SELECT @dateStart = MIN(swl.dateStart), @dateEnd = MAX(swl.dateEnd)
		FROM dbo.tblBundledItems AS bi
		INNER JOIN dbo.tblSeminars AS s ON s.seminarID = bi.seminarID AND s.isPublished = 1 AND s.isDeleted = 0
		INNER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = bi.seminarID
		WHERE bi.bundleID = @bundleID;

	SET @bundleOrderID = dbo.fn_getBundleOrderIDForBundle(@bundleID, @MCMemberID);

	-- qry1: get bundle
	SELECT TOP 1 b.bundleID, b.bundleName, b.bundleSubTitle, b.programCode, b.bundleDesc, b.isSWOD, tla.[description], p.showUSD, p.orgcode AS publisherOrgCode,
		CASE WHEN @bundleOrderID > 0 THEN 1 ELSE 0 END as isRegistered, 
		b.isPriceBasedOnActual, b.includeSpeakers, b.freeRateDisplay, b.[status], fiu.featureImageID, 
		fics.fileExtension AS featureImageFileExtension, ficus.featureImageSizeID,
		@dateStart AS dspStartDate, @dateEnd AS dspEndDate, b.customTextEnabled, b.customTextContent
	FROM dbo.tblBundles AS b
	INNER JOIN dbo.tblParticipants AS p ON b.participantID = p.participantID
	INNER JOIN trialsmith.dbo.depotla AS tla ON tla.[state] = p.orgcode
	INNER JOIN dbo.swb_BundlesInMyCatalog(@catalogOrgCode) bmc on bmc.bundleID = b.bundleID
	INNER JOIN memberCentral.dbo.sites AS sites ON sites.siteCode = p.orgCode
	LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = sites.siteID
		AND ficu.referenceType = 'swProgram'
	LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageUsages AS fiu 
		INNER JOIN memberCentral.dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
		ON fiu.featureImageConfigID = ficu.featureImageConfigID 
		AND fiu.referenceID = b.bundleID
		AND fiu.referenceType = 'swbProgram'
	LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsagesAndSizes AS ficus ON ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
		AND ficus.referenceType = 'swProgramDetail'
	LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigSizes AS fics ON fics.featureImageSizeID = ficus.featureImageSizeID
	WHERE b.bundleID = @bundleID;

	-- qry2: get bundle prices for detail page
	EXEC dbo.swb_getPricesForCatalog @bundleID=@bundleID, @catalogOrgCode=@catalogOrgCode, @billingstate=@billingstate, @billingZip=@billingZip;

	-- qry3: get bundle prices for buy now pages
	EXEC dbo.swb_getPricesForBuyNow @bundleID=@bundleID, @catalogOrgCode=@catalogOrgCode, @billingstate=@billingstate, @billingZip=@billingZip, @memberID=@MCMemberID;

	-- qry4: program objectives
	EXEC dbo.sw_getLearningObjectivesByProgramID @programType='SWB', @programID=@bundleID;

	-- qry5: program sponsors
	EXEC memberCentral.dbo.sponsors_getSponsorsByReferenceIDFull @siteID=@publisherSiteID, @referenceType='swbProgram', @referenceID=@bundleID;
	
	-- qry6: program optIn participants
	EXEC dbo.sw_getParticipantsOptedIntoBundle @bundleID = @bundleID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
