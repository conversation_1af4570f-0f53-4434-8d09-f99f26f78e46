USE membercentral
GO

ALTER PROC dbo.ams_exportMemberDataTemplate
@orgID int,
@csvfilename varchar(400),
@isPartial bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @sql varchar(max), @orgCode varchar(10), @hasPrefix bit, @hasMiddleName bit,  
		@hasSuffix bit, @hasProfessionalSuffix bit, @WTSelectList varchar(max),
		@ETSelectList varchar(max), @minATID int, @minAT varchar(20), @hasAttn bit, @hasAddress2 bit, 
		@hasAddress3 bit, @hasCounty bit, @ATSelectList varchar(max), @PTSelectList varchar(max), 
		@PLTSelectList varchar(max), @MDSelectList varchar(max), @showRecordType bit, 
		@AddrTagSelectList varchar(max), @EmlTagSelectList varchar(max);
	
	select @orgcode=orgcode, @hasPrefix=hasPrefix, @hasMiddleName=hasMiddleName,
		@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix
	from dbo.organizations 
	where orgID = @orgid;
	
	IF OBJECT_ID('tempdb..##tmpImportMemberTemplate' + @orgCode) IS NOT NULL 
		EXEC('DROP TABLE ##tmpImportMemberTemplate' + @orgCode);

	-- loop over websitetypes
	select @WTSelectList = COALESCE(@WTSelectList + ', ', '') + '''Optional'' as ' + quotename(websiteType)
	from dbo.ams_memberWebsiteTypes 
	where orgid = @orgid
	order by websiteTypeOrder;
	select @WTSelectList = isnull(@WTSelectList,'');

	-- loop over emailtypes
	select @ETSelectList = COALESCE(@ETSelectList + ', ', '') + '''Optional'' as ' + quotename(emailType)
	from dbo.ams_memberEmailTypes 
	where orgid = @orgid
	order by emailTypeOrder;
	select @ETSelectList = isnull(@ETSelectList,'');

	-- email tags
	select @EmlTagSelectList = COALESCE(@EmlTagSelectList + ', ', '') + '''Optional'' as ' + quotename(met.emailTagType + 'EmailType')
	from dbo.ams_memberEmailTagTypes as met
	where met.orgid = @orgid
	order by met.emailTagTypeOrder;
	select @EmlTagSelectList = isnull(@EmlTagSelectList,'');

	-- loop over address types and phone types and district types
	set @ATSelectList = '';
	select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID;
	while @minATID is not null BEGIN
		select @minAT=addressType, @hasAttn=hasAttn, @hasAddress2=hasAddress2, @hasAddress3=hasAddress3, @hasCounty=hasCounty
		from dbo.ams_memberAddressTypes 
		where addressTypeID = @minATID;

		set @ATSelectList = @ATSelectList +
			case when @hasAttn = 1 then ', ''Optional'' as ' + quotename(@minAT + '_attn') else '' end +
			', ''Optional'' as ' + quotename(@minAT + '_address1') +
			case when @hasAddress2 = 1 then ', ''Optional'' as ' + quotename(@minAT + '_address2') else '' end +
			case when @hasAddress3 = 1 then ', ''Optional'' as ' + quotename(@minAT + '_address3') else '' end +
			', ''Optional'' as ' + quotename(@minAT + '_city') +
			', ''Optional'' as ' + quotename(@minAT + '_stateprov') +
			', ''Optional'' as ' + quotename(@minAT + '_postalCode') +
			case when @hasCounty = 1 then ', ''Optional'' as ' + quotename(@minAT + '_county') else '' end +
			', ''Optional'' as ' + quotename(@minAT + '_country');
		
		set @PTSelectList = '';
		select @PTSelectList = COALESCE(@PTSelectList + ', ', '') + '''Optional'' as ' + quotename(@minAT + '_' + phoneType)
		from dbo.ams_memberPhoneTypes 
		where orgid = @orgid
		order by phoneTypeOrder;

		select @PTSelectList = isnull(@PTSelectList,'');
		set @ATSelectList = @ATSelectList + @PTSelectList;

		select @minATID = min(addressTypeID) from dbo.ams_memberAddressTypes where orgID = @orgID and addressTypeID > @minATID;
	END

	-- address tags
	select @AddrTagSelectList = COALESCE(@AddrTagSelectList + ', ', '') + '''Optional'' as ' + quotename(mat.addressTagType + 'AddressType')
	from dbo.ams_memberAddressTagTypes as mat
	where mat.orgid = @orgid
	order by mat.addressTagTypeOrder;
	select @AddrTagSelectList = isnull(@AddrTagSelectList,'');
	
	-- professional license types
	select @PLTSelectList = COALESCE(@PLTSelectList + ', ', '') + 
		'''Optional'' as ' + quotename(PLName + '_licenseNumber') + ', ' + 
		'''Optional'' as ' + quotename(PLName + '_activeDate') + ', ' + 
		'''Optional'' as ' + quotename(PLName + '_status')
	from dbo.ams_memberProfessionalLicenseTypes
	where orgid = @orgid
	order by PLName;
	select @PLTSelectList = isnull(@PLTSelectList,'');

	-- loop over member data columns 
	select @MDSelectList = COALESCE(@MDSelectList + ', ', '') + '''Optional'' as ' + quotename(mdc.columnName)
		from dbo.ams_memberDataColumns as mdc
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
		where mdc.orgid = @orgid
		and mdc.isReadOnly = 0
		and dt.dataTypeCode <> 'DOCUMENTOBJ'
		order by mdc.columnName;
	select @MDSelectList = isnull(@MDSelectList,'');

	-- include recordType
	IF EXISTS (select 1 from dbo.ams_recordTypes where orgID = @orgid)
		set @showRecordType = 1;
	ELSE
		set @showRecordType = 0;
	
	select @sql = 'select ''Req'' as MemberNumber' + 
	case when @isPartial = 1 then '' else ', ''Optional'' as NewMemberNumber' end +
		case when @hasPrefix = 1 then ', ''Optional'' as Prefix' else '' end + 
		', ''Req for new members'' as FirstName' + 
		case when @hasMiddleName = 1 then ', ''Optional'' as MiddleName' else '' end + 
		', ''Req for new members'' as LastName' + 
		case when @hasSuffix = 1 then ', ''Optional'' as Suffix' else '' end + 
		case when @hasProfessionalSuffix = 1 then ', ''Optional'' as ProfessionalSuffix' else '' end +
		', ''Optional'' as Company'  +
		', ''Optional'' as MCAccountType' + 
		', ''Optional'' as MCAccountStatus' + 
		case when @showRecordType = 1 then ', ''Optional'' as MCRecordType' else '' end + 
		case when len(@WTSelectList) > 0 then ', ' + @WTSelectList else '' end +
		case when len(@ETSelectList) > 0 then ', ' + @ETSelectList else '' end +
		case when len(@EmlTagSelectList) > 0 then ', ' + @EmlTagSelectList else '' end +
		case when len(@ATSelectList) > 0 then @ATSelectList else '' end +
		case when len(@AddrTagSelectList) > 0 then ', ' + @AddrTagSelectList else '' end +
		case when len(@PLTSelectList) > 0 then ', ' + @PLTSelectList else '' end +
		case when len(@MDSelectList) > 0 then ', ' + @MDSelectList else '' end + 
		'into ##tmpImportMemberTemplate' + @orgCode;
	exec(@sql);

	set @sql = 'SELECT *, ROW_NUMBER() OVER(order by memberNumber) as mcCSVorder *FROM* ##tmpImportMemberTemplate' + @orgCode;
	
	-- try the export with all columns.
	BEGIN TRY
		EXEC dbo.up_queryToCSV @selectsql=@sql, @csvfilename=@csvfilename, @returnColumns=0;
	END TRY
	BEGIN CATCH
		-- if too many columns, then try it without prof license columns
		IF len(@PLTSelectList) > 0 AND CHARINDEX('Query too large to export',ERROR_MESSAGE()) > 0 BEGIN
			IF OBJECT_ID('tempdb..##tmpImportMemberTemplate' + @orgCode) IS NOT NULL 
				EXEC('DROP TABLE ##tmpImportMemberTemplate' + @orgCode);
			select @sql = 'select ''Req'' as MemberNumber' + 
				case when @isPartial = 1 then '' else ', ''Optional'' as NewMemberNumber' end +
				case when @hasPrefix = 1 then ', ''Optional'' as Prefix' else '' end + 
				', ''Req for new members'' as FirstName' + 
				case when @hasMiddleName = 1 then ', ''Optional'' as MiddleName' else '' end + 
				', ''Req for new members'' as LastName' + 
				case when @hasSuffix = 1 then ', ''Optional'' as Suffix' else '' end + 
				case when @hasProfessionalSuffix = 1 then ', ''Optional'' as ProfessionalSuffix' else '' end +
				', ''Optional'' as Company' +
				', ''Optional'' as MCAccountType' + 
				', ''Optional'' as MCAccountStatus' + 
				case when @showRecordType = 1 then ', ''Optional'' as MCRecordType' else '' end + 
				case when len(@WTSelectList) > 0 then ', ' + @WTSelectList else '' end +
				case when len(@ETSelectList) > 0 then ', ' + @ETSelectList else '' end +
				case when len(@EmlTagSelectList) > 0 then ', ' + @EmlTagSelectList else '' end +
				case when len(@ATSelectList) > 0 then @ATSelectList else '' end +
				case when len(@AddrTagSelectList) > 0 then ', ' + @AddrTagSelectList else '' end +
				case when len(@MDSelectList) > 0 then ', ' + @MDSelectList else '' end + 
				'into ##tmpImportMemberTemplate' + @orgCode;
			exec(@sql);

			set @sql = 'SELECT *, ROW_NUMBER() OVER(order by memberNumber) as mcCSVorder *FROM* ##tmpImportMemberTemplate' + @orgCode;
			EXEC dbo.up_queryToCSV @selectsql=@sql, @csvfilename=@csvfilename, @returnColumns=0;
		END ELSE 
			THROW;
	END CATCH

	IF OBJECT_ID('tempdb..##tmpImportMemberTemplate' + @orgCode) IS NOT NULL 
		EXEC('DROP TABLE ##tmpImportMemberTemplate' + @orgCode);
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
