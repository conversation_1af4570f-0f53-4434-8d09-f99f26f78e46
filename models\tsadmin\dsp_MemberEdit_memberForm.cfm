<cfsavecontent variable="local.memberFormJS">
<cfoutput>
<script type='text/javascript' src='/javascripts/membercentral/jquery-1.8.3.min.js'></script>
<script type='text/javascript' src='/javascripts/membercentral/colorbox/jquery.colorbox-min-1.4.19.js'></script>
<script type="text/javascript">
function clearAffilDivs() {
	if (document.getElementById('affilToNewDiv')) document.getElementById('affilToNewDiv').style.display = 'none';
	if (document.getElementById('convertAIWDiv')) document.getElementById('convertAIWDiv').style.display = 'none';
}

// convert AIW
function fnConvertAIW() {
	clearAffilDivs();
	<cfif arguments.strMemberInit.MCSitesAIW.recordcount is 1>
		var msg='Are you sure you want to convert this account in waiting?';
		if (confirm(msg)) {
			var theform = document.getElementById('frmMember');
			theform.AIWsiteid.value = '#arguments.strMemberInit.MCSitesAIW.siteid#';
			submitForm('convertAIW');
		} else return false;
	<cfelse>
		document.getElementById('convertAIWDiv').style.display='';
		document.getElementById('frmMember').AIWsiteid.focus();
	</cfif>
}

function validateForm(theForm) {
	if (theForm.FNAME.value == "")
	{
	alert("Please enter a value for the \"First Name\" field.");
	theForm.FNAME.focus();
	return (false);
	}
	if (theForm.LNAME.value == "")
	{
	alert("Please enter a value for the \"Last Name\" field.");
	theForm.LNAME.focus();
	return (false);
	}

	if (theForm.ICITY.value == "")
	{
	alert("Please enter a value for the \"City\" field.");
	theForm.ICITY.focus();
	return (false);
	}

	if (theForm.IST.value == "")
	{
	alert("Please enter a value for the \"State\" field.");
	theForm.IST.focus();
	return (false);
	}
	if (theForm.IZIPCODE.value == "")
	{
	alert("Please enter a value for the \"ZIP\" field.");
	theForm.IZIPCODE.focus();
	return (false);
	}
	if (theForm.ICOUNTRY.value == "")
	{
	alert("Please enter a value for the \"Country\" field.");
	theForm.ICOUNTRY.focus();
	return (false);
	}
	if (theForm.IRECTYPE.value == "")
	{
	alert("Please enter a value for the \"Main Association\" field.");
	theForm.IRECTYPE.focus();
	return (false);
	}

	return (true);
}
// when approving an account awaiting approval, set mailcode to A and Pending to 1
function fnapproveAccount() {
	var theform = document.getElementById('frmMember');
	theform.Pending.selectedIndex = 2;
	theform.IMAILCODE.selectedIndex = 0;
	submitForm('approveAccount');
}
// when denying an account awaiting approval, set mailcode to I and Pending to 0
function fndenyAccount() {
	var theform = document.getElementById('frmMember');
	theform.Pending.selectedIndex = 2;
	theform.IMAILCODE.selectedIndex = 1;
	submitForm('denyAccount');
}
// when merging an account
function fnmergeAccount() {
	var theform = document.getElementById('frmMember');
	var mergeChecked = false;
	if (theform.frmMergeDepoID.length) {
		for (i=0; i<theform.frmMergeDepoID.length; i++) {
			if (theform.frmMergeDepoID[i].checked) {
				mergeChecked = true;
			}
		}
	} else if (theform.frmMergeDepoID.checked) {
		mergeChecked = true;
	}
	if (!mergeChecked) {
		alert("Check at least one account to compare to the main account.");
		theForm.frmMergeDepoID[0].focus();
		return (false);
	}
	submitForm('mergeAccounts');
}
// when adding MC affiliation
function fnAddMCAffiliation() {
	var theform = document.getElementById('frmMember');
	if (theform.mc_memnum.value == "") {
		alert("Enter a valid member number for this affiliation.");
		theform.mc_memnum.focus();
		return (false);
	}
	if (theform.mc_siteid.options[theform.mc_siteid.selectedIndex].value == "") {
		alert("Select a valid website for this affiliation.");
		return (false);
	}
	submitForm('addMCAffiliation');
}
function submitForm(buttonname) {
	document.getElementById('memAction').value = buttonname;
	if (validateForm(document.getElementById('frmMember'))) {
		if (buttonname == 'zendeskTSCU') {
			document.getElementById('frmMember').target="_blank";	
		}
		document.getElementById('frmMember').submit();
		setTimeout(function(){document.getElementById('frmMember').target="_self";},100);
	}
}

function loginAsMember(sitecode,mn) {
	launchCus('MemberEdit.cfm?memAction=loginAsMember&nobanner=1&depomemberdataid=#val(arguments.strMemberInit.qdepoMemberData.depomemberdataid)#&lamSite=' + sitecode + '&lamMN=' + mn,520,260);
}
function zendeskSSO(sitecode,mn) {
	window.open('MemberEdit.cfm?memAction=zendeskSSO&nobanner=1&depomemberdataid=#val(arguments.strMemberInit.qdepoMemberData.depomemberdataid)#&lamSite=' + sitecode + '&lamMN=' + mn);
}
function cloneAccount() {
	$.colorbox( {onCleanup:closeBox, innerWidth:500, html:$('##cloneConfirmationTemplate').html(), overlayClose:false} );
}
function doCloneAccount() {
	$('##btnCloneAcct').prop('disabled',true).html('Please wait...');
	self.location.href = 'MemberEdit.cfm?memAction=cloneAccount&nobanner=1&depomemberdataid=#val(arguments.strMemberInit.qdepoMemberData.depomemberdataid)#';
}
function closeBox() { $.colorbox.close(); }
</script>
<link type='text/css' rel='stylesheet' href='/javascripts/membercentral/colorbox/colorbox-1.4.19.css'>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.memberFormJS#">

<!--- Crumb and title --->
<cfoutput>
<div id="crumbs">
	You are here: <a href="adm_depomenu.cfm">Admin menu</a> \
	<a href="MemberFind.cfm">Find Member Account</a> \
	Member Account: #arguments.strMemberInit.qdepoMemberData.FirstName# #arguments.strMemberInit.qdepoMemberData.LastName#
</div>
<cfif arguments.strMemberInit.qryMatches.recordCount gt 0>
	<div style="float:right;margin-top:6px;">
		<table><tr><td style="font-size:8pt;"><img src="media/warning.gif" width="16" height="16"> <a href="##matching">View #arguments.strMemberInit.qryMatches.recordCount# possible matching accounts</a></td></tr></table>
	</div>
</cfif>
<div id="pageTitle">Member Account</div>
</cfoutput>

<!--- Any messages --->
<cfoutput>
<cfif isDefined("request.msg") and val(request.msg) gt 0>
	<cfswitch expression="#request.msg#">
		<cfcase value="1">
			<div class="red"><b>Member Account updated successfully at #TimeFormat(now(),"h:mm tt")#.</b></div>
		</cfcase>
		<cfcase value="2">
			<div class="red"><b>Member Account updated successfully at #TimeFormat(now(),"h:mm tt")# and information e-mailed to the account holder.</b></div>
		</cfcase>
		<cfcase value="5">
			<div class="red"><b>Member Account updated successfully at #TimeFormat(now(),"h:mm tt")# and account notes updated.</b></div>
		</cfcase>
		<cfcase value="6">
			<div class="red"><b>Credit Card information successfully updated and validated at #TimeFormat(now(),"h:mm tt")#.</b></div>
		</cfcase>
		<cfcase value="7">
			<div class="red"><b>Member Account updated successfully at #TimeFormat(now(),"h:mm tt")# and credit card removed.</b></div>
		</cfcase>
		<cfcase value="70">
			<div class="red"><b>Member Account updated successfully at #TimeFormat(now(),"h:mm tt")#. Credit card could not be removed. #request.err#</b></div>
		</cfcase>
		<cfcase value="9">
			<div class="red"><b>The Member Account was updated by another user after this page was initially loaded. You will need to redo any changes.</b></div>
		</cfcase>
		<cfcase value="10">
			<div class="red"><b>This member's subscription information has been updated. They will need to log off and log back on again for the changes to take effect.</b></div>
		</cfcase>
		<cfcase value="11">
			<div class="red"><b>This member's account has been created and transactions recorded.</b></div>
		</cfcase>
		<cfcase value="12">
			<div class="red"><b>Login IDs need to be unique, and "#request.oldusername#" is already used by another member. You will need to redo any changes.</b></div>
		</cfcase>
	</cfswitch>
</cfif>
</cfoutput>

<!--- member info table --->
<cfoutput>
<form action="MemberEdit.cfm" method="POST" onsubmit="return validateForm(this);" name="frmMember" id="frmMember">
<table border="0" width="100%">
<tr>
<td colspan="3">
	<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">Account Actions</div>
	<div style="border:1px solid ##0E568D;padding:4px;">

		<table width="100%">
		<tr><td width="25%">
			<cfif arguments.strMemberInit.qdepoMemberData.Pending is 0>
				<input type="button" name="approveAccount" value="Approve Account" onClick="fnapproveAccount()">
				<input type="button" name="denyAccount" value="Deny Account" onClick="fndenyAccount()"><br/>
			</cfif>
			<input type="button" name="saveAccount" value="Save Details" onClick="submitForm(this.name)">

			<cfif arguments.strMemberInit.qryBillingRecords.recordcount>
				<div style="background:##00CC66;margin-left:60px;padding:4px 20px;display:inline;font-weight:bold;">
					Billing Record for #ValueList(arguments.strMemberInit.qryBillingRecords.orgcode)#
				</div>
			</cfif>
			</td>
			<td align="center" width="25%">
				<input type="button" name="cloneAcct" value="Clone Account" onClick="cloneAccount();">
			</td>
			<td align="right" nowrap>
			<cfif arguments.strMemberInit.qdepoMemberData.membertype is not 7>
				<cfif arguments.strMemberInit.qryMCMemberInfo.memberID gt 0>
					<cfset local.cpLink = "http://#arguments.strMemberInit.qryMCMemberInfo.hostname#?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#arguments.strMemberInit.qryMCMemberInfo.memberID#&tab=emailactivity">
					<input type="button" name="EmailActivity" value="Email Activity" onClick="window.open('#local.cpLink#','_blank')">
				</cfif>
				<input type="button" name="DocumentView" value="Documents" onClick="submitForm(this.name)">
				<input type="button" name="groupAssoc" value="Org Groups" onClick="submitForm(this.name)">
				<input type="button" name="PurchaseCreditView" value="Purchase Credits" onClick="submitForm(this.name)">
				<input type="button" name="DocumentViewerHistory" value="Documents Viewed" onClick="submitForm(this.name)">
				<input type="button" name="QueryView" value="Queries" onClick="submitForm(this.name)">
			</cfif>
			<input type="button" name="SeminarWebView" value="SeminarWeb" onClick="submitForm(this.name)">
			<input type="button" name="transaction" value="Transactions" onClick="submitForm(this.name)">
			</td>
		</tr>
		</table>
	</div>
	<br/>
</td>
</tr>
<tr valign="top">
	<td>
	<input type="hidden" name="memAction" id="memAction" value="">
	<input type="hidden" name="depomemberdataid" value="#val(arguments.strMemberInit.qdepoMemberData.depomemberdataid)#">
	<input type="hidden" name="ts_lastUpdate" value="#ToBase64(arguments.strMemberInit.qdepoMemberData.ts_LastUpdate)#">
	
	<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">Membership Information</div>
	<div style="border:1px solid ##0E568D;padding:4px;">

		<table cellpadding=1 cellspacing=0>
		<tr style="height:22px;">
			<td>Last Site Access:</td>
			<td>&nbsp;</td>
			<td nowrap>
				<cfif arguments.strMemberInit.loginHistory.loginCount gt 0>
					#DateFormat(arguments.strMemberInit.loginHistory.LastLoginDate,"m/d/yyyy")# #TimeFormat(arguments.strMemberInit.loginHistory.LastLoginDate,"h:mm tt")#
				<cfelseif len(arguments.strMemberInit.qdepoMemberData.LastAccess)>
					#DateFormat(arguments.strMemberInit.qdepoMemberData.LastAccess,"m/d/yyyy")# #TimeFormat(arguments.strMemberInit.qdepoMemberData.LastAccess,"h:mm tt")#
				<cfelse>
					<b>NEVER</b>
				</cfif>
				&nbsp;
				[<a href="javascript:submitForm('loginhistory');">Total logins</a>: #numberformat(arguments.strMemberInit.loginHistory.loginCount)#]
			</td>
		</tr>
		<tr style="height:22px;">
			<td>Source ID:</td>
			<td>&nbsp;</td>
			<td><b>#arguments.strMemberInit.qdepoMemberData.sourceID#</b></td>
		</tr>
		<tr style="height:22px;">
			<td>DepoMemberData&nbsp;ID:</td>
			<td>&nbsp;</td>
			<td><b>#arguments.strMemberInit.qdepoMemberData.depomemberdataid#</b></td>
		</tr>
		<tr>
			<td class="red">First Name:</td>
			<td class="red">(R)</td>
			<td><input type="text" size="16" name="FNAME" value="#arguments.strMemberInit.qdepoMemberData.FirstName#"></td>
		</tr>
		<tr>
			<td class="red">Last Name:</td>
			<td class="red">(R)&nbsp;</td>
			<td><input type="text" size="16" name="LNAME" value="#arguments.strMemberInit.qdepoMemberData.LastNAME#"></td>
		</tr>
		<tr>
			<td>Firm:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="34" name="IFIRM" value="#arguments.strMemberInit.qdepoMemberData.BillingFIRM#"></td>
		</tr>
		<tr>
			<td>Billing Address 1:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="34" name="IADDRESS1" value="#arguments.strMemberInit.qdepoMemberData.BillingADDRESS#"></td>
		</tr>
		<tr>
			<td>Billing Address 2:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="34" name="IADDRESS2" value="#arguments.strMemberInit.qdepoMemberData.BillingADDRESS2#"></td>
		</tr>
		<tr>
			<td class="red">Billing City:</td>
			<td class="red">(R)&nbsp;</td>
			<td><input type="text" size="16" name="ICITY" value="#arguments.strMemberInit.qdepoMemberData.BillingCITY#"></td>
		</tr>
		<tr>
			<td class="red">Billing State:</td>
			<td class="red">(R)&nbsp;</td>
			<td>
				<select name="IST">
				<option value=""></option>
				<cfloop query="arguments.strMemberInit.qStates">
					<option value="#Code#" <cfif code eq arguments.strMemberInit.qdepoMemberData.BillingState>selected</cfif>>#Name# (#Code#)</option>							
				</cfloop>
				</select>
			</td>
		</tr>
		<tr>
			<td class="red">Billing ZIP:</td>
			<td class="red">(R)&nbsp;</td>
			<td><input type="text" size="12" name="IZIPCODE" value="#arguments.strMemberInit.qdepoMemberData.BillingZip#"></td>
		</tr>
		<tr>
			<td class="red">Billing Country:</td>
			<td class="red">(R)&nbsp;</td>
			<td>
				<select name="ICOUNTRY">
				<option value=""></option>
				<cfloop query="arguments.strMemberInit.qCountries">
					<option value="#countryID#" <cfif countryid eq arguments.strMemberInit.qdepoMemberData.BillingCountry>selected</cfif>>#Country#</option>							
				</cfloop>
				</select>
			</td>
		</tr>
		<tr>
			<td>Telephone:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="16" name="ITEL" value="#arguments.strMemberInit.qdepoMemberData.Phone#"></td>
		</tr>
		<tr>
			<td>Fax:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="16" name="IFAX" value="#arguments.strMemberInit.qdepoMemberData.FAX#"></td>
		</tr>
		<tr>
			<td nowrap>Main E-Mail:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="30" name="INTERNET" value="#arguments.strMemberInit.qdepoMemberData.Email#"></td>
		</tr>
		<tr>
			<td nowrap>Opt-out TrialSmith Marketing:</td>
			<td>&nbsp;</td>
			<td><input type="checkbox" name="optOutTSMarketing" value="1"<cfif arguments.strMemberInit.qdepoMemberData.optOutTSMarketing> checked</cfif>></td>
		</tr>
		<tr>
			<td colspan="3" align="center">
				<input type="button" style="font-size:8pt;" name="allEmails" value="All Emails" onClick="submitForm(this.name)" <cfif len(arguments.strMemberInit.qdepoMemberData.Email) is 0>disabled</cfif>>
				<input type="button" name="btnLyris" style="font-size:8pt;" value="Lyris Lookup" onClick="launchX('http://lists.trialsmith.com/members/show/index.tml?type=emailall&key=#arguments.strMemberInit.qdepoMemberData.Email#')" <cfif len(arguments.strMemberInit.qdepoMemberData.Email) is 0>disabled</cfif>>
				<input type="button" style="font-size:8pt;" name="lyrisManager" value="Lyris Subscription Mgr" onClick="submitForm(this.name)" <cfif len(arguments.strMemberInit.qdepoMemberData.Email) is 0>disabled</cfif>>
			</td>
		</tr>
		<tr><td colspan="3">&nbsp;</td></tr>
		<tr valign="top">
			<td class="red">Main Association:</td>
			<td class="red">(R)&nbsp;</td>
			<td>
				<select name="IRECTYPE" style="font-size:8pt;">
				<option value=""></option>
				<cfloop query="arguments.strMemberInit.orgCodesAll">
					<option value="#state#" <cfif state eq arguments.strMemberInit.qdepoMemberData.TLAMemberState>selected</cfif>>#ReplaceNoCase(description,"Association","Assoc")# (#state#)</option>
				</cfloop>
				</select><br/>
				<cfif arguments.strMemberInit.qdepoMemberData.Pending is 0>
					<span class="b red">NOTE: This account has not been approved yet. Changing their main association may result in a different price for the plan they purchased.</span>
				</cfif>
			</td>
		</tr>
		<tr valign="top">
			<td>Signed up with:</td>
			<td>&nbsp;</td>
			<cfif len(arguments.strMemberInit.qdepoMemberData.signuporgcode)>
				<td>#arguments.strMemberInit.qdepoMemberData.signuporgcode#</td>
			<cfelse>
				<td>TrialSmith</td>
			</cfif>
		</tr>
		</table>
	</div>
	<br/>
	<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">Payment Information</div>
	<div style="border:1px solid ##0E568D;padding:4px;<cfif arguments.strMemberInit.qCreditCard_TS.recordcount gt 1 or arguments.strMemberInit.qCreditCard_SW.recordcount gt 1> background:##99CC00;</cfif>">
		<table cellpadding=1 cellspacing=0>
		<tr>
			<td>Billing Contact Name:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="30" name="BILLINGCONTACTNAME" value="#arguments.strMemberInit.qdepoMemberData.BillingContactName#"></td>
		</tr>
		<tr>
			<td>Billing Contact E-mail:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="30" name="BILLINGCONTACTEMAIL" value="#arguments.strMemberInit.qdepoMemberData.BillingContactEmail#"></td>
		</tr>
		</table>
		<br/>

		<table cellpadding=1 cellspacing=0>
		<tr>
			<td class="red" nowrap width="175">Payment Type:</td>
			<td class="red" width="20">(R)&nbsp;</td>
			<td colspan="2">
				<select name="paymentType">
				<option value="C" <cfif arguments.strMemberInit.qdepoMemberData.paymentType EQ "C">SELECTED</cfif>>Credit Card</option>
				<option value="I" <cfif arguments.strMemberInit.qdepoMemberData.paymentType NEQ "C">SELECTED</cfif>>Invoice</option>
				</select>
			</td>
		</tr>

		<tr><td colspan="4"><br/><b>Payment Methods on File:</td></tr>

		<cfset local.ccqryList = "TrialSmith via Authorize|TS,SeminarWeb via Authorize|SW">

		<cfloop list="#local.ccqryList#" index="local.ccQryName">
			<cfset local.qryTitle = getToken(local.ccQryName,1,'|')>
			<cfset local.qrySuffix = getToken(local.ccQryName,2,'|')>
			<cfset local.qry = arguments.strMemberInit["qCreditCard_" & local.qrySuffix]>
			<cfif local.qry.recordcount is 0>
				<tr>
					<td width="175" class="r">#local.qryTitle#:</td>
					<td width="20" class="r">&nbsp;</td>
					<td>No pay method on file.</td>
					<td>&nbsp;
						<input type="button" name="updateCard#local.qrySuffix#" value="Add" style="width:40px;font-size:.9em;" onClick="submitForm(this.name)"> &nbsp;
					</td>
				</tr>
			<cfelse>
				<cfloop query="local.qry">
					<tr>
						<td width="90" class="r">#local.qryTitle#:</td>
						<td width="20" class="r <cfif local.qry.declined is 1>red</cfif>">#local.qry.cardType#&nbsp;</td>
						<td <cfif local.qry.declined is 1>class="red"</cfif>>
							#local.qry.detail#
							<cfif len(local.qry.expiration)>&nbsp; Exp #dateformat(local.qry.expiration,"mm/yy")#</cfif>
							<cfif local.qry.declined is 1>&nbsp; (declined)</cfif>
						</td>
						<td>&nbsp;
							<input type="button" name="updateCard#local.qrySuffix#" value="Edit" style="width:40px;font-size:.9em;" onClick="submitForm(this.name);" <cfif local.qry.recordcount gt 1>disabled</cfif>>
							<input type="button" value="Replace" style="width:55px;font-size:.9em;" disabled>
							<input type="button" name="clearcard#local.qrySuffix#" value="Remove" style="font-size:.9em;" onClick="submitForm(this.name);">
							<input type="button" name="chargecard#local.qrySuffix#" value="Charge" style="font-size:.9em;" onClick="submitForm(this.name)" <cfif local.qry.recordcount gt 1 or local.qry.declined is 1>disabled</cfif>>
						</td>
						<cfif local.qry.recordcount gt 1>
							<td><i class="far fa-exclamation-triangle"></i> <b>Multiple pay methods on file. Remove one!</b></td>
						</cfif>
					</tr>
				</cfloop>
			</cfif>
		</cfloop>

		<tr><td colspan="4">&nbsp;</td></tr>
		</table>
	</div>
	</td>
	<td width="10">&nbsp;</td>
	<td>
	<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">TrialSmith Support Information</div>
	<div style="border:1px solid ##0E568D;padding:4px;<cfif arguments.strMemberInit.qdepoMemberData.MAILCODE eq "I">background:##FF0000;</cfif>">
		<table cellpadding=1 cellspacing=0>
		<tr>
			<td>Subscription Plan:</td>
			<td>&nbsp;</td>
			<td>
				<select name="MemberType">
				<cfloop query="arguments.strMemberInit.qMemberType">
					<option value="#membertypeID#" <cfif membertypeid eq arguments.strMemberInit.qdepoMemberData.MemberType>selected</cfif>>#membertype#</option>
				</cfloop>
				</select>
			</td>
		</tr>
		<tr>
			<td nowrap>Current Subscription Expires:</td>
			<td>&nbsp;</td>
			<cfif len(arguments.strMemberInit.qdepoMemberData.RenewalDate)>
				<td><script>DateInput('RenewalDate', true, 'MM/DD/YYYY', '#DateFormat(arguments.strMemberInit.qdepoMemberData.RenewalDate,'m/d/yyyy')#')</script></td>
			<cfelse>
				<td><script>DateInput('RenewalDate', true, 'MM/DD/YYYY', '#DateFormat(now(),'m/d/yyyy')#')</script></td>
			</cfif>
		</tr>
		</table>
		<cfif arguments.strMemberInit.FirmPlanInfo.recordcount is 1>
			<div style="color:##009900;font-weight:bold;margin:5px 20px;">
				Note: This member belongs to the <a href="firmPlanEdit.cfm?firmplanID=#arguments.strMemberInit.FirmPlanInfo.firmPlanID#">#arguments.strMemberInit.FirmPlanInfo.firmPlan#</a> firm plan.<br/>
				<cfif arguments.strMemberInit.FirmPlanInfo.isMaster>
					This is the master account.
				<cfelse>
					View the <a href="MemberEdit.cfm?depomemberdataID=#arguments.strMemberInit.FirmPlanInfo.depoMemberDataID#">master account</a>.
				</cfif>
			</div>
		</cfif>
		<cfif arguments.strMemberInit.qdepoMemberData.Pending is not 0 and arguments.strMemberInit.qdepoMemberData.MAILCODE eq "A">
			<cfif arguments.strMemberInit.qdepoMemberData.BillingCountry is 1>
				<cfset local.hasTaxInfo = application.objCommon.checkBillingZIP(billingState=arguments.strMemberInit.qdepoMemberData.BillingState, billingZip=left(arguments.strMemberInit.qdepoMemberData.BillingZip,5))>
			<cfelse>
				<cfset local.hasTaxInfo = len(arguments.strMemberInit.qdepoMemberData.BillingState) and len(arguments.strMemberInit.qdepoMemberData.BillingZip)>
			</cfif>
			<table>
			<tr><td><input type="button" name="membershipRenew" value="Renew" style="font-size:8pt;" onClick="submitForm(this.name)" <cfif not local.hasTaxInfo>disabled</cfif>></td>
				<td style="font-size:9pt;"><b>Renew</b> will change plans (if necessary) and 
					<cfif len(arguments.strMemberInit.qdepoMemberData.renewaldate) and datecompare(arguments.strMemberInit.qdepoMemberData.renewaldate,now()) gt 0>
						extend the current expiration date based on the plan selected.
					<cfelse>
						issue a new expiration date based on today's date.
					</cfif>
				</td>
			</tr>
			<cfif len(arguments.strMemberInit.qdepoMemberData.renewaldate) and datecompare(arguments.strMemberInit.qdepoMemberData.renewaldate,now()) gt 0>
				<tr>
					<td><input type="button" name="membershipUpgrade" value="Upgrade" style="font-size:8pt;" onClick="submitForm(this.name)" <cfif not local.hasTaxInfo>disabled</cfif>></td>
					<td style="font-size:9pt;"><b>Upgrade</b> will change plans and issue a new expiration date based on the plan selected and today's date.</td>
				</tr>
			</cfif>
			<cfif not local.hasTaxInfo>
				<tr>
					<td colspan="2">
						<div style="padding:.5rem 1rem;border:1px solid transparent;border-radius:.25rem;color:##8a6d3b;background-color:##fcf8e3;border-color:##faebcc;">
							To Renew<cfif len(arguments.strMemberInit.qdepoMemberData.renewaldate) and datecompare(arguments.strMemberInit.qdepoMemberData.renewaldate,now()) gt 0> or Upgrade</cfif> Subscription, BillingState and BillingZIP must be defined and be valid.
						</div>
					</td>
				</tr>
			</cfif>
			</table>
		</cfif>
		<cfif arguments.strMemberInit.qdepoMemberData.MAILCODE eq "I">
			<div class="b"><br/>Account is inactive.</div>			
		</cfif>
		
		<br/>
		<table cellpadding=2 cellspacing=0>
		<tr>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>Pending:</td>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>(R)&nbsp;</td>
			<td colspan="2">
				<select name="Pending" style="font-size:8pt;" >
				<option value="" <cfif len(arguments.strMemberInit.qdepoMemberData.Pending) is 0>SELECTED</cfif>>Not Selected</option>
				<option value="0" <cfif arguments.strMemberInit.qdepoMemberData.Pending is 0>SELECTED</cfif>>0 - Pending</option>
				<option value="1" <cfif arguments.strMemberInit.qdepoMemberData.Pending is 1>SELECTED</cfif>>1 - Approved/Denied</option>
				</select>
			</td>
		</tr>
		<tr>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>Mail&nbsp;Code:</td>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>(R)&nbsp;</td>
			<td><select name="IMAILCODE" style="font-size:8pt;" >
				<option <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE eq "A">selected</cfif> value="A">Active</option>
				<option <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE eq "I">selected</cfif> value="I">Inactive</option>
				</select>
			</td>	
		</tr>
		<tr>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>JurySmith&nbsp;Access:</td>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>(R)&nbsp;</td>
			<td><select name="juryResearchEnabled" style="font-size:8pt;" >
				<option value="0" <cfif arguments.strMemberInit.qdepoMemberData.juryResearchEnabled is not 1>SELECTED</cfif>>Not Allowed</option>
				<option value="1" <cfif arguments.strMemberInit.qdepoMemberData.juryResearchEnabled is 1>SELECTED</cfif>>Allowed - Agreement on File</option>
				</select>
			</td>	
		</tr>
		<tr>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>Search&nbsp;Tool&nbsp;Access:</td>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>(R)&nbsp;</td>
			<td><select name="TSAllowed" style="font-size:8pt;" >
				<option value="">No Value Selected</option>
				<option value="0" <cfif arguments.strMemberInit.qdepoMemberData.TSAllowed is 0>SELECTED</cfif>>Only Approved for TLA Tools buckets</option>
				<option value="1" <cfif arguments.strMemberInit.qdepoMemberData.TSAllowed is 1>SELECTED</cfif>>Approved for all buckets - Plantiff Lawyer</option>
				</select>
			</td>	
		</tr>
		<tr>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>Document&nbsp;Chat:</td>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>(R)&nbsp;</td>
			<td><select name="isDocumentChatEnabled" style="font-size:8pt;" >
				<option value="0" <cfif arguments.strMemberInit.qdepoMemberData.isDocumentChatEnabled is 0>SELECTED</cfif>>Disabled - Document Chat is turned OFF</option>
				<option value="1" <cfif arguments.strMemberInit.qdepoMemberData.isDocumentChatEnabled is 1>SELECTED</cfif>>Enabled - Document Chat is turned ON</option>				
				</select>
			</td>	
		</tr>
		<tr>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>Deposed&nbsp;Experts&nbsp;Bucket:</td>
			<td <cfif arguments.strMemberInit.qdepoMemberData.MAILCODE neq "I">class="red"</cfif>>(R)&nbsp;</td>
			<td><select name="includeInSubscriberDepos" style="font-size:8pt;" >
				<option value="0" <cfif arguments.strMemberInit.qdepoMemberData.includeInSubscriberDepos is 0>SELECTED</cfif>>Not Included in Lawyers Deposing Experts Search Results</option>
				<option value="1" <cfif arguments.strMemberInit.qdepoMemberData.includeInSubscriberDepos is 1>SELECTED</cfif>>Included in Lawyers Deposing Experts Search Results</option>				
				</select>
			</td>	
		</tr>
		</table>
		<br/>
		<div>
			<button type="button" style="font-size:9pt;width:150px;" onclick="submitForm('zendeskTSCU');" <cfif len(arguments.strMemberInit.qdepoMemberData.Email) is 0>disabled</cfif>>Create Ticket</button> Create a support ticket for TrialSmith.com
		</div>
		
		<cfset local.isLinkedToTS = false>
		<cfloop query="arguments.strMemberInit.linkedSites">
			<cfif arguments.strMemberInit.linkedSites.sitecode eq "TS">
				<cfset local.isLinkedToTS = true>
			</cfif>
		</cfloop>
		<cfif not arguments.strMemberInit.qdepoMemberData.adminFlag2 eq "Y" and NOT local.isLinkedToTS>
			<div style="margin-top:10px;">
				<button type="button" style="font-size:9pt;width:150px;" <cfif arguments.strMemberInit.accountsInWaiting.recordcount eq 0>onclick="submitForm('affiliateTS');"</cfif><cfif arguments.strMemberInit.accountsInWaiting.recordcount>title="This action is disabled because there is an Account in Waiting that needs to be converted." disabled</cfif>>Affiliate to TS</button> This will create a new account under MC and affiliate to TS.
			</div>
		</cfif>
		<div style="margin-top:10px;">
			<input type="button" value="Send Welcome Email" style="font-size:9pt;width:150px;" onClick="submitForm('sendWelcomeEmail')" <cfif len(arguments.strMemberInit.qdepoMemberData.Email) is 0>disabled</cfif>> Preview and Send a Welcome Email
		</div>
	</div>
	<br/>

	<cfif not arguments.strMemberInit.qdepoMemberData.adminFlag2 eq "Y">
		<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">Website Affiliations</div>
		<div style="border:1px solid ##0E568D;padding:4px;">

			<cfsavecontent variable="buttonAreaAFFIL">
				<cfoutput>
				<br/>
				<table cellpadding="2" cellspacing="0">

				<!--- no affil to new if account in waiting --->
				<cfif arguments.strMemberInit.accountsInWaiting.recordcount is 0>
					<tr><td><button type="button" style="font-size:9pt;width:140px;" onclick="clearAffilDivs();document.getElementById('affilToNewDiv').style.display='';" <cfif arguments.strMemberInit.qdepoMemberData.MemberType is 9>disabled</cfif>>Affiliate</button></td><td>Add an affiliation to a hosted website</td></tr>
				<cfelse>
					<tr><td><button type="button" style="font-size:9pt;width:140px;" disabled>Affiliate</button></td><td>Add an affiliation to a hosted website <nobr><i>(not available while account in waiting)</i></nobr></td></tr>
				</cfif>
				
				<cfset local.isLinkedToMC = false>
				<cfset local.isLinkedToMCSuper = false>
				<cfloop query="arguments.strMemberInit.linkedSites">
					<cfif arguments.strMemberInit.linkedSites.orgcode eq "MC">
						<cfset local.isLinkedToMC = true>
						<cfset local.linkedToMCMemberNumber = arguments.strMemberInit.linkedSites.membernumber>

						<cfquery name="local.qryIsInSuperUserGroup" datasource="#application.settings.dsn.membercentral.dsn#">
							select count(*) as theCount
							from dbo.cache_members_groups as mcg
							inner join dbo.ams_groups as g on g.orgID = 1 
								and g.groupID = mcg.groupID
								and g.groupCode = 'SuperAdmins'
							where mcg.orgID = 1
							and mcg.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.strMemberInit.linkedSites.memberID#">
						</cfquery>
						<cfif local.qryIsInSuperUserGroup.theCount is 1>
							<cfset local.isLinkedToMCSuper = true>
						</cfif>
					</cfif>
				</cfloop>

				<cfif local.isLinkedToMC and local.isLinkedToMCSuper>
					<tr><td style="padding-top:10px;">
							<input type="hidden" name="mcsuperMemNum" value="#local.linkedToMCMemberNumber#">
							<button type="button" style="font-size:9pt;width:140px;" onclick="submitForm('makeSuperUser');">Make SuperUser</button>
						</td><td style="padding-top:10px;">Turn this user into a SuperUser</td></tr>
				<cfelse>
					<tr valign="top">
						<td style="padding-top:10px;"><button type="button" style="font-size:9pt;width:140px;" disabled>Make SuperUser</button></td>
						<td style="padding-top:10px;">
							Not able to make SuperUser
							<cfif NOT local.isLinkedToMC>
								<br/>- This account is not affiliated to an account on MC.
							</cfif>
							<cfif local.isLinkedToMC and NOT local.isLinkedToMCSuper>
								<br/>- The affiliated MC account is not in the SuperUser group.
							</cfif>
						</td>
					</tr>
				</cfif>

				</table>
				</cfoutput>
			</cfsavecontent>

			<input type="hidden" id="loginOrgcode" name="loginOrgcode" value="">

			<cfif arguments.strMemberInit.linkedSites.recordcount is 0 and arguments.strMemberInit.accountsInWaiting.recordcount is 0>
				<div style="padding-bottom:4px;">This account is not affiliated with any sites.</div>
				<br/>
			
			<cfelse>
				<table class="tblInside">
				<cfif arguments.strMemberInit.accountsInWaiting.recordcount>
					<cfset local.cpLink = "http://" & arguments.strMemberInit.accountsInWaiting.mainHostName & "?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#arguments.strMemberInit.accountsInWaiting.memberID#">
					<tr valign="top">
						<td nowrap style="padding-bottom:8px;padding-right:8px;">
							<button style="font-size:12pt;" type="button" onclick="fnConvertAIW();" title="Convert Account in Waiting"><i class="far fa-random red"></i></button>
							<button style="font-size:12pt;" type="button" disabled><i class="far fa-ticket-alt"></i></button>
							<button style="font-size:12pt;" type="button" onclick="submitForm('clearAIW');" title="Clear Account in Waiting"><i class="far fa-user-times"></i></button>
							<div class="c red" style="margin-top:6px;"><b>ACCT IN WAITING</b></div>
						</td>
						<td style="padding-bottom:8px;padding-right:8px;"><b>#arguments.strMemberInit.accountsInWaiting.orgCode#</b></td>
						<td <cfif arguments.strMemberInit.accountsInWaiting.status neq "A">class="red"</cfif> style="padding-bottom:8px;">
								<a href="#local.cpLink#" target="_blank"><b>#arguments.strMemberInit.accountsInWaiting.firstname# #arguments.strMemberInit.accountsInWaiting.lastname#</b></a> (#arguments.strMemberInit.accountsInWaiting.membernumber#)
							<cfif len(arguments.strMemberInit.accountsInWaiting.address1)><br/>#arguments.strMemberInit.accountsInWaiting.address1#</cfif>
							<cfif len(arguments.strMemberInit.accountsInWaiting.address2)><br/>#arguments.strMemberInit.accountsInWaiting.address2#</cfif>
							<cfif len("#arguments.strMemberInit.accountsInWaiting.city##arguments.strMemberInit.accountsInWaiting.stateCode##arguments.strMemberInit.accountsInWaiting.postalCode#")>
								<br/>#arguments.strMemberInit.accountsInWaiting.city# #arguments.strMemberInit.accountsInWaiting.stateCode# #arguments.strMemberInit.accountsInWaiting.postalCode#
								<br/><i>Last updated: #dateformat(arguments.strMemberInit.accountsInWaiting.addressLastUpdated,"m/d/yyyy")#</i>
							</cfif>
							<cfif len(arguments.strMemberInit.accountsInWaiting.email)><br/>#arguments.strMemberInit.accountsInWaiting.email#</cfif>
						</td>
					</tr>
				</cfif>
				<cfif arguments.strMemberInit.linkedSites.recordcount>
					<input type="hidden" id="mnpIDToRemove" name="mnpIDToRemove" value="0">
					<cfloop query="arguments.strMemberInit.linkedSites">
						<cfset local.cpLink = "http://" & arguments.strMemberInit.linkedSites.mainHostName & "?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#arguments.strMemberInit.linkedSites.memberID#">
						<tr valign="top">
							<td nowrap style="padding-bottom:8px;padding-right:8px;">
								<button style="font-size:12pt;" type="button" onclick="loginAsMember('#arguments.strMemberInit.linkedSites.siteCode#','#arguments.strMemberInit.linkedSites.membernumber#');" title="Login As Member" <cfif arguments.strMemberInit.linkedSites.status neq "A">disabled</cfif>><i class="far fa-sign-in-alt"></i></button>
								<button style="font-size:12pt;" type="button" onclick="zendeskSSO('#arguments.strMemberInit.linkedSites.siteCode#','#arguments.strMemberInit.linkedSites.membernumber#');" title="Create Ticket As Member"><i class="far fa-ticket-alt"></i></button>
								<button style="font-size:12pt;" type="button" onclick="document.getElementById('mnpIDToRemove').value=#arguments.strMemberInit.linkedSites.mnpID#; submitForm('removeMNP');" title="Remove affiliation"><i class="far fa-user-times"></i></button>
							</td>
							<td style="padding-bottom:8px;padding-right:8px;"><b>#arguments.strMemberInit.linkedSites.SiteCode#</b></td>
							<td <cfif arguments.strMemberInit.linkedSites.status neq "A">class="red"</cfif> style="padding-bottom:8px;">
								<a href="#local.cpLink#" target="_blank"><b>#arguments.strMemberInit.linkedSites.firstname# #arguments.strMemberInit.linkedSites.lastname#</b></a> (#arguments.strMemberInit.linkedSites.membernumber#)
								<cfif len(arguments.strMemberInit.linkedSites.username)><br/>Username: #arguments.strMemberInit.linkedSites.username#</cfif>
								<cfif len(arguments.strMemberInit.linkedSites.address1)><br/>#arguments.strMemberInit.linkedSites.address1#</cfif>
								<cfif len(arguments.strMemberInit.linkedSites.address2)><br/>#arguments.strMemberInit.linkedSites.address2#</cfif>
								<cfif len("#arguments.strMemberInit.linkedSites.city##arguments.strMemberInit.linkedSites.stateCode##arguments.strMemberInit.linkedSites.postalCode#")>
									<br/>#arguments.strMemberInit.linkedSites.city# #arguments.strMemberInit.linkedSites.stateCode# #arguments.strMemberInit.linkedSites.postalCode#
									<br/><i>Last updated: #dateformat(arguments.strMemberInit.linkedSites.addressLastUpdated,"m/d/yyyy")#</i>
								</cfif>
								<cfif len(arguments.strMemberInit.linkedSites.email)><br/>#arguments.strMemberInit.linkedSites.email#</cfif>
							</td>
						</tr>
					</cfloop>
				</cfif>
				</table>			
			</cfif>
			
			<cfquery name="local.qryRemoteLogins" datasource="#application.settings.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
				select siteCode, siteName	
				from dbo.sites	
				where useRemoteLogin = 1	
				order by siteCode	
			</cfquery>	
			<div style="padding:.75rem 1.25rem;border:1px solid transparent;border-radius:.25rem;color:##0c5460;background-color:##d1ecf1;border-color:##bee5eb;">
				The following sites have remote authentication.<cfif arguments.strMemberInit.linkedSites.recordcount or arguments.strMemberInit.accountsInWaiting.recordcount> Login as Member icons will still work for troubleshooting.</cfif>
				<cfloop query="local.qryRemoteLogins">	
					<li>#local.qryRemoteLogins.siteCode# - #local.qryRemoteLogins.siteName#</li>	
				</cfloop>	
			</div>

			#buttonAreaAFFIL#
			
			<cfif arguments.strMemberInit.accountsInWaiting.recordcount is 0>
				<div id="affilToNewDiv" style="display:none;">
					<br/>
					<b>Add affiliation to a hosted website:</b><br/>
					<div style="margin-left:20px;">
						<table>
						<tr><td>Member Number:</td><td><input type="text" size="24" name="mc_memnum" maxlength="50" value=""></td></tr>
						<tr><td>Website:</td><td><select name="mc_siteid" style="font-size:.9em;">
								<option value="">Select Website</option>
								<cfloop query="arguments.strMemberInit.MCSitesAll">
									<cfif arguments.strMemberInit.linkedSites.recordcount>
										<cfif loginNetworkID is arguments.strMemberInit.linkedSites.networkID>
											<option value="#siteid#">#sitecode# - #sitename#</option>
										</cfif>
									<cfelse>
										<option value="#siteid#">#sitecode# - #sitename#</option>
									</cfif>
								</cfloop>
								</select></td></tr>
						<tr><td colspan="2"><input type="button" style="font-size:.9em;" name="addMCAffiliation" value="Add Affil" onClick="fnAddMCAffiliation()"></td></tr>
						</table>
					</div>
				</div>
			<cfelseif arguments.strMemberInit.MCSitesAIW.recordcount gt 1>
				<div id="convertAIWDiv" style="display:none;">
					<br/>
					<b>Convert this "account in waiting" to a network profile:</b><br/>
					<div style="margin-left:20px;">
						<table>
						<tr><td>Website:</td><td><select name="AIWsiteid" style="font-size:.9em;">
								<option value="#arguments.strMemberInit.MCSitesAIW.siteid#">Select Website</option>
								<cfloop query="arguments.strMemberInit.MCSitesAIW">
									<option value="#siteid#">#sitecode# - #sitename#</option>
								</cfloop>
								</select></td></tr>
						<tr><td colspan="2"><input type="button" style="font-size:.9em;" name="convertAIW" value="Convert Acct" onclick="submitForm(this.name);"></td></tr>
						</table>
					</div>
				</div>
			<cfelse>
				<input type="hidden" id="AIWsiteid" name="AIWsiteid" value="">
			</cfif>	

		</div>
		<br/>
	</cfif>

	<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">Depositions Information</div>
	<div style="border:1px solid ##0E568D;padding:4px;">
		<table cellpadding=1 cellspacing=0>
		<tr>
			<td>Depos Contact First Name:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="16" name="DEPOCONTACTFNAME" value="#arguments.strMemberInit.qdepoMemberData.DepoContactFirstName#"></td>
		</tr>
		<tr>
			<td>Depos Contact Last Name:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="16" name="DEPOCONTACTLNAME" value="#arguments.strMemberInit.qdepoMemberData.DepoContactLastName#"></td>
		</tr>
		<tr>
			<td>Depos Contact E-mail:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="30" name="DEPOCONTACTEMAIL" value="#arguments.strMemberInit.qdepoMemberData.DepoContactEmail#"></td>
		</tr>
		<tr>
			<td>Depos Contact Phone:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="16" name="DEPOCONTACTPHONE" value="#arguments.strMemberInit.qdepoMemberData.DepoContactPhone#"></td>
		</tr>
		<tr>
			<td nowrap>Depos Contact Interaction Date:</td>
			<td>&nbsp;</td>
			<td><input type="text" size="16" name="DEPOCONTACTDATE" value="#DateFormat(arguments.strMemberInit.qdepoMemberData.DepoContactDate,'m/d/yyyy')#"></td>
		</tr>
		</table>
	</div>
	</td>
</tr>

<cfif arguments.strMemberInit.qryMatches.recordCount gt 0>
	<tr>
	<td colspan="3">
		<a name="matching"></a>
		<br/>
		<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">Possible Matching Accounts</div>
		<div style="border:1px solid ##0E568D;padding:4px;">
			The higher the match score, the more closely the accounts match.<br/><br/>
			<table class="tblSlim">
			<tr><th colspan="10">&nbsp;<img src="media/arrow_small.gif" width="21" height="15"><input type="button" value="Compare Selected Accounts" style="font-size:8pt;width:160px;" onClick="fnmergeAccount();"> <cfif arguments.strMemberInit.HasPendingTransactions.recordcount><img src="media/warning.gif" width="16" height="16"> Pending transactions must be cleared to merge member.</cfif></th></tr>
			<tr><th></th><th></th><th>Member</th><th>SourceID</th><th>DepoID</th><th>Firm</th><th>Assoc</th><th>Signed&nbsp;Up</th><th>Subscription</th><th>&nbsp;</th></tr>
			<cfloop query="arguments.strMemberInit.qryMatches">
				<tr>
				<td><input type="checkbox" id="frmMergeDepoID" name="frmMergeDepoID" value="#arguments.strMemberInit.qryMatches.depomemberdataid#" <cfif arguments.strMemberInit.qryMatches.hasPendingTransactions is 1 OR arguments.strMemberInit.HasPendingTransactions.recordcount>disabled</cfif>></td>
				<td colspan="2" class="matchBack" nowrap>
					<span class="matchScore">#arguments.strMemberInit.qryMatches.matchPct#% match</span>
					<span class="matchName"><a href="MemberEdit.cfm?depoMemberDataID=#arguments.strMemberInit.qryMatches.depomemberdataid#&approval=1">#arguments.strMemberInit.qryMatches.LastName#, #arguments.strMemberInit.qryMatches.FirstName#</a></span></td>
				<td style="color:##666;">#arguments.strMemberInit.qryMatches.SourceID#</td>
				<td style="color:##666;">#arguments.strMemberInit.qryMatches.depomemberdataID#</td>
				<td style="color:##666;">#arguments.strMemberInit.qryMatches.billingFIRM#</td>
				<td style="color:##666;">#arguments.strMemberInit.qryMatches.tlamemberstate#</td>
				<td style="color:##666;">#arguments.strMemberInit.qryMatches.signuporgcode#</td>
				<td style="color:##666;">#arguments.strMemberInit.qryMatches.MemberType#</td>
				<td style="color:##666;"><cfif arguments.strMemberInit.qryMatches.hasPendingTransactions is 1><img src="media/warning.gif" width="16" height="16"> Pending transactions must be cleared to merge member.</cfif></td>
				</tr>
			</cfloop>
			</table>
		</div>
	</td>
	</tr>
</cfif>

<tr>
<td colspan="3">
	<br/>
	<div style="background:##0E568D;color:##FFFFFF;padding:4px;font-weight:bold;">Account Notes</div>
	<div style="border:1px solid ##0E568D;padding:4px;">
		<table cellspacing="0" cellpadding="1" width="98%">
		<tr valign="bottom">
			<td>
				Note Type:<br/>
				<select name="notetypeid">
				<cfloop query="arguments.strMemberInit.notetypes">
					<option value="#NoteTypeID#" <cfif notetypeid is 5>selected</cfif>>#Description#</option>
				</cfloop>
				</select>
			</td>
			<td>
				<textarea rows="2" name="NewNote" style="width:500px;"></textarea>
			</td>
			<td><input type="button" value="Save New Note" name="saveNote" onClick="submitForm(this.name)"></td>
		</tr>
		</table>
		<br/>
		<table cellspacing="0" width="98%">
		<cfloop query="arguments.strMemberInit.getnotes">
			<tr valign="top">
				<td class="bb" width="120">#dateformat(dateentered, "m/d/yy")# #timeformat(dateentered,"h:mm tt")#</td>
				<td class="bb">#Replace(note,chr(10),"<br/>","ALL")#</td>
			</tr>
		</cfloop>
		</table>
	</div>
</td>
</tr>
</table>
</form>
<script type="text/html" id="cloneConfirmationTemplate">
	<div style="padding:10px;font-family: verdana;">
		<div style="font-size: 1.5em;font-weight: bold;margin-bottom:20px;color: ##0E568D;">Clone Member Account</div>
		<div style="font-size: 1.1em;margin-bottom:20px;">
			This will create a new TS Admin account with the <b>same demographic details</b> as this account but <b>without any website affiliations</b>. The new account will be assigned a <b>Basic Plan</b> with the same expiration as this account.<br/><br/>
			You would use this functionality primarily to split an account so each can be affiliated to sites on different networks.
		</div>
		<div style="align-content:center !important; align-items:center !important; display:flex !important; color:##824224; background-color:##fde4d5; border-color:##fcd9c4; position:relative; padding:.75rem 1.25rem; margin-bottom:1em; border:1px solid transparent; border-radius:.65rem;font-size:14px;">
			<span style="font-size: 1.1875rem;height: 40px !important; line-height: 40px !important; width: 40px !important; text-align: center !important; margin-right: .5rem !important;display: block !important;">
				<i class="fas fa-question" style="padding-top:10px;"></i>
			</span>
			<span>Are you sure you want to <b>clone</b> this member account?</span>
		</div>
		<div style="text-align:right;"><button type="button" name="btnCloneAcct" id="btnCloneAcct" onclick="doCloneAccount();">Continue</button></div>
	</div>
</script>
</cfoutput>