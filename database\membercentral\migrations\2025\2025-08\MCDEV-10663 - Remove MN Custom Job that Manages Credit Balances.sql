use customApps
GO

DROP PROC dbo.mn_addAndRemoveMemberCredit;
GO

DROP TABLE dbo.mn_memberCredits
GO

use membercentral
GO

DELETE FROM platformStatsMC.dbo.scheduledTaskHistory where taskID = 345;
DELETE FROM dbo.scheduledTaskEnvironments where taskID = 345;
DELETE FROM dbo.scheduledTasks where taskID = 345;
GO


select t.transactionID, tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountOfPayment as unallocatedAmount
into #tmpCredits
from dbo.tr_transactions as t
inner join dbo.tr_transactionPayments as tp on tp.orgID = 171 
	and tp.profileID in (494,509,510,511,512)
	and tp.transactionID = t.transactionID
	and tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountOfPayment > 0
where t.ownedByOrgID = 171
and t.statusid IN (1,3);

declare @paymentDate datetime;
declare @WriteOffCreditGLAccountID int, @writeOffTransactionID int, @paymentTransactionID int, @paymentAmount decimal(18,2);
EXEC membercentral.dbo.tr_getGLAccountByAccountCode @orgID=171, @accountCode='RevokedCredit', @GLAccountID=@WriteOffCreditGLAccountID OUTPUT;

SELECT @paymentTransactionID = min(transactionID) FROM #tmpCredits;
WHILE @paymentTransactionID IS NOT NULL BEGIN
	SELECT @paymentAmount = unallocatedAmount FROM #tmpCredits WHERE transactionID = @paymentTransactionID;
	SET @paymentDate = GETDATE();
	SET @writeOffTransactionID = null;

	EXEC membercentral.dbo.tr_createTransaction_writeoff_payment @recordedOnSiteID=181, 
		@recordedByMemberID=2270475, @statsSessionID=null, @status='Active', @amount=@paymentAmount, 
		@transactionDate=@paymentDate, @paymentTransactionID=@paymentTransactionID,
		@creditGLAccountID=@WriteOffCreditGLAccountID, @transactionID=@writeOffTransactionID OUTPUT;

	SELECT @paymentTransactionID = min(transactionID) FROM #tmpCredits WHERE transactionID > @paymentTransactionID;
END

DROP TABLE #tmpCredits;
GO
