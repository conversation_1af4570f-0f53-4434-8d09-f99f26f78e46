CREATE PROC dbo.swb_updateBundleSettings
@bundleID INT,
@siteCode VARCHAR(10),
@customTextEnabled BIT,
@customTextContent VARCHAR(MAX) = NULL,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @participantID INT, @orgID INT, @siteID INT, @msgjson VARCHAR(MAX), @crlf VARCHAR(10);

	SET @crlf = CHAR(13) + CHAR(10);
	SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(@sitecode);
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = @sitecode;

	-- Create temp tables for logging (following swb_updateBundle pattern)
	IF OBJECT_ID('tempdb..#tblExistingBundleSettings') IS NOT NULL
		DROP TABLE #tblExistingBundleSettings;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	CREATE TABLE #tblExistingBundleSettings(bundleID INT, bundleName VARCHAR(250), customTextEnabled BIT, customTextContent VARCHAR(MAX));
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

	-- Validate bundle exists and belongs to the organization
	IF NOT EXISTS (
		SELECT 1 FROM dbo.tblBundles b
		INNER JOIN dbo.tblParticipants p ON b.participantID = p.participantID
		WHERE b.bundleID = @bundleID AND p.orgcode = @siteCode
	)
	BEGIN
		RAISERROR('Bundle not found or access denied.', 16, 1);
		RETURN -1;
	END

	-- Store existing values for audit logging
	INSERT INTO #tblExistingBundleSettings(bundleID, bundleName, customTextEnabled, customTextContent)
	SELECT bundleID, bundleName, ISNULL(customTextEnabled, 0), customTextContent
	FROM dbo.tblBundles
	WHERE bundleID = @bundleID;

	-- Update bundle settings
	UPDATE dbo.tblBundles
	SET customTextEnabled = @customTextEnabled,
		customTextContent = CASE WHEN @customTextEnabled = 1 THEN @customTextContent ELSE NULL END
	WHERE bundleID = @bundleID;

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Custom Text Enabled changed from ' + CASE WHEN customTextEnabled = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @customTextEnabled = 1 THEN 'Yes' ELSE 'No' END + '.'
	FROM #tblExistingBundleSettings
	WHERE bundleID = @bundleID
	AND customTextEnabled <> @customTextEnabled;

	INSERT INTO #tmpLogMessages(msg)
	SELECT 'Custom Text Content ' + CASE WHEN @customTextEnabled = 1 THEN 'updated' ELSE 'cleared' END + '.'
	FROM #tblExistingBundleSettings
	WHERE bundleID = @bundleID
	AND (
		(@customTextEnabled = 1 AND ISNULL(customTextContent,'') <> ISNULL(@customTextContent,''))
		OR (@customTextEnabled = 0 AND customTextContent IS NOT NULL)
	);

	SELECT @msgjson = 'SWB-' + CAST(bundleID AS VARCHAR(10)) + ' [' + memberCentral.dbo.fn_cleanInvalidXMLChars(bundleName) + '] settings have been updated.'
	FROM #tblExistingBundleSettings
	WHERE bundleID = @bundleID;

	IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
		SET @msgjson = @msgjson + @crlf + 'The following changes have been made:';

		SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
		FROM #tmpLogMessages
		WHERE msg IS NOT NULL;
	END

	-- Log to platformQueue.dbo.queue_mongo (same as swb_updateBundle)
	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');

	-- Clean up temp tables
	IF OBJECT_ID('tempdb..#tblExistingBundleSettings') IS NOT NULL
		DROP TABLE #tblExistingBundleSettings;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
