<cfcomponent output="no">
	
	<cffunction name="initUser" access="public" returntype="struct" hint="sets/resets all variables">
		<cfset var local = structNew()>
		<cfset local.cfcuser = structNew()>

		<cfset local.cookieUsername = CreateObject("component","model.login.login").getCredentialsCookie('username')>
		
		<cfset local.cfcuser["loggedIn"] = 0>
		<cfset local.cfcuser["siteadmin"] = 0>
		<cfset local.cfcuser["superuser"] = 0>
		<cfset local.cfcuser["memberData"] = { memberid=0, IdentifiedAsMemberID=0, profileID=0, depoMemberDataID=0, membertypeid=1, username=local.cookieUsername, 
											   prefix="", firstname="", middlename="", lastname="", suffix="", professionalSuffix="", company="", email="", 
											   memberNumber="", orgcode="", hasMemberPhoto=0, hasMemberPhotoThumb=0 }>
		<cfset local.cfcuser["orgMemberData"] = {}>
		<cfset local.cfcuser["lastLogin"] = "">
		<cfset local.cfcuser["subscriptionData"] = {}>
		<cfset structInsert(local.cfcuser.subscriptionData,"4","",true)>
		<cfset local.cfcuser.subscriptionData["4"] = { TSGroups="", AdminFlag="N", MemberType="0", TSAllowed="0", 
													   mailCode="0", pending="0", renewaldate="", billingState="", billingZip="", billingCountry=1, 
													   displayversion="0", TLAMemberState="", paymentType="C", isDocumentChatEnabled=0, rights="0" }>
	
		<cfreturn local.cfcuser>
	</cffunction>

	<cffunction name="IsLoggedIn" access="public" returntype="boolean" output="false">
		<cfargument name="cfcuser" type="struct" required="yes">
		<cfreturn arguments.cfcuser.loggedIn ?: 0>
	</cffunction>
	
	<cffunction name="IsSuperUser" access="public" returntype="boolean" output="false">
		<cfargument name="cfcuser" type="struct" required="yes">
		<cfreturn arguments.cfcuser.superuser ?: 0>
	</cffunction>

	<cffunction name="IsSiteAdmin" access="public" returntype="boolean" output="false">
		<cfargument name="cfcuser" type="struct" required="yes">
		<cfreturn arguments.cfcuser.siteadmin ?: 0>
	</cffunction>

	<cffunction name="IsSiteAdminByMemberID" access="public" returntype="boolean" output="false">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var qrySACheck = "">

		<cfquery name="qrySACheck" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;
			DECLARE @memberID int = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">;

			select mg.memberID
			from dbo.cache_members_groups as mg
			inner join dbo.ams_groups as g on g.orgID = @orgID 
				and g.groupID = mg.groupID 
				and g.status <> 'D'
				and g.groupName = 'Site Administrators'
				and g.isSystemGroup = 1
			where mg.orgID = @orgID
			and mg.memberID = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySACheck.recordcount gt 0>
	</cffunction>

	<cffunction name="getSubscriptionData" access="public" returntype="any" output="false">
		<cfargument name="cfcuser" type="struct" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="key" type="string" required="yes">

		<cfif StructKeyExists(arguments.cfcuser.subscriptionData,arguments.orgid) and StructKeyExists(arguments.cfcuser.subscriptionData[arguments.orgid],arguments.key)>
			<cfreturn arguments.cfcuser.subscriptionData[arguments.orgID][arguments.key]>
		<cfelse>
			<cfreturn "">
		</cfif>
	</cffunction>

	<cffunction name="getOrgMemberID" access="public" returntype="numeric" output="false">
		<cfargument name="cfcuser" type="struct" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfif isLoggedIn(cfcuser=arguments.cfcuser) and NOT IsSuperUser(cfcuser=arguments.cfcuser) and structKeyExists(arguments.cfcuser.orgMemberData,arguments.orgID)>
			<cfreturn arguments.cfcuser.orgMemberData[arguments.orgID].memberID>
		<cfelseif isLoggedIn(cfcuser=arguments.cfcuser) and IsSuperUser(cfcuser=arguments.cfcuser)>
			<cfreturn arguments.cfcuser.memberdata.memberID>
		<cfelse>
			<cfreturn 0>
		</cfif>
	</cffunction>

	<cffunction name="isCKFinderAllowed" access="public" returntype="boolean" output="false">
		<cfargument name="cfcuser" type="struct" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryCheckEditContent = "">
		<cfset local.groupPrintID = application.objMember.getMemberGroupPrintID(memberID=arguments.cfcuser.memberData.memberID)>

		<cfquery name="qryCheckEditContent" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimespan(0,0,1,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare 
				@groupPrintID int = <cfqueryparam value="#local.groupPrintID#" cfsqltype="CF_SQL_INTEGER">, 
				@siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;

			if exists(
				select 1
				from dbo.cms_siteResourceTypes srt
				inner join dbo.cms_siteResourceTypeFunctions srtf on srtf.resourceTypeID = srt.resourceTypeID
					and srt.resourceType = 'UserCreatedContent'
				inner join dbo.cms_siteResourceFunctions srf on srf.functionID = srtf.functionID
					and srf.functionName = 'editContent'
				inner join dbo.cms_siteResources sr on sr.siteID = @siteID
					and sr.ResourcetypeID = srt.ResourceTypeID
				inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
					and srfrp.siteResourceID = sr.siteResourceID
					and srfrp.functionID = srf.functionID
				inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
					and gprp.rightPrintID = srfrp.rightPrintID 
					and gprp.groupPrintID = @groupPrintID 
				) select cast(1 as bit) as allowed
			else 
				select cast(0 as bit) as allowed;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryCheckEditContent.allowed>
	</cffunction>

	<cffunction name="refreshUserDataIfMemberIDChanged" access="public" returntype="void" output="false">
		<cfargument name="cfcuser" type="struct" required="true">

		<cfset var local = structNew()>

		<cfset local.activeMemberID = application.objCommon.getActiveMemberID(memberID=arguments.cfcuser.memberData.memberID)>

		<cfif local.activeMemberID gt 0 AND local.activeMemberID neq arguments.cfcuser.memberData.memberID>
			<cfset session.cfcuser.memberData.memberID = local.activeMemberID>
			<cfset StructAppend(session.cfcuser, refresh(cfcuser=session.cfcuser, siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID, orgcode=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgcode),true)>
		</cfif>
	</cffunction>
	
	<cffunction name="refresh" access="public" returntype="struct" output="false">
		<cfargument name="cfcuser" type="struct" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgCode" type="string" required="yes">
		
		<cfset var local = StructNew()>
		<cfset local.cfcuser = StructNew()>

		<!--- blank values if org doesnt use fields --->
		<cfset local.cfcuser.memberData = getMemberDetail(memberID=arguments.cfcuser.memberData.memberID, siteID=arguments.siteID)>

		<!--- check super user status --->
		<cfset local.cfcuser.superuser = 0>
		<cfif local.cfcuser.memberData.profileID>
			<cfquery name="local.checknetwork" datasource="#application.dsn.membercentral.dsn#">
				SELECT top 1 networkID
				FROM dbo.ams_networkProfiles
				WHERE profileID = <cfqueryparam value="#local.cfcuser.memberdata.profileid#" cfsqltype="CF_SQL_INTEGER">
				ORDER BY networkID
			</cfquery>
			<cfif local.checknetwork.networkid is 1>
				<cfset local.cfcuser.superuser = 1>
			</cfif>
		</cfif>
		
		<!--- check site admin --->
		<cfset local.cfcuser.siteadmin = 0>
		<cfif local.cfcuser.memberData.profileID>
			<cfset local.cfcuser.siteadmin = local.cfcuser.superuser>
			<cfif not local.cfcuser.siteadmin>
				<cfset local.cfcuser.siteadmin = IsSiteAdminByMemberID(orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID, memberID=local.cfcuser.memberData.memberID)>
			</cfif>
		</cfif>
		
		<!--- load org member data if there is a profile --->
		<cfif local.cfcuser.memberData.profileID and local.cfcuser.superuser is 0>
			<cfquery name="local.qrymemberdetails" datasource="#application.dsn.membercentral.dsn#">
				SELECT s.orgID, m.memberID
				FROM dbo.ams_members AS m 
				INNER JOIN dbo.ams_memberNetworkProfiles AS mnp ON m.memberID = mnp.memberID 
					AND mnp.profileID = <cfqueryparam value="#local.cfcuser.memberData.profileID#" cfsqltype="CF_SQL_INTEGER">
				INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
				INNER JOIN dbo.sites AS s ON s.siteID = mnp.siteID
				WHERE m.status <> 'D'
				AND mnp.status <> 'D' 
				AND np.status <> 'D' 
				AND m.memberID = m.activeMemberID
				ORDER BY s.orgID, m.memberID
			</cfquery>	
			<cfset local.cfcuser.orgMemberData = arguments.cfcuser.orgMemberData>
			<cfloop query="local.qrymemberdetails">
				<cfset local.tmpStr = { memberid=local.qrymemberdetails.memberid }>
				<cfset StructInsert(local.cfcuser.orgMemberData,local.qrymemberdetails.orgID,local.tmpStr,true)>
			</cfloop>
		</cfif>
		
		<!--- TS subscription info --->
		<cfset local.cfcuser.subscriptionData = arguments.cfcuser.subscriptionData>
		<cfset refreshTrialSmithSubscription(cfcuser=local.cfcuser, orgCode=arguments.orgCode)>

		<cfreturn local.cfcuser>
	</cffunction>

	<cffunction name="refreshTrialSmithSubscription" access="public" returntype="void" output="false">
		<cfargument name="cfcuser" type="struct" required="true">
		<cfargument name="orgCode" type="string" required="true">

		<cfset var local = structNew()>

		<!--- TS subscription info --->
		<cfset arguments.cfcuser.subscriptionData = getSubscriptionInfo(subscriptionData=arguments.cfcuser.subscriptionData, depoMemberDataID=arguments.cfcuser.memberData.depoMemberDataID, orgCode=arguments.orgCode)>
	</cffunction>

	<!--- memberkey --->
	<cffunction name="setIdentifiedMemberIDfromMK" access="public" returntype="void" output="false">
		<cfargument name="cfcuser" type="struct" required="true">
		<cfargument name="mk" type="string" required="true">

		<cfset var local = structNew()>

		<cfif NOT isLoggedIn(cfcuser=arguments.cfcuser) and len(arguments.mk)>
			<cftry>
				<cfset local.strMemberKey = deserializeJSON(decrypt(arguments.mk,"M@!6T$", "CFMX_COMPAT", "Hex"))>
				<cfset local.orgID = application.objOrgInfo.getOrgIDFromOrgCode(orgCode=local.strMemberKey.o)>
				<cfset local.identifiedMemberID = application.objMember.getMemberIDByMemberNumber(memberNumber=toString(local.strMemberKey.m), orgID=local.orgID)>

				<cfif local.identifiedMemberID GT 0 AND local.orgID EQ application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID>
					<cfset session.cfcuser.memberdata.IdentifiedAsMemberID = local.identifiedMemberID>

					<!--- set MCIDME cookie --->
					<cfset application.objPlatform.setSignedCookie(cookiename="mcidme", value="#local.identifiedMemberID#|#local.orgID#|#GetTickCount()#", expires="90")>
				</cfif>
			<cfcatch type="Any">
			</cfcatch>
			</cftry>
		</cfif>
	</cffunction>
	<cffunction name="setIdentifiedMemberIDfromID" access="public" returntype="void" output="false">
		<cfargument name="cfcuser" type="struct" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="setCookie" type="boolean" required="false" default="true">

		<cfif NOT isLoggedIn(cfcuser=arguments.cfcuser)>
			<cfset session.cfcuser.memberdata.IdentifiedAsMemberID = arguments.memberID>

			<cfif arguments.memberID gt 0 AND arguments.setCookie>
				<!--- set MCIDME cookie --->
				<cfset application.objPlatform.setSignedCookie(cookiename="mcidme", value="#arguments.memberID#|#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID#|#GetTickCount()#", expires="90")>
			</cfif>
		</cfif>
	</cffunction>
	<cffunction name="getIdentifiedMemberID" access="public" returntype="numeric" output="false">
		<cfargument name="cfcuser" type="struct" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.orgMemberID = getOrgMemberID(cfcuser=arguments.cfcuser, orgID=arguments.orgID)>

		<cfif local.orgMemberID gt 0>
			<cfreturn local.orgMemberID>
		<cfelseif NOT isLoggedIn(cfcuser=arguments.cfcuser) and (session.cfcuser?.memberdata?.IdentifiedAsMemberID ?: 0) gt 0>
			<cfreturn session.cfcuser.memberdata.IdentifiedAsMemberID>
		<cfelse>
			<cfreturn 0>
		</cfif>
	</cffunction>

	<cffunction name="getMemberDetail" access="public" returntype="struct" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
	
		<cfset var local = structNew()>
		<cfset local.memberData = structNew()>

		<cfquery name="local.memberdetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @memberID int;
			SET @memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">;
			SELECT @orgID = orgID from dbo.ams_members where memberID = @memberID;

			SELECT TOP 1 m.memberID, m.membertypeid, np.profileID, np.depoMemberDataID, m.orgID, 
				case when o.hasPrefix = 1 then m.prefix else '' end as prefix, 
				m.firstname, 
				case when o.hasMiddleName = 1 then m.middlename else '' end as middlename, 
				m.lastname, 
				case when o.hasSuffix = 1 then m.suffix else '' end as suffix, 
				case when o.hasProfessionalSuffix = 1 then m.professionalSuffix else '' end as professionalSuffix, 
				m.company, m.memberNumber, me.email, o.orgcode, np.username, m.hasMemberPhoto, m.hasMemberPhotoThumb
			FROM dbo.ams_members AS m 
			INNER JOIN dbo.organizations as o on o.orgID = @orgID
			LEFT OUTER JOIN dbo.ams_memberNetworkProfiles AS mnp 
				INNER JOIN dbo.ams_networkProfiles AS np ON mnp.profileID = np.profileID
					AND np.status <> 'D' 
				ON m.memberID = mnp.memberID 
				AND mnp.status <> 'D' 
				AND mnp.siteID = <cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">
			INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberid = m.memberid
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
				and metag.memberID = me.memberID 
				AND metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = @orgID 
				and metagt.emailTagTypeID = metag.emailTagTypeID 
				AND metagt.emailTagType = 'Primary'
			WHERE m.status <> 'D'
			AND m.memberID = @memberID
			AND m.memberID = m.activeMemberID;
		</cfquery>

		<cfset local.memberData.memberID = val(local.memberdetails.memberID)>
		<cfif local.memberData.memberID>
			<cfset local.memberData.IdentifiedAsMemberID = local.memberData.memberID>
		</cfif>
		<cfset local.memberData.profileID = val(local.memberdetails.profileID)>
		<cfset local.memberData.depoMemberDataID = val(local.memberdetails.depoMemberDataID)>
		<cfset local.memberData.membertypeid = val(local.memberdetails.membertypeid)>
		<cfset local.memberData.prefix = local.memberdetails.prefix>
		<cfset local.memberData.firstname = local.memberdetails.firstname>
		<cfset local.memberData.middlename = local.memberdetails.middlename>
		<cfset local.memberData.lastname = local.memberdetails.lastname>
		<cfset local.memberData.suffix = local.memberdetails.suffix>
		<cfset local.memberData.professionalSuffix = local.memberdetails.professionalSuffix>
		<cfset local.memberData.company = local.memberdetails.company>
		<cfset local.memberData.email = local.memberdetails.email>
		<cfset local.memberData.memberNumber = local.memberdetails.memberNumber>
		<cfset local.memberData.orgcode = local.memberdetails.orgcode>
		<cfset local.memberData.username = local.memberdetails.username>
		<cfset local.memberData.hasMemberPhoto = val(local.memberdetails.hasMemberPhoto)>
		<cfset local.memberData.hasMemberPhotoThumb = val(local.memberdetails.hasMemberPhotoThumb)>

		<cfif local.memberData.memberID and not local.memberData.depoMemberDataID>
			<!--- See if proc can find an account in waiting --->
			<cfstoredproc procedure="ams_getTLASITESDepoMemberDataIDByMemberID" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.memberData.memberID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">
				<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.depoMemberDataID">
			</cfstoredproc>
			<cfset local.memberData.depoMemberDataID = val(local.depoMemberDataID)>
		</cfif>



		<cfreturn local.memberData>
	</cffunction>
	
	<cffunction name="getSubscriptionInfo" access="public" returntype="struct" output="false">
		<cfargument name="subscriptionData" type="struct" required="true">
		<cfargument name="depoMemberDataID" type="numeric" required="true">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="isloggedIn" type="boolean" required="false" default="#isLoggedIn(cfcuser=session.cfcuser)#">
	
		<cfset var local = structNew()>
		<cfset local.subscriptionData = structNew()>
		<cfset local.subscriptionData = arguments.subscriptionData>

		<cfif not structKeyExists(local.subscriptionData, "4")>
			<cfset structInsert(local.cfcuser.subscriptionData,"4","",true)>
		</cfif>
		
		<cfset local.subscriptionData["4"] = { TSGroups="", AdminFlag="N", MemberType="0", TSAllowed="0", mailCode="0", pending="0", 
			renewaldate="", billingState="", billingZip="", displayversion="0", TLAMemberState="", paymentType="C", isDocumentChatEnabled=0, rights="0" }>

		<cfif arguments.depoMemberDataID>
			<cfquery name="local.qryTSInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				SELECT TOP 1 d.AdminFlag2, d.membertype, d.mailCode, d.pending, d.renewalDate, d.billingState, d.billingZip, t.DisplayVersion, 
					d.TLAMemberState, d.paymentType, d.isDocumentChatEnabled, t.rights, 
					case 
					when d.AdminFlag2 = 'Y' then 1
					when d.membertype in (7,9) then 0 
					else d.TSAllowed 
					end as TSAllowed
				FROM dbo.depomemberdata AS d 
				INNER JOIN dbo.membertype AS t ON d.membertype = t.membertypeID 
				WHERE d.depomemberdataID = <cfqueryparam value="#arguments.depomemberdataID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfset local.subscriptionData["4"].AdminFlag = local.qryTSInfo.AdminFlag2>
			<cfset local.subscriptionData["4"].MemberType = local.qryTSInfo.membertype>
			<cfset local.subscriptionData["4"].TSAllowed = local.qryTSInfo.TSAllowed>
			<cfset local.subscriptionData["4"].MailCode = local.qryTSInfo.mailCode>
			<cfset local.subscriptionData["4"].pending = local.qryTSInfo.pending>
			<cfset local.subscriptionData["4"].renewaldate = dateformat(local.qryTSInfo.renewaldate,"mm/dd/yyyy")>
			<cfset local.subscriptionData["4"].billingState = local.qryTSInfo.billingState>
			<cfset local.subscriptionData["4"].billingZip = local.qryTSInfo.billingZip>
			<cfset local.subscriptionData["4"].TLAMemberState = local.qryTSInfo.TLAMemberState>
			<cfset local.subscriptionData["4"].displayversion = local.qryTSInfo.DisplayVersion>
			<cfset local.subscriptionData["4"].rights = local.qryTSInfo.rights>
			<cfset local.subscriptionData["4"].paymentType = local.qryTSInfo.paymentType>
			<cfset local.subscriptionData["4"].isDocumentChatEnabled = local.qryTSInfo.isDocumentChatEnabled>

			<!--- Add user to any missing default TS groups for orgcode --->
			<cfstoredproc procedure="account_addMissingDefaultTSGroups" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataID#" null="no">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgCode#" null="no">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryTSInfo.TLAMemberState#" null="no">
			</cfstoredproc>

			<cfquery name="local.getTSgroups" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				SELECT g.groupid
				FROM dbo.depogroups as g
				INNER JOIN dbo.deposourcegroup as s on g.groupid = s.groupid
				WHERE s.depomemberdataid = <cfqueryparam value="#arguments.depomemberdataID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfset local.subscriptionData["4"].TSGroups = valuelist(local.getTSgroups.groupid)>
		</cfif>

		<cfset local.subscriptionData["4"].TrialSmithAllowed = TrialSmithAllowed(TSAllowed=local.subscriptionData["4"].TSAllowed)>
		<cfset local.subscriptionData["4"].TrialSmithDisabled = TrialSmithDisabled(MailCode=local.subscriptionData["4"].MailCode)>
		<cfset local.subscriptionData["4"].TrialSmithPending = TrialSmithPending(isloggedIn=arguments.isloggedIn, orgCode=arguments.orgCode, pending=local.subscriptionData["4"].Pending)>
		<cfset local.subscriptionData["4"].TrialSmithExpired = TrialSmithExpired(subscriptionData=local.subscriptionData["4"])>
		<cfset local.subscriptionData["4"].TrialSmithNoPlan = TrialSmithNoPlan(membertype=local.subscriptionData["4"].membertype)>
		<cfset local.subscriptionData["4"].TrialSmithFirmPlanNonMaster = TrialSmithFirmPlanNonMaster(depomemberdataID=arguments.depomemberdataID)>

		<cfreturn local.subscriptionData>
	</cffunction>	

	<!--- security key functions. cant use csrfgeneratetoken because as of 4/22/19 it doesnt work under lucee with sessionCluster enabled --->
	<cffunction name="renderSecurityKeyElement" access="public" returntype="string" output="false">
		<cfif not structKeyExists(session,"MCSecurityKey")>
			<cfset session["MCSecurityKey"] = hash(createUUID(), 'SHA-512', 'UTF-8')>
		</cfif>
		<cfreturn '<input type="hidden" name="mcSecKey" id="f#hash(GetTickCount(),'SHA-512','UTF-8')#" value="#session.MCSecurityKey#">'>
	</cffunction>
	<cffunction name="verifySecurityKey" access="public" returntype="boolean" output="false">
		<cfargument name="mcSecKey" type="string" required="yes">
		<cfif not structKeyExists(session,"MCSecurityKey") OR compare(session.MCSecurityKey,arguments.mcSecKey) is not 0>
			<cfreturn false>
		<cfelse>
			<cfreturn true>
		</cfif>
	</cffunction>
	<cffunction name="login_getUsername" access="public" returntype="string" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var username = "">
		<cfset var isDefault = "">

		<cfstoredproc procedure="ams_up_getUsername" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="username">
			<cfprocparam type="out" cfsqltype="CF_SQL_BIT" variable="isDefault">
		</cfstoredproc>

		<cfreturn username>
	</cffunction>
	<cffunction name="login_getUsernameSuperUser" access="public" returntype="string" output="false">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var username = "">

		<cfstoredproc procedure="ams_up_getUsernameSuperUser" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="username">
		</cfstoredproc>

		<cfreturn username>
	</cffunction>
	<cffunction name="login_isUsernameAvailable" access="public" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="username" type="string" required="true">

		<cfset var isAvailable = 0>

		<cfstoredproc procedure="ams_up_isUsernameAvailable" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.username#">
			<cfprocparam type="out" cfsqltype="CF_SQL_BIT" variable="isAvailable">
		</cfstoredproc>

		<cfreturn isAvailable>
	</cffunction>
	<cffunction name="login_setUsername" access="public" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="username" type="string" required="true">
		<cfargument name="recordedByMemberID" type="numeric" required="true">

		<cfstoredproc procedure="ams_up_setUsername" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.username#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
		</cfstoredproc>
	</cffunction>
	<cffunction name="login_setUsernameSuperUser" access="public" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="networkProfileID" type="numeric" required="true">
		<cfargument name="username" type="string" required="true">

		<cfstoredproc procedure="ams_up_setUsernameSuperUser" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.networkProfileID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.username#">
		</cfstoredproc>
	</cffunction>
	<cffunction name="login_checkPassword" access="public" returntype="boolean" output="no">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="password" type="string" required="true">

		<cfset var isCorrect = 0>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_up_checkPassword">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.password#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_BIT" variable="isCorrect">
		</cfstoredproc>

		<cfreturn isCorrect>
	</cffunction>
	<cffunction name="login_setPassword" access="public" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="password" type="string" required="true">
		<cfargument name="recordedByMemberID" type="numeric" required="true">

		<cfstoredproc procedure="ams_up_setPassword" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.password#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.recordedByMemberID#">
		</cfstoredproc>
	</cffunction>
	<cffunction name="login_setPasswordSuperUser" access="public" returntype="void" output="false">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="networkProfileID" type="numeric" required="true">
		<cfargument name="password" type="string" required="true">

		<cfstoredproc procedure="ams_up_setPasswordSuperUser" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.networkProfileID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.password#">
		</cfstoredproc>
	</cffunction>
	<cffunction name="login_attemptSuperUser" access="public" returntype="struct" output="false">
		<cfargument name="sessionID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="username" type="string" required="true">
		<cfargument name="password" type="string" required="true">
		<cfargument name="loginAsMemberID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.strResult = { result="", memberID=0 }>

		<cfstoredproc procedure="ams_up_attemptSuperUser" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.sessionID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.username#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.password#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.loginAsMemberID#">
			<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="local.strResult.result">
			<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.strResult.memberID">
		</cfstoredproc>

		<cfreturn local.strResult>
	</cffunction>
	<cffunction name="login_attemptUser" access="public" returntype="struct" output="false">
		<cfargument name="sessionID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="username" type="string" required="true">
		<cfargument name="password" type="string" required="true">
		<cfargument name="loginAsMemberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strResult = { result="", memberID=0, memberStatus="" }>

		<cfstoredproc procedure="ams_up_attemptUser" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.sessionID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.username#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.password#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.loginAsMemberID#">
			<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="local.strResult.result">
			<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.strResult.memberID">
			<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="local.strResult.memberStatus">
		</cfstoredproc>

		<cfreturn local.strResult>
	</cffunction>

	
	<!--- TrialSmith specific functions --->	
	<cffunction name="TrialSmithAllowed" access="private" returntype="boolean" output="no" hint="Tests whether a user can access TrialSmith buckets">
		<cfargument name="TSAllowed" type="numeric" required="yes">

		<cfreturn arguments.TSAllowed is 1>
	</cffunction>
	<cffunction name="TrialSmithDisabled" access="private" returntype="boolean" output="no" hint="Tests whether a user has been disabled by TrialSmith">
		<cfargument name="MailCode" type="string" required="yes">

		<cfreturn arguments.MailCode eq "I">
	</cffunction>
	<cffunction name="TrialSmithPending" access="private" returntype="boolean" output="no" hint="Tests whether a user is pending">
		<cfargument name="isloggedIn" type="boolean" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="pending" type="numeric" required="yes">
		
		<cfif arguments.isloggedIn and not listfindNoCase("MC,TS",arguments.orgcode)> 
			<cfreturn false>
		<cfelse>
			<cfreturn arguments.pending is 0>
		</cfif>
	</cffunction>
	<cffunction name="TrialSmithNoPlan" access="private" returntype="boolean" output="no" hint="Tests whether a user has a TrialSmith plan">
		<cfargument name="membertype" type="numeric" required="yes">

		<cfreturn arguments.membertype is 0>
	</cffunction>
	<cffunction name="TrialSmithExpired" access="private" returntype="boolean" output="no" hint="Tests whether a user's TrialSmith access has expired">
		<cfargument name="subscriptionData" type="struct" required="yes">
		
		<cfif isDate(arguments.subscriptionData.renewaldate)>
			<cfreturn datecompare(arguments.subscriptionData.renewaldate,now()) lt 0>
		<cfelse>
			<cfreturn 1>
		</cfif>
	</cffunction>
	<cffunction name="TrialSmithFirmPlanNonMaster" access="private" returntype="boolean" output="no" hint="Tests whether a user is a firm plan non-master">
		<cfargument name="depomemberdataID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cfif arguments.depomemberdataID gt 0>		
			<cfquery name="local.checkFirm" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				select top 1 firmPlanLinkID 
				from dbo.tlaFirmPlanLink 
				where depomemberdataID = <cfqueryparam value="#arguments.depomemberdataID#" cfsqltype="CF_SQL_INTEGER">
				and isMaster = 0
			</cfquery>
			<cfreturn local.checkFirm.recordcount>
		<cfelse>
			<cfreturn 0>
		</cfif>
	</cffunction>

</cfcomponent>