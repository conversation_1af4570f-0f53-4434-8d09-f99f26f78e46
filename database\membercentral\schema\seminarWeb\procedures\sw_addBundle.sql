ALTER PROC dbo.sw_addBundle
@orgcode varchar(10),
@isSWOD bit,
@bundleName varchar(200),
@bundleSubTitle varchar(200),
@bundleDesc varchar(max),
@dateCatalogStart smalldatetime,
@dateCatalogEnd smalldatetime,
@status char(1),
@freeRateDisplay varchar(5),
@isPriceBasedOnActual bit,
@customTextEnabled bit = 0,
@customTextContent varchar(max) = NULL,
@recordedByMemberID int,
@bundleID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @participantID int, @orgID int, @siteID int, @programCode varchar(15), @nowDate datetime = getdate(), 
		@resourceTypeID int, @siteResourceID int, @semWebCatalogSiteResourceID int;

	SELECT @resourceTypeID = memberCentral.dbo.fn_getResourceTypeID('SWBundle');
	SET @participantID = dbo.fn_getParticipantIDFromOrgCode(@orgcode);
	SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = @orgcode;
	SET @semWebCatalogSiteResourceID = membercentral.dbo.fn_getSiteResourceIDForResourceType('SemWebCatalog', @siteID);
	
	EXEC memberCentral.dbo.getUniqueCode @uniqueCode=@programCode OUTPUT;

	BEGIN TRAN;
		EXEC memberCentral.dbo.cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,  
			@isVisible=1, @parentSiteResourceID=@semWebCatalogSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;
			
		INSERT INTO dbo.tblBundles (participantID, isSWOD, bundleName, bundleSubTitle, programCode, bundleDesc, dateCatalogStart, dateCatalogEnd,
			[status], dateCreated, isPriceBasedOnActual, freeRateDisplay, siteResourceID, submittedByMemberID, customTextEnabled, customTextContent)
		VALUES (@participantID, @isSWOD, @bundleName, @bundleSubTitle, @programCode, @bundleDesc, @dateCatalogStart, @dateCatalogEnd, @status,
			@nowDate, @isPriceBasedOnActual, @freeRateDisplay, @siteResourceID, @recordedByMemberID, @customTextEnabled, 
			CASE WHEN @customTextEnabled = 1 THEN @customTextContent ELSE NULL END);
		SELECT @bundleID = SCOPE_IDENTITY();

		SET @programCode = 'SWB-' + CAST(@bundleID AS varchar(10));

		UPDATE dbo.tblBundles
		SET programCode = @programCode
		WHERE bundleID = @bundleID;

		INSERT INTO dbo.tblBundlesOptIn (bundleID, participantID, isPriceBasedOnActual, freeRateDisplay, dateAdded, isActive)
		VALUES (@bundleID, @participantID, @isPriceBasedOnActual, @freeRateDisplay, @nowDate, 1);

		EXEC dbo.swb_addBillingLogForBundleCreation @orgCode=@orgCode, @bundleID=@bundleID, @recordedByMemberID=@recordedByMemberID;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"auditLog", "d": {
			"AUDITCODE":"SW",
			"ORGID":' + cast(@orgID as varchar(10)) + ',
			"SITEID":' + cast(@siteID as varchar(10)) + ',
			"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
			"ACTIONDATE":"' + convert(varchar(20),@nowDate,120) + '",
			"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('SWB-' + cast(bundleID as varchar(20)) + ' [' + bundleName + '] has been created as ['+ CASE WHEN @isSWOD = 1 THEN 'SWOD' ELSE 'SWL' END +'].'),'"','\"') + '" } }'
		FROM dbo.tblBundles
		WHERE bundleID = @bundleID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
