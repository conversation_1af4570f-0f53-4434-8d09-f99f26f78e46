ALTER PROC dbo.up_Document_Buy
@documentID int,
@depomemberdataid int,
@membertype int,
@billingstate varchar(5),
@billingzip varchar(25),
@orgcode varchar(10),
@allowPCuse bit,
@incFeePerUpload bit,
@billingPlanID int,
@caseref varchar(600),
@linksource varchar(50),
@linkterms varchar(100),
@statsSessionID int,
@transactionID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @pPrice numeric(9,2), @pSalesTaxAmount numeric(9,2), @feePerUpload numeric(9,2), @acctCode varchar(4),
		@depoDocumentsPriceExceptionID int, @royaltyArrangementid int, @deliveryTypeID int = 4;
	SET @transactionID = 0;

	-- get price for doc
	select *
	into #tmpGetPrice
	from dbo.fn_Documents_getPrice(@documentID,@membertype,@billingstate,@billingzip,@orgcode,@depomemberdataid);

	SELECT @pPrice = price, @pSalesTaxAmount = isnull(salestaxamount,0),
		@depoDocumentsPriceExceptionID = depoDocumentsPriceExceptionID, 
		@royaltyArrangementid = royaltyArrangementid
	from #tmpGetPrice;

	-- if price is not found
	IF NOT EXISTS (select pricefound from #tmpGetPrice where pricefound = 1)
		RETURN -1;

	IF @incFeePerUpload = 1 AND @billingPlanID IS NOT NULL BEGIN
		SELECT @acctCode = dt.acctCode
		FROM dbo.depodocuments AS d
		INNER JOIN dbo.depodocumenttypes AS dt ON dt.typeid = d.documenttypeid
		WHERE d.documentID = @documentID;
		
		SELECT @feePerUpload = feePerUpload
		FROM dbo.depomemberDataCaseBillingPlans
		WHERE billingPlanID = @billingPlanID;

		SET @pPrice = @pPrice + ISNULL(@feePerUpload,0);
		SELECT @pSalesTaxAmount = dbo.fn_tax_getTax('TS',@billingstate,@billingzip,@acctCode,@pPrice);
	END

	-- get purchase credits
	DECLARE @pcbalance numeric(9,2);
	SELECT @pcbalance = balance from dbo.fn_Documents_getPurchaseCredits(@depomemberdataid);

	BEGIN TRAN;
		-- get transgroup
		DECLARE @transgroup int	
		SELECT @transgroup = MAX(transgroup) + 1 FROM dbo.depoTransactions

		-- check permission and add if needed
		IF (dbo.fn_Documents_checkPermissionsToDoc(@documentID,@depomemberdataid) = 0)
		BEGIN
			insert into dbo.depoPermissions (depomemberdataid, documentid, dateadded)
			values (@depomemberdataid,@documentID,getdate())
			
		END

		-- insert sale of document
		INSERT INTO dbo.depoTransactions (depomemberdataid, documentID, [Description], AmountBilled, AccountCode, 
			CaseRef, SourceState, deliveryType, salestaxamount, orgcode, transgroup, 
			depodocumentspriceexception, linksource, linkterms, royaltyArrangementid, stateForTax, zipForTax, statsSessionID)
		SELECT TOP 1 @depomemberdataid, @documentID, dt.[description] + ' - ' + d.expertname, @pPrice, dt.acctcode, 
			@caseref, d.[state], @deliveryTypeID, isnull(@pSalesTaxAmount,0), @orgcode, @transgroup, 
			@depoDocumentsPriceExceptionID, @linksource, @linkterms, @royaltyArrangementid, @billingstate, @billingzip, @statsSessionID
		FROM dbo.depoDocuments d
		INNER JOIN dbo.DepoDocumentTypes dt on dt.TypeID = d.documentTypeID
		WHERE d.documentID = @documentID;		

		-- get new transactionid
		SELECT @transactionID = SCOPE_IDENTITY();
		

		-- can we use whole purchase credit?	
		IF @allowPCuse = 1 AND @pPrice > 0 AND @pcbalance >= @pPrice
		BEGIN
			DECLARE @reversePrice numeric(9,2), @reverseTax numeric(9,2)
			SELECT @reversePrice = @pPrice * -1
			
			SELECT @reverseTax = @pSalesTaxAmount * -1
			
			-- insert pc transaction
			INSERT INTO depoTransactions (depomemberdataid, documentID, [Description], AmountBilled, AccountCode, 
				CaseRef, SourceState, deliveryType, salestaxamount, orgcode, transgroup, 
				linksource, linkterms, royaltyArrangementid, Reversable, PurchaseCreditFlag, stateForTax, zipForTax, statsSessionID)
			SELECT TOP 1 @depomemberdataid, @documentID, 'Purchase Credit Applied from Document Contributions', 
				@reversePrice, dt.acctcode, @caseref, d.[state], @deliveryTypeID, isnull(@reverseTax,0), @orgcode, @transgroup,
				@linksource, @linkterms, @royaltyArrangementid, 'N', 'Y', @billingstate, @billingzip, @statsSessionID
			FROM dbo.depoDocuments d
			INNER JOIN dbo.DepoDocumentTypes dt on dt.TypeID = d.documentTypeID
			WHERE d.documentID = @documentID
			
			-- deduct from PC
			insert into PurchaseCredits (DepomemberDataId, StateToCredit, StateCreditAmount, PurchaseCreditAmount, 
				CreditDescription, DocumentID, transactionid)
			SELECT TOP 1 @depomemberdataid, d.[State], 0, @reversePrice, 'Credit Deducted for Purchase of document ' + CAST(@documentID as varchar(10)), @documentID, @transactionID
			FROM dbo.depoDocuments d
			WHERE d.documentID = @documentID;
		END
		
		-- can we use partial purchase credit?	
		IF @allowPCuse = 1 AND @pcbalance > 0 AND @pcbalance < @pPrice
		BEGIN
			-- get tax
			DECLARE @pc_salestaxamount numeric(9,2);

			SELECT @acctCode = dt.acctcode
			FROM dbo.depoDocuments d
			INNER JOIN dbo.depoDocumentTypes AS dt ON dt.TypeID = d.DocumentTypeID
			WHERE d.DocumentID = @documentID;

			SELECT @pc_salestaxamount = isnull(dbo.fn_tax_getTax('TS',@billingstate,@billingzip,@acctCode,@pcbalance),0);
			
			DECLARE @reverseBalance numeric(9,2), @reverseBalanceTax numeric(9,2);
			SELECT @reverseBalance = @pcbalance * -1;
			
			SELECT @reverseBalanceTax = @pc_salestaxamount * -1;
			

			-- insert pc transaction
			INSERT INTO depoTransactions (depomemberdataid, documentID, [Description], AmountBilled, 
				AccountCode, CaseRef, SourceState, deliveryType, SalesTaxAmount, Reversable, 
				PurchaseCreditFlag, orgcode, transgroup, linksource, linkterms, royaltyArrangementid, stateForTax, zipForTax, statsSessionID)
			SELECT TOP 1 @depomemberdataid, @documentID, 'Purchase Credit Applied from Document Contributions', @reverseBalance, 
				dt.acctcode, @caseref, d.[state], @deliveryTypeID, isnull(@reverseBalanceTax,0), 'N', 
				'Y', @orgcode, @transgroup, @linksource, @linkterms, @royaltyArrangementid, @billingstate, @billingzip, @statsSessionID
			FROM dbo.depoDocuments d
			INNER JOIN dbo.DepoDocumentTypes dt on dt.TypeID = d.documentTypeID
			WHERE d.documentID = @documentID;
			
			-- deduct from PC
			insert into dbo.PurchaseCredits (DepomemberDataId, StateToCredit, StateCreditAmount, PurchaseCreditAmount, 
				CreditDescription, DocumentID, transactionid)
			SELECT TOP 1 @depomemberdataid, d.[State], 0, @reverseBalance, 'Credit Deducted for Purchase of document ' + CAST(@documentID as varchar(10)), @documentID, @transactionID
			FROM dbo.depoDocuments d
			WHERE d.documentID = @documentID;
		END
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpGetPrice') IS NOT NULL
		DROP TABLE #tmpGetPrice;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
