<cfcomponent output="false">
   
   <cffunction name="getSubmissions" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any">
		<cfargument name="mode" type="string" required="false" default="list">
		
		<cfset var local = structNew()>

		<cfif arguments.mode EQ 'grid'>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"submissionID")>
			<cfset arrayAppend(local.arrCols,"firstName")>
			<cfset arrayAppend(local.arrCols,"lastName")>
			<cfset arrayAppend(local.arrCols,"memberNumber")>
			<cfset arrayAppend(local.arrCols,"submissionType")>
			<cfset arrayAppend(local.arrCols,"eventDate")>
			<cfset arrayAppend(local.arrCols,"createdDate")>
			<cfset arrayAppend(local.arrCols,"submissionstatus")>
			<cfset arrayAppend(local.arrCols,"isMember")>
			<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">
		</cfif>
		
		<cfset arguments.event.paramValue('submissionId',0)>
		<cfif NOT isSimpleValue(arguments.event.getValue('submissionId')) OR NOT isValid("integer",arguments.event.getValue('submissionId')) or arguments.event.getValue('submissionId') lt 0>
			<cflocation url="/?pg=#arguments.event.getValue('pg','main')#" addtoken="false">
		</cfif>

		<cfquery name="local.qrySubmissions" datasource="#application.dsn.customapps.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			IF OBJECT_ID('tempdb..##tmpSubmissions') IS NOT NULL 
				DROP TABLE ##tmpSubmissions;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgId')#">,
				@totalCount int;
			
			<cfif arguments.mode EQ 'grid'>
				DECLARE @posStart int, @posStartAndCount int;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">
				SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
			</cfif>
			
			SELECT DISTINCT s.submissionId, s.submissionTypeId, st.submissionType, s.submissionStatusId, ss.submissionStatus, s.achievementDescription,
				s.achievementDate, s.appointedBy, s.role, s.termsOfService, s.appointedDate, s.appointmentType, s.awardName, s.awardOrganization,
				s.awardOrgDetails, s.awardedDate, s.othersHonoured, s.previousPosition, s.previousFirm, s.newPosition, s.newFirm, s.movedDate,
				s.pressReleaseFileName, s.pressReleaseFileDocumentId, s.memberID, m2.memberid as originalMemberID, s.isMerged, s.usePhotoForProfile,
				s.submissionPhotoName, s.submittedByName, s.submittedByEmail, s.submittedByPhone, s.submittedByTitle, s.createdDate, s.modifiedDate,
				s.isStatusUpdateEmailSent
				,(select firstname + ' ' + lastName from membercentral.dbo.ams_members WHERE memberid = s.lastModifiedBy) as lastModifiedBy
				,ISNULL((
					select CASE WHEN mg.autoId	> 0 THEN 'Yes' ELSE 'No' END 
					from membercentral.dbo.cache_members_groups as mg 
					INNER JOIN membercentral.dbo.ams_members as m on m.orgID = @orgID
						and m.memberid = mg.memberid
						and m.memberid = m.activememberID 
						and m.[status] = 'A'
						and m.memberid = m2.memberid
					INNER JOIN membercentral.dbo.ams_groups as g on g.orgID = @orgID
						and g.groupID = mg.groupID 
						and g.uid= '1F638ED0-AA33-4D59-BB93-E60C6B2A8A15'
					where mg.orgID = @orgID
					),'No') as IsMember
				, m2.firstName + ' ' + m2.lastname + ' (' + m2.memberNumber +')' as memberName
				, m2.firstName + ' ' + m2.lastname as memberFullName
				, m2.firstName
				, m2.lastName
				, m2.memberNumber
				, ISNULL(pl.licenseNumber,'') as bpr
				, case when st.submissionType = 'Achievement' then s.achievementDate
					when st.submissionType = 'Appointment' then s.appointedDate
					when st.submissionType = 'Award/Recognition' then s.awardedDate
					when  st.submissionType = 'Career Move' then s.movedDate
					end as eventDate	
			INTO ##tmpSubmissions
			FROM dbo.TNBAR_submissions s
			INNER JOIN membercentral.dbo.ams_members m on m.memberid = 
				CASE WHEN ISNULL(s.isMerged,0) = 0 THEN s.memberID ELSE s.originalMemberID END and m.orgid = @orgID
			inner join membercentral.dbo.ams_members m2 on m2.memberid = m.activememberID
			INNER JOIN dbo.TNBAR_submissionStatuses ss on ss.submissionStatusID = s.submissionStatusID
			INNER JOIN dbo.TNBAR_submissionTypes st on st.submissionTypeID = s.submissionTypeID
			LEFT JOIN (select pl.memberid,pl.LicenseNumber 
						from membercentral.dbo.ams_memberProfessionalLicenses pl 	
						INNER JOIN membercentral.dbo.ams_memberProfessionalLicenseTypes plt on plt.PLTypeID = pl.PLTypeID	and plt.orgID = @orgID
						INNER JOIN membercentral.dbo.ams_memberProfessionalLicenseStatuses pls on pls.PLStatusID = pl.PLStatusID
							and pls.orgID = @orgID
							and plt.PLName = 'Tennessee') as pl on m.memberid = pl.memberid
			WHERE 1 = 1
			<cfif arguments.event.valueExists('submissionId') AND val(arguments.event.getValue('submissionId',0)) GT 0>
				AND s.submissionId = <cfqueryparam value="#arguments.event.getValue('submissionId')#" cfsqltype="cf_sql_integer"/>
			</cfif>
			<cfif arguments.event.valueExists('bpr')>
				AND ISNULL(pl.licenseNumber,'') like <cfqueryparam value="%#arguments.event.getValue('bpr')#%" cfsqltype="cf_sql_varchar" />
			</cfif>
			<cfif arguments.event.valueExists('firstName')>
				AND m.firstName like <cfqueryparam value="%#arguments.event.getValue('firstName')#%" cfsqltype="cf_sql_varchar" />
			</cfif>
			<cfif arguments.event.valueExists('lastName')>
				AND m.lastName like <cfqueryparam value="%#arguments.event.getValue('lastName')#%" cfsqltype="cf_sql_varchar" />
			</cfif>
			<cfif arguments.event.valueExists('company')>
				AND m.company like <cfqueryparam value="%#arguments.event.getValue('company')#%" cfsqltype="cf_sql_varchar" />
			</cfif>
			<cfif arguments.event.valueExists('submittedByName')>
				AND s.submittedByName like <cfqueryparam value="%#arguments.event.getValue('submittedByName')#%" cfsqltype="cf_sql_varchar" />
			</cfif>
			<cfif arguments.event.valueExists('submissionTypeId') AND len(arguments.event.getValue('submissionTypeId'))>
				AND s.submissionTypeId IN(<cfqueryparam value="#arguments.event.getValue('submissionTypeId')#" cfsqltype="cf_sql_integer"  list="yes"/>)
			</cfif>
			<cfif arguments.event.valueExists('submissionStatusId')>
				AND s.submissionStatusId IN(<cfqueryparam value="#arguments.event.getValue('submissionStatusId')#" cfsqltype="cf_sql_integer"  list="yes"/>)
			</cfif>
			<cfif len(arguments.event.getTrimValue('createdDateFrom',''))>
				AND s.createdDate >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('createdDateFrom')#">
			</cfif>
			<cfif len(arguments.event.getTrimValue('createdDateTo',''))>
				AND s.createdDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('createdDateTo')# 23:59:59.997">
			</cfif>
			<cfif len(arguments.event.getTrimValue('keyword',''))>
				AND ( m.firstName like <cfqueryparam value="#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" /> OR
					m.lastName like <cfqueryparam value="#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
					OR s.achievementDescription like <cfqueryparam value="%#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
					OR s.appointedBy like <cfqueryparam value="%#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
					OR s.role like <cfqueryparam value="%#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
					OR s.awardName like <cfqueryparam value="%#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
					OR s.awardOrganization like <cfqueryparam value="%#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
					OR s.newFirm like <cfqueryparam value="%#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
					OR s.newPosition like <cfqueryparam value="%#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
					OR s.previousFirm like <cfqueryparam value="%#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
					OR s.previousPosition like <cfqueryparam value="%#arguments.event.getValue('keyword')#%" cfsqltype="cf_sql_varchar" />
				)
			</cfif>
			<cfif len(arguments.event.getTrimValue('eventDateFrom',''))>
				AND case when st.submissionType = 'Achievement' then s.achievementDate
				when st.submissionType = 'Appointment' then s.appointedDate
				when st.submissionType = 'Award/Recognition' then s.awardedDate
				when st.submissionType = 'Career Move' then s.movedDate
				end >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('eventDateFrom')#">
			</cfif>
			<cfif len(arguments.event.getTrimValue('eventDateTo',''))>
				AND case when st.submissionType = 'Achievement' then s.achievementDate
				when st.submissionType = 'Appointment' then s.appointedDate
				when st.submissionType = 'Award/Recognition' then s.awardedDate
				when st.submissionType = 'Career Move' then s.movedDate
				end <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('eventDateTo')# 23:59:59.997">
			</cfif>;
			
			SET @totalCount = @@ROWCOUNT;

			<cfif arguments.mode EQ 'grid'>
				SELECT tmp.*, @totalCount as totalCount
				FROM (
					SELECT *, ROW_NUMBER() OVER (order by #local.orderby#) as row
					FROM ##tmpSubmissions
				) AS tmp 
				WHERE row >= @posStart AND row <= @posStartAndCount
				ORDER BY row;
			<cfelse>
				SELECT tmp.*, @totalCount as totalCount
				FROM (
					SELECT tmp2.*, ROW_NUMBER() OVER (order by eventDate DESC) as row
					FROM ##tmpSubmissions as tmp2
				) AS tmp 
				<cfif arguments.event.valueExists('startrow') AND arguments.event.getValue('startrow',0) GTE 0>
					WHERE row >= #arguments.event.getValue('startrow')# AND row <= #arguments.event.getValue('startrow') + arguments.event.getValue('count')#
				</cfif>
				ORDER BY row;
			</cfif>
			
			IF OBJECT_ID('tempdb..##tmpSubmissions') IS NOT NULL 
				DROP TABLE ##tmpSubmissions;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qrySubmissions>
	</cffunction>

	<cffunction name="getSubmissionDetailsById" access="public" output="false" returntype="query">
		<cfargument name="submissionID" type="numeric" required="true" />
		<cfargument name="orgID" type="numeric" required="true" />

		<cfset var local = structNew() />	

		<cfquery name="local.qryGetSubmissionDetailsById" datasource="#application.dsn.customapps.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID INT = <cfqueryparam value="#arguments.orgId#" cfsqltype="cf_sql_integer">;

			SELECT distinct s.submissionId, s.submissionTypeId, st.submissionType, s.submissionStatusId, ss.submissionStatus,
				s.achievementDescription, s.achievementDate, s.appointedBy, s.role, s.termsOfService, s.appointedDate, s.appointmentType,
				s.awardName, s.awardOrganization, s.awardOrgDetails, s.awardedDate, s.othersHonoured, s.previousPosition, s.previousFirm,
				s.newPosition, s.newFirm, s.movedDate, s.pressReleaseFileName, s.pressReleaseFileDocumentId, s.memberID,
				m2.memberID as originalMemberID, s.isMerged, s.usePhotoForProfile, s.submissionPhotoName, s.submittedByName,
				s.submittedByEmail, s.submittedByPhone, s.submittedByTitle, s.createdDate, s.modifiedDate, s.isStatusUpdateEmailSent,
				(select firstname + ' ' + lastName from membercentral.dbo.ams_members WHERE memberid = s.lastModifiedBy) as lastModifiedBy,
				(select CASE WHEN mg.autoId > 0 THEN 'Yes' ELSE 'No' END
					from membercentral.dbo.cache_members_groups as mg 
					INNER JOIN membercentral.dbo.ams_members as m on m.orgID = @orgID
						and m.memberid = mg.memberid
						and m.memberid = m.activememberID 
						and m.status = 'A'
						AND m.memberid = s.memberid
					INNER JOIN membercentral.dbo.ams_groups as g on g.orgID = @orgID
						and g.groupID = mg.groupID 
						and g.uid= 'C61146D6-85CC-4F4D-86A1-E2E95103F7A4'
					where mg.orgID = @orgID) as IsMember
				, m.firstName + ' ' + m.lastname + ' (' + m.memberNumber +')' as memberName
				, m.firstName + ' ' + m.lastname as memberFullName
				, m.firstName
				, m.lastName
				, m.memberNumber
				, ISNULL(pl.licenseNumber,'') as bpr
				, case when st.submissionType = 'Achievement' then s.achievementDate
					when st.submissionType = 'Appointment' then s.appointedDate
					when st.submissionType = 'Award/Recognition' then s.awardedDate
					when  st.submissionType = 'Career Move' then s.movedDate
					end as eventDate			
			FROM 
				TNBAR_submissions s
				INNER JOIN membercentral.dbo.ams_members m on m.memberid = 
				CASE WHEN ISNULL(s.isMerged,0) = 0 THEN s.memberID ELSE s.originalMemberID  END and m.orgid = @orgID
				INNER JOIN membercentral.dbo.ams_members m2 on m2.memberid = m.activememberID
				INNER JOIN TNBAR_submissionStatuses ss on ss.submissionStatusID = s.submissionStatusID
				INNER JOIN TNBAR_submissionTypes st on st.submissionTypeID = s.submissionTypeID
				LEFT JOIN (select pl.memberid,pl.LicenseNumber from membercentral.dbo.ams_memberProfessionalLicenses pl 	
				INNER JOIN membercentral.dbo.ams_memberProfessionalLicenseTypes plt on plt.PLTypeID = pl.PLTypeID	and plt.orgID = '#arguments.orgId#'
				INNER JOIN membercentral.dbo.ams_memberProfessionalLicenseStatuses pls on pls.PLStatusID = pl.PLStatusID
				and pls.orgID = @orgID and plt.PLName = 'Tennessee') as pl on m.memberid = pl.memberid
				WHERE 1 = 1
				<cfif val(arguments.submissionId) GT 0>
					AND s.submissionId = <cfqueryparam value="#arguments.submissionId#" cfsqltype="cf_sql_integer"/>
				</cfif>
				
				ORDER BY s.createdDate DESC;
		</cfquery>

		<cfreturn local.qryGetSubmissionDetailsById>
	</cffunction>

	<cffunction name="saveSubmission" access="public" output="false" returntype="void">
		<cfargument name="submissionId" required="true" type="numeric">
		<cfargument name="submissionStatusId" required="true" type="numeric">
		<cfargument name="oldSubmissionStatusId" required="true" type="numeric">
		<cfargument name="achievementDescription" type="string" required="false">
		<cfargument name="achievementDate" type="string" required="false">
		<cfargument name="appointedBy" type="string" required="false">
		<cfargument name="role" type="string" required="false">
		<cfargument name="termsOfService" type="string" required="false">
		<cfargument name="appointedDate" type="string" required="false">
		<cfargument name="appointmentType" type="string" required="false">
		<cfargument name="awardName" type="string" required="false">
		<cfargument name="awardOrganization" type="string" required="false">
		<cfargument name="awardOrgDetails" type="string" required="false">
		<cfargument name="awardedDate" type="string" required="false">
		<cfargument name="othersHonoured" required="false" type="boolean" default="0">
		<cfargument name="previousPosition" type="string" required="false">
		<cfargument name="previousFirm" type="string" required="false">
		<cfargument name="newPosition" type="string" required="false">
		<cfargument name="newFirm" type="string" required="false">
		<cfargument name="movedDate" type="string" required="false">
		<cfargument name="usePhotoForProfile" required="false" type="boolean" default="0">
		<cfargument name="modifiedBy" required="true" type="numeric">
		<cfargument name="lastEffectiveDate" required="false" type="string">
		<cfargument name="statusNotes" required="false" type="string">

		<cfset var local = structNew() />	
		<cfquery name="local.qrySaveSubmission" datasource="#application.dsn.customapps.dsn#">
			UPDATE TNBAR_submissions SET
				submissionStatusId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionStatusId#">
				,achievementDescription = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.achievementDescription#">
				,achievementDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.achievementDate#">
				,appointedBy = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.appointedBy#">
				,role = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.role#">
				,termsOfService = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.termsOfService#">
				,appointedDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.appointedDate#">
				,appointmentType = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.appointmentType#">
				,awardName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.awardName#">
				,awardOrganization = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.awardOrganization#">
				,awardOrgDetails = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.awardOrgDetails#">
				,awardedDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.awardedDate#">
				,othersHonoured = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.othersHonoured#">
				,previousPosition = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.previousPosition#">
				,previousFirm = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.previousFirm#">
				,newPosition = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.newPosition#">
				,newFirm = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.newFirm#">
				,movedDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.movedDate#">
				,usePhotoForProfile = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.usePhotoForProfile#">
				,modifiedDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">
				,lastModifiedBy = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.modifiedBy#">
				,isStatusUpdateEmailSent = 0
			WHERE submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionId#">
			<cfif arguments.oldSubmissionStatusId NEQ arguments.submissionStatusId>
				INSERT INTO TNBAR_submissions_submissionStatuses
					(submissionID
					,submissionStatusId
					,statusNotes
					,dateStatusEffective
					,enteredBy
					,dateCreated)
				VALUES (
					<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionId#">
					,<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionStatusId#">
					,<cfqueryparam cfsqltype="cf_sql_string" value="#arguments.statusNotes#">
					,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.lastEffectiveDate#">
					,<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.modifiedBy#">
					,<cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">
				)
			</cfif>
		</cfquery>
		
		<cfquery name="local.qrySubmissionDocumentDetails" datasource="#application.dsn.customapps.dsn#">
			SELECT DISTINCT t.submissionId, d.siteID, srr.resourceRightsID, d.siteResourceID, d.documentId, t.submissionStatusId, ss.submissionStatus
			FROM dbo.TNBAR_submissions t 
			INNER JOIN membercentral.dbo.cms_documents d ON  d.documentId = t.pressReleaseFileDocumentId
			INNER JOIN dbo.TNBAR_submissionStatuses ss ON ss.submissionStatusID = t.submissionStatusId
			LEFT JOIN membercentral.dbo.cms_siteResourceRights srr ON srr.resourceID = d.siteResourceID and srr.siteID = d.siteID 
			WHERE t.submissionId = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.submissionId#">
			AND d.documentId IS NOT NULL
		</cfquery>

		<cfset local.objAdminSuccess = CreateObject("component","model.admin.custom.tnbar.tnbar.success")>
		<cfset local.qrySubmissionStatuses = local.objAdminSuccess.getSubmissionStatuses()/>
		
		<cfquery name="local.qryGetSubmissionStatusByStatusID" dbtype="query">
			SELECT submissionStatusID, submissionStatus
			FROM 
				local.qrySubmissionStatuses
			WHERE submissionStatusID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.submissionStatusId#">
		</cfquery>

		<cfif local.qrySubmissionDocumentDetails.recordcount>
			<cfif local.qryGetSubmissionStatusByStatusID.recordcount and local.qryGetSubmissionStatusByStatusID.submissionStatus EQ 'Approved' and len(local.qrySubmissionDocumentDetails.resourceRightsID) EQ 0>				
				<cfset local.objPermissionAdmin = createObject("component","model.admin.permissions.permissionAdmin")/>
				<cfset local.qryFunctions = local.objPermissionAdmin.getFunctionsQuery(siteResourceID=local.qrySubmissionDocumentDetails.siteResourceID, functionName='View')/>
				
				<cfquery name="local.qryPublicGroup" datasource="#application.dsn.membercentral.dsn#">
					SELECT g.groupID FROM dbo.sites AS s
					INNER JOIN dbo.ams_groups AS g ON g.orgID = s.orgID 
					AND g.groupcode = 'Public' 
					AND g.issystemgroup = 1 
					AND s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubmissionDocumentDetails.siteID#">
				</cfquery>
				<cfif local.qryPublicGroup.recordCount>					
					<cfset local.objPermissionAdmin.insertPermission(resourceID=local.qrySubmissionDocumentDetails.siteResourceID, include=1, functionIDList=local.qryFunctions.functionID, roleID=0, groupIDList=local.qryPublicGroup.groupID, inheritedRightsResourceID=0, inheritedRightsFunctionID=0)/>
				</cfif>		
			<cfelse>
				<cfif len(local.qrySubmissionDocumentDetails.resourceRightsID) and local.qryGetSubmissionStatusByStatusID.submissionStatus NEQ 'Approved'>
					<cfset local.objPermissionAdmin = createObject("component","model.admin.permissions.permissionAdmin")/>
					<cfset local.objPermissionAdmin.removePermission(mcproxy_siteID=local.qrySubmissionDocumentDetails.siteID, siteResourceID=local.qrySubmissionDocumentDetails.siteResourceID, resourceRightsID=local.qrySubmissionDocumentDetails.resourceRightsID)/>
				</cfif>
			</cfif>
		</cfif>

	</cffunction>

	<cffunction name="updateSubmissionMember" access="public" output="false" returntype="void">
		<cfargument name="submissionId" required="true" type="numeric">
		<cfargument name="newMemberId" required="true" type="numeric">
		<cfargument name="modifiedBy" required="true" type="numeric">
		<cfset var local = structNew() />	
		<cfquery name="local.qrySaveSubmission" datasource="#application.dsn.customapps.dsn#">
			UPDATE TNBAR_submissions SET
				isMerged = 1
				,originalMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.newMemberId#">
				,modifiedDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">
				,lastModifiedBy = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.modifiedBy#">
			WHERE submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionId#">
		</cfquery>
	</cffunction>

	<cffunction name="getSubmissionMemberDetailsById" access="public" output="false" returntype="query">
		<cfargument name="memberID" required="true" type="numeric">
		<cfset var local = structNew() />			
		<cfquery name="local.qryGetSubmissionMemberDetailsById" datasource="#application.dsn.membercentral.dsn#">
			
			SELECT m.firstName,m.lastName,m.memberNumber,m.memberID, m.hasMemberPhoto,m.hasMemberPhotoThumb
			,pl.licenseNumber
			,pls.StatusName as status
			,pl.ActiveDate
			,m.company
			FROM ams_members m
			LEFT JOIN membercentral.dbo.ams_memberProfessionalLicenses pl on pl.memberid = m.memberid
			LEFT JOIN membercentral.dbo.ams_memberProfessionalLicenseTypes plt on plt.PLTypeID = pl.PLTypeID 
			and plt.orgID = (select orgid from organizations where orgCode = 'TNBAR' )
			LEFT OUTER JOIN membercentral.dbo.ams_memberProfessionalLicenseStatuses pls on pls.PLStatusID = pl.PLStatusID
			and pls.orgID = (select orgid from organizations where orgCode = 'TNBAR' ) and plt.PLName = 'Tennessee'
			WHERE m.memberid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
		</cfquery>
		<cfreturn local.qrygetSubmissionMemberDetailsById />
	</cffunction>

    <cffunction name="getSubmissionTypes" access="public" output="false" returntype="query">
		<cfargument name="displayInFrontEnd" required="false" type="boolean" default="false">
		<cfset var local = structNew() />		
		<cfquery name="local.qryGetSubmissionTypes" datasource="#application.dsn.customapps.dsn#">
			SELECT submissionTypeId
					,submissionType
			FROM 
				TNBAR_submissionTypes st
				<cfif arguments.displayInFrontEnd>
					WHERE st.isDisplayFrontEnd = 1
				</cfif>
			ORDER BY
				st.submissionTypeId	
		</cfquery>
		<cfreturn local.qryGetSubmissionTypes />
	</cffunction>
		
	<cffunction name="getSubmissionStatuses" access="public" output="false" returntype="query">
		<cfset var local = structNew() />		
		<cfquery name="local.qryGetSubmissionStatuses" datasource="#application.dsn.customapps.dsn#">
			SELECT submissionStatusID
					,submissionStatus
			FROM 
				TNBAR_submissionStatuses ss
			ORDER BY
				ss.submissionStatus	
		</cfquery>
		<cfreturn local.qryGetSubmissionStatuses />
	</cffunction>

	<cffunction name="getGroupOfMember" access="public" output="false" returntype="query">
		<cfargument name="memberID" required="true" type="numeric">
		<cfargument name="orgID" required="true" type="numeric">

		<cfset var local = structNew() />		

		<cfquery name="local.qryGetGroupOfMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID INT = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">;

			SELECT distinct g.groupID, isnull(nullif(mgsg.labelOverride,''),g.groupName) as groupName 
			FROM dbo.ams_groups AS g
			INNER JOIN dbo.cache_members_groups AS mg ON mg.orgID = @orgID 
				and g.groupID = mg.groupID
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID
				and m.memberid = mg.memberid 
				and m.memberid = m.activememberID 
				and m.status = 'A' 
			inner join dbo.ams_members m2 on m2.orgID = @orgID
				and m.memberid = m2.activememberID
				AND m2.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			inner join dbo.ams_memberGroupSetGroups mgsg on mgsg.groupID = mg.groupID
			inner join dbo.ams_memberGroupSets mgs on mgs.groupSetID = mgsg.groupSetID
				and mgs.uid = '76DA6157-B168-42A4-A915-3B754545C5CF'
			where g.orgID = @orgID
			AND g.status <> 'D';
		</cfquery>

		<cfreturn local.qryGetGroupOfMember>
	</cffunction>

	<cffunction name="getMergeDataForSubmission" access="public" output="false" returntype="query">
		<cfargument name="newMemberId" required="true" type="numeric">
		<cfargument name="orgID" required="true" type="numeric">
		<cfset var local = structNew() />		
		<cfquery name="local.qryGetMergeDataForSubmission" datasource="#application.dsn.membercentral.dsn#">
			SELECT m.memberid,  m.firstName + ' ' + m.lastname + ' (' + m.memberNumber +')' as newMemberName,
					mm.mergeID, mm.dateMerged,  mPBActive.firstname + ' ' + mPBActive.lastname as mergedByMemberName
			from dbo.ams_merges as mm
			inner join dbo.ams_members as m2 on m2.memberID = mm.newMemberID
			inner join dbo.ams_members as m on m.memberID = m2.activeMemberID
			inner join dbo.ams_members as mPB on mPB.memberID = mm.performedByMemberID
			inner join dbo.ams_members as mPBActive on mPBActive.memberID = mPB.activeMemberID
			where m.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#"> 
			and mm.newMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.newMemberID#">
		</cfquery>
		
		<cfreturn local.qryGetMergeDataForSubmission />
	</cffunction>
	
	<cffunction name="getSubmissionStatusLogs" access="public" output="false" returntype="query">
		<cfargument name="submissionID" required="true" type="numeric">
		<cfset var local = structNew() />		
		<cfquery name="local.qryGetSubmissionStatusLogs" datasource="#application.dsn.customapps.dsn#">
			SELECT sts.submissionsSubmissionStatusId
				,sts.submissionID
				,sts.submissionStatusId
				,sts.statusNotes
				,sts.dateStatusEffective
				,sts.enteredBy
				,sts.dateCreated
				, ss.submissionStatus
				, m.firstName
				, m.lastName
				, m.memberNumber
				, CASE WHEN  ISNULL(sts.enteredBy,0) = 0 THEN s.submittedByName ELSE (m.firstName + ' ' + m.lastName) END as enteredByName
			from dbo.TNBAR_submissions_submissionStatuses as sts
			INNER JOIN dbo.TNBAR_submissionStatuses ss on ss.submissionStatusID = sts.submissionStatusID
			INNER JOIN dbo.TNBAR_submissions s on s.submissionID = sts.submissionID
			LEFT JOIN membercentral.dbo.ams_members as m on m.memberid = sts.enteredBy
			WHERE sts.submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionID#">
			ORDER BY sts.submissionsSubmissionStatusId desc
		</cfquery>
		
		<cfreturn local.qryGetSubmissionStatusLogs />
	</cffunction>
	<cffunction name="getCustomFieldValueForMember" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true" />
		<cfargument name="memberID" type="numeric" required="true" />
		<cfargument name="colmnName" type="string" required="false" default="">
		<cfquery name="local.qryGetCustomFieldValue" datasource="#application.dsn.membercentral.dsn#">
			SELECT mdcv.columnValueString
			FROM ams_memberDataColumns mdc
			INNER JOIN ams_memberdatacolumnvalues mdcv ON mdcv.columnID = mdc.columnID
				AND mdc.columnName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.colmnName#">
			INNER JOIN ams_memberdata amd ON amd.valueID = mdcv.valueID
				AND amd.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">		
        </cfquery>
		<cfreturn local.qryGetCustomFieldValue>
	</cffunction>
	<cffunction name="getSubmissionExportQuery" access="public" output="false" returntype="void">
		<cfargument name="csvFileName" type="string" required="false">
		<cfargument name="firstName" type="string" required="false">
		<cfargument name="lastName" type="string" required="false">
		<cfargument name="company" type="string" required="false">
		<cfargument name="submittedByName" type="string" required="false">
		<cfargument name="submissionTypeId" type="string" required="false">
		<cfargument name="submissionStatusId" type="string" required="false">
		<cfargument name="createdDateFrom" type="string" required="false">
		<cfargument name="createdDateTo" type="string" required="false">
		<cfargument name="eventDateFrom" type="string" required="false">
		<cfargument name="eventDateTo" type="string" required="false">
		<cfargument name="bpr" type="string" required="false">
		<cfargument name="keyword" type="string" required="false">

		<cfset var local = structNew()>
		
		<cfstoredproc datasource="#application.dsn.customapps.dsn#" procedure="dbo.tnbar_exportSubmissionData">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.csvFileName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.firstName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.lastName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.company#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.submittedByName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.submissionTypeId#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.submissionStatusId#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.createdDateFrom#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.createdDateTo#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.eventDateFrom#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.eventDateTo#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.bpr#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.keyword#">
		</cfstoredproc>
	</cffunction>
</cfcomponent>