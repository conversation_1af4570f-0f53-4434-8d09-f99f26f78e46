<cfsavecontent variable="local.settingsJS">
	<cfoutput>
		<script type="text/javascript">
		function toggleCustomTextArea() {
			var isEnabled = $('##customTextEnabled').is(':checked');
			if (isEnabled) {
				$('##customTextGroup').show();
			} else {
				$('##customTextGroup').hide();
				$('##customTextContent').val('');
			}
		}
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.settingsJS#">

<cfoutput>
<form name="frmSWBProgramSettings" id="frmSWBProgramSettings" class="mb-3">
<input type="hidden" name="bundleID" id="bundleID" value="#local.qryBundle.bundleID#">

<div id="err_swbsettings" class="alert alert-danger mb-2 d-none"></div>

<div class="card card-box mb-1">
	<div class="card-header bg-light">
		<div class="card-header--title">
		<b>At Time of Registration</b>
		</div>
	</div>
	<div class="card-body">
		<div class="form-group row">
			<div class="col-sm-12">
				<div class="custom-control custom-switch">
					<input type="checkbox" name="customTextEnabled" id="customTextEnabled" value="1" class="custom-control-input"<cfif local.qryBundle.customTextEnabled is 1> checked="checked"</cfif> onchange="toggleCustomTextArea();">
					<label class="custom-control-label" for="customTextEnabled">Include custom text in confirmation emails <i class="fa-solid fa-info-circle pl-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="left" data-trigger="hover" title="" data-original-title="When enabled, custom text will appear in the confirmation email above the signature line."></i></label>
				</div>
			</div>
		</div>
		
		<div class="form-group row mt-3" id="customTextGroup"<cfif local.qryBundle.customTextEnabled is not 1> style="display:none;"</cfif>>
			<div class="col-sm-12">
				<div class="form-label-group">
					<textarea name="customTextContent" id="customTextContent" class="form-control" rows="4" maxlength="1000">#encodeForHTML(local.qryBundle.customTextContent)#</textarea>
					<label for="customTextContent">Custom Text (no HTML, 1000 characters max)</label>
				</div>
			</div>
		</div>
	</div>
</div>

</form>

<div id="divSWProgramSettingsSaveLoading" style="display:none;">
	<div class="text-center">
		<br/>
		<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
		<br/><br/>
		<b>Please wait while we save these settings.</b>
		<br/>
	</div>
</div>
</cfoutput>
