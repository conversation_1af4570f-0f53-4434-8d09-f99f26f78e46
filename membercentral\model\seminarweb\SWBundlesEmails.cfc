<cfcomponent extends="SWBundles">
	<cffunction name="sendConfirmation" access="public" returntype="boolean" output="no">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		<cfargument name="signUpOrgCode" type="string" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		<cfargument name="emailOverride" type="string" required="no" default="">

		<cfset var local = structnew()>
		
		<!--- construct email messages --->
		<cfset local.strEmailContent = generateConfirmationEmail(arguments.bundleID,arguments.depoMemberDataID,arguments.signUpOrgCode)>
		
		<!--- handle email override --->
		<cfset local.arrEmailTo = []>
		<cfif len(arguments.emailOverride)>
			<cfset local.emailOverride = replace(replace(arguments.emailOverride,',',';','ALL'),' ','','ALL')>
			<cfset local.emailOverrideArr = listToArray(local.emailOverride,';')>
			
			<cfloop array="#local.emailOverrideArr#" item="local.thisEmail">
				<cfif isValid("regex",local.thisEmail,application.regEx.email)>
					<cfset local.arrEmailTo.append({ name=local.strEmailContent.emailToName, email=local.thisEmail })>
				</cfif>
			</cfloop>
		<cfelseif len(local.strEmailContent.emailTo) and isValid("regex",local.strEmailContent.emailTo,application.regEx.email)>
			<cfset local.arrEmailTo.append({ name=local.strEmailContent.emailToName, email=local.strEmailContent.emailTo })>
		</cfif>

		<cfif len(local.strEmailContent.overrideEmail)>
			<cfset local.arrEmailTo.append({ name=local.strEmailContent.emailToName, email=local.strEmailContent.overrideEmail })>
		</cfif>

		<!--- add to email queue--->
		<cftry>
			<cfif arrayLen(local.arrEmailTo) gt 0>
				<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.signUpOrgCode)>
				<cfset local.seminarWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SemWebCatalog',siteID=local.mc_siteInfo.siteID)>
				<cfset local.emailhtmlcontent = application.objEmailWrapper.wrapMessage(emailTitle=local.strEmailContent.emailTitle, emailContent=local.strEmailContent.html, emailFooter=local.strEmailContent.emailFooter , sitecode=application.objSiteInfo.getSiteCodeFromSiteID(local.mc_siteInfo.siteID))>		
				<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.strEmailContent.emailFromName, email="<EMAIL>" },
						emailto=local.arrEmailTo,
						emailreplyto="<EMAIL>",
						emailsubject=local.strEmailContent.subject,
						emailtitle=local.strEmailContent.emailTitle,
						emailhtmlcontent=local.emailhtmlcontent,
						siteID=local.mc_siteInfo.siteID,
						memberID=local.strEmailContent.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBCONF"),
						sendingSiteResourceID=local.seminarWebSiteResourceID,
						doWrapEmail=false
				)>
				<cfif NOT local.strResult.success>
					<cfthrow message="#local.strResult.err#">
				<cfelse>
					<cfloop array="#local.arrEmailTo#" index="local.thisEmail">
						<cfset logAction(arguments.bundleID,arguments.outgoingType,arguments.performedBy,local.thisEmail.email)>
					</cfloop>
					<cfset local.emailSent = true>
				</cfif>
			<cfelse>
				<cfset local.emailSent = false>
			</cfif>
		<cfcatch type="Any">
			<cfset local.emailSent = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.emailSent>
	</cffunction>

	<cffunction name="generateConfirmationEmail" access="private" returntype="struct" output="no">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		<cfargument name="signUpOrgCode" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.strBundle = getBundleForCatalog(bundleID=arguments.bundleID, catalogOrgCode=arguments.signUpOrgCode, billingState='', billingZip='', MCMemberID=0)>
		<cfset local.qryMember = CreateObject("component","SWCommon").getMemberInfoForOrg(arguments.depoMemberDataID,arguments.signUpOrgCode)>
		<cfset local.qryAssociation = CreateObject("component","SWParticipants").getAssociationDetails(arguments.signUpOrgCode).qryAssociation>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.signUpOrgCode)>
		
		<cfif local.qryAssociation.handlesOwnPayment is 1>
			<cfquery name="local.qryAssocHandlesEnrollPayments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;

				DECLARE @bundleID int, @depoMemberDataID int, @bundleOrderID int, @memberID int, @sitecode varchar(10);
				SET @bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">;
				SET @depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">;
				SET @sitecode = <cfqueryparam value="#arguments.signUpOrgCode#" cfsqltype="CF_SQL_VARCHAR">;

				EXEC membercentral.dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode=@sitecode, @depomemberdataid=@depoMemberDataID, @memberID=@memberID OUTPUT;

				SELECT @bundleOrderID = dbo.fn_getBundleOrderIDForBundle(@bundleID, @memberID);

				SELECT e.enrollmentID, e.bundleOrderID
				FROM dbo.tblEnrollments AS e
				INNER JOIN dbo.tblUsers AS u ON u.userID = e.userID
					AND u.depoMemberDataID = @depoMemberDataID
				WHERE e.bundleOrderID = @bundleOrderID
				AND e.isActive = 1
				AND e.handlesOwnPayment = 1;
			</cfquery>
		</cfif>

		<cfif local.qryAssociation.handlesOwnPayment is 1 and local.qryAssocHandlesEnrollPayments.recordCount>
			<cfset local.assocHandlesPayments = true>

			<!--- transactions for confirmation --->
			<cfstoredproc procedure="sw_enrollmentTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryAssocHandlesEnrollPayments.bundleOrderID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="SWB">
				<cfprocresult name="local.qryTotals" resultset="1">
				<cfprocresult name="local.qryEnrollmentTransactions" resultset="2">
				<cfprocresult name="local.qryPaymentAllocations" resultset="3">
			</cfstoredproc>
			<cfif val(local.qryTotals.total) gt 0>
				<cfstoredproc procedure="sw_getEnrollmentPaymentDetail" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryAssocHandlesEnrollPayments.bundleOrderID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="SWB">
					<cfprocresult name="local.qryEnrollmentPaymentDetail" resultset="1">
				</cfstoredproc>
			</cfif>
		<cfelse>
			<cfset local.assocHandlesPayments = false>

			<cfset local.SWChargeDetail = application.mcCacheManager.sessionGetValue(keyname='SWChargeDetail', defaultValue="")>

			<!--- get transaction data --->
			<cfquery name="local.qryGetTransactionData" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select sum(dt.amountBilled) as total, sum(dt.salesTaxAmount) as tax, max(dt.datePurchased) as datePurchased
				from dbo.tblBundleOrders as bo
				inner join trialsmith.dbo.depoTransactionsApplications as dta on dta.itemID = bo.orderID
					and dta.itemType = 'SWBO'
				inner join trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
				where bo.bundleID = <cfqueryparam value="#arguments.bundleID#" cfsqltype="CF_SQL_INTEGER">
				and dt.depomemberdataID = <cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
				and bo.isActive = 1
			</cfquery>
		</cfif>

		<!--- get username of registrant --->
		<cfquery name="local.getUN" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @memberID int, @siteID int, @sitecode varchar(10), @bundleID int, @depoMemberDataID int, @bundleOrderID int,
				@overrideEmail varchar(200), @userName varchar(75), @isDefault bit;

			set @sitecode = <cfqueryparam value="#arguments.signUpOrgCode#" cfsqltype="CF_SQL_VARCHAR">;
			set @bundleID = <cfqueryparam value="#arguments.bundleID#" cfsqltype="CF_SQL_INTEGER">;
			set @depoMemberDataID = <cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">;
			set @siteID = dbo.fn_getSiteIDFromSiteCode(@sitecode);

			EXEC dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode=@sitecode, @depomemberdataid=@depoMemberDataID, @memberID=@memberID OUTPUT;

			select @bundleOrderID = seminarWeb.dbo.fn_getBundleOrderIDForBundle(@bundleID, @memberID);
			
			select top 1 @overrideEmail = eo.email
			from seminarWeb.dbo.tblEnrollments as e
			inner join seminarWeb.dbo.tblUsers as u on u.userID = e.userID 
				and u.depoMemberDataID = @depoMemberDataID
			inner join dbo.ams_emailAppOverrides AS eo ON eo.itemID = e.enrollmentID AND eo.itemType = 'semwebreg'
			where e.bundleOrderID = @bundleOrderID
			and e.isActive = 1;

			EXEC dbo.ams_up_getUsername @siteID=@siteID, @memberID=@memberID, @userName=@userName OUTPUT, @isDefault=@isDefault OUTPUT;

			select @userName as username, @memberID as memberID, isnull(@overrideEmail,'') as overrideEmail;
		</cfquery>
		
		<!--- set subject/to --->
		<cfset local.strEmailContent.subject = "Access Your Bundle: #local.strBundle.qryBundle.bundleName#">
		<cfset local.strEmailContent.emailToName = "#local.qryMember.firstname# #local.qryMember.lastname#">
		<cfset local.strEmailContent.emailTo = local.qryMember.email>
		<cfset local.strEmailContent.emailFromName = local.qryAssociation.emailFrom>
		<cfset local.strEmailContent.memberID = local.getUN.memberID>
		<cfset local.strEmailContent.overrideEmail = local.getUN.overrideEmail>

		<cfif len(local.mc_siteInfo.alternateForgotPasswordLink)>
			<cfset local.strEmailContent.alternateForgotPasswordLink = local.mc_siteInfo.alternateForgotPasswordLink>
		<cfelse>
			<cfset local.strEmailContent.alternateForgotPasswordLink = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?pg=login&logact=requestReset">
		</cfif>

		<cfset local.strEmailContent.LinkToProgram = "#local.qryAssociation.CatalogURL#/?pg=semwebCatalog&panel=My">
		
		<cfset local.CreditCompleteByDate = CreateObject("component","SWBundles").getCreditCompleteByDateForBundle(depoMemberDataID=arguments.depoMemberDataID, bundleID=arguments.bundleID, memberID=local.getUN.memberid)>
		
		<cfsavecontent variable="local.strEmailContent.emailTitle">
			<cfoutput>
				<div style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:5px;text-align:center;"><i>#local.qryAssociation.description#</i></div>
				<div style="text-align:center;">Access Your Bundle</div>
			</cfoutput>
		</cfsavecontent>

		<cfset local.memberNumber = application.objMember.getMemberNumberByMemberID(memberID=local.getUN.memberID,orgID=local.mc_siteInfo.orgID)>
		<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=local.mc_siteInfo.orgcode, membernumber=local.memberNumber)>
		<cfset local.myProgramsLink = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#?pg=semwebcatalog&panel=My">
			
		<cfif local.strBundle.qryBundle.isSWOD>		
			<cfset local.otherProgramsLink = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#?pg=semwebcatalog&panel=browse&_swft=SWOD">	
			<cfsavecontent variable="local.strProgramDetailsHTML">
				<cfoutput>
				<table cellspacing="0" cellpadding="0" width="100%" style="background-color:##fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;border:1px black solid;padding:10px">
					<tr>
						<td>
							<div align="center">
								<a href="#local.myProgramsLink#&_swft=SWOD&mk=#local.memberKey#" target="_blank" style="-webkit-border-radius: 5px; -moz-border-radius: 5px; border-radius: 5px; color: ##ffff; display: block;width: 180px; background: ##2c78e4;font-size: 16px;font-weight: bold;font-family: sans-serif;text-decoration: none;line-height: 40px;">
									Access Bundle!
								</a>
								<br>
								<span style="font-size:12px;">Your programs are self-paced, accessible 24/7, and compatible with Chrome, Firefox or Safari for best results.</span>
								<br><br>
								<span style="font-size: 12px;"><a href="#local.otherProgramsLink#&mk=#local.memberKey#">+ Register for Other Programs</a> </span>
							</div>
						</td>
					</tr>
				</table>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.objSWL = CreateObject("component","SWLiveSeminars")>
			<cfset local.otherProgramsLink = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#?pg=semwebcatalog&panel=browse&_swft=SWL">
			<cfquery name="local.qryEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;

				DECLARE @bundleID int, @depoMemberDataID int, @bundleOrderID int, @memberID int, @sitecode varchar(10);
				SET @bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">;
				SET @depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">;
				SET @sitecode = <cfqueryparam value="#arguments.signUpOrgCode#" cfsqltype="CF_SQL_VARCHAR">;

				EXEC membercentral.dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode=@sitecode, @depomemberdataid=@depoMemberDataID, @memberID=@memberID OUTPUT;

				SELECT @bundleOrderID = dbo.fn_getBundleOrderIDForBundle(@bundleID, @memberID);

				SELECT DISTINCT e.enrollmentID, e.seminarID
				FROM dbo.tblEnrollments e
				INNER JOIN dbo.tblUsers AS u ON u.userID = e.userID
					AND u.depoMemberDataID = @depoMemberDataID
				WHERE e.bundleOrderID = @bundleOrderID
				AND e.isActive = 1;
			</cfquery>

			<cfsavecontent variable="local.strProgramDetailsHTML">
				<cfoutput>
				<cfloop query="local.qryEnrollments">
					<cfset local.qryEnrollment = local.objSWL.getEnrollmentByEnrollmentID(enrollmentID=local.qryEnrollments.enrollmentID)>
					<cfset local.qrySeminar = local.objSWL.getSeminarBySeminarID(seminarID=local.qryEnrollments.seminarID)>

					<cfset local.parsedTime = local.objSWL.parseTimesFromWDDX(local.qryEnrollment.wddxTimeZones,local.qryEnrollment.OrgwddxTimeZones,local.qrySeminar.dateStart,local.qrySeminar.dateEnd)>
					<cfset local.StartDate = local.parsedTime.StartDate>
					<cfset local.EndDate = local.parsedTime.EndDate>
					<cfset local.TZ = local.parsedTime.TimeZone>
					<cfset local.TZstr = local.parsedTime.TimeZoneCompare>

					<cfset local.icalURL = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?pg=semwebCatalog&panel=downloadICal&seminarid=#local.qryEnrollment.seminarID#&accesscode=#local.qryEnrollment.SWLCode#&mode=stream">
					<cfset local.gcalURL = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?pg=semwebCatalog&panel=downloadGCal&seminarid=#local.qryEnrollment.seminarID#&accesscode=#local.qryEnrollment.SWLCode#&mode=stream">
					
					<div style="border:1px solid ##B2B38F;padding:10px;margin-bottom:10px;">
						<b>#encodeForHTML(local.qryEnrollment.seminarName)#</b><br/>
						<cfif len(local.qryEnrollment.seminarSubTitle)>
							<b>#encodeForHTML(local.qryEnrollment.seminarSubTitle)#</b><br/>
						</cfif>
						<b>#DateFormat(local.StartDate,"dddd, mmmm d, yyyy")#</b><br/>
						<b>#replace(TimeFormat(local.startDate,"h:mm TT"),":00","")#-#replace(TimeFormat(local.enddate,"h:mm TT"),":00","")# #ucase(local.tz)#</b> #local.TZstr#
						<br/><br/><br/>
						<span>Add Webinar to Your Calendar:</span> &nbsp; 
						<nobr><a title="Download webinar to your calendar" href="#local.icalURL#">Add via Outlook</a></nobr> &nbsp;
						<nobr><a title="Add webinar to your Google calendar" href="#local.gcalURL#">Add via Google</a></nobr> &nbsp;
						<nobr><a title="Download webinar to your calendar" href="#local.icalURL#">Add via iCal</a></nobr>
						<br/><br/><br/>
						<cfif local.qrySeminar.isOpen is 1 and local.qrySeminar.includeConnectionInstruction is 1>
							<b style="font-size:1.2em;">HOW TO JOIN THE WEBINAR:</b><br/>
							10 minutes prior to the start of the program, <a href="#local.qryEnrollment.CatalogURL#/?k=#local.qryEnrollment.SWLCode#" target="_blank">click this link to connect to the program</a>
						</cfif>
					</div>
				</cfloop>
				<br/>
				<div style="text-align:center;border: 1px solid ##B2B38F;">
					<p style="text-align:center;font-size:16px;font-weight:bold;font-family:sans-serif;line-height:10px;">Review All Webinars</p>
					<p style="text-align:center;">
						<a href="#local.myProgramsLink#&mk=#local.memberKey#" target="_blank">Click here</a> to review your purchase history, access materials, and download certificates.
					</p>
					<p style="text-align:center;">
						<a href="#local.otherProgramsLink#&mk=#local.memberKey#" target="_blank">+ Register for Other Programs</a>
					</p>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<!--- html / email version --->
		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			#local.qryMember.firstname# #local.qryMember.lastname#:<br/><br/>								
			You're registered for <b>#encodeForHTML(local.strBundle.qryBundle.bundleName)#.</b><br/>
			<p><i>Completion deadlines may vary by program.</i></p>
			<cfif not local.assocHandlesPayments and local.qryGetTransactionData.recordCount>
				<cfif val(local.qryGetTransactionData.total)>
					<br/>Amount: <b>#dollarFormat(local.qryGetTransactionData.total)# <cfif local.qryGetTransactionData.tax gt 0>+ #dollarformat(local.qryGetTransactionData.tax)# tax</cfif></b><br/>
				<cfelseif len(local.strBundle.qryBundle.freeRateDisplay)>
					<br/>Amount: <b>#local.strBundle.qryBundle.freeRateDisplay#</b><br/>
				</cfif>
				<cfif len(local.SWChargeDetail)>
					Paid by <b>#local.SWChargeDetail# <span style="padding-left:2em;">#DateFormat(local.qryGetTransactionData.datePurchased,"m/d/yyyy")#</span></b><br/>
					<div style="margin-top:3px;">PLEASE KEEP A COPY OF THIS RECEIPT FOR YOUR RECORDS</div>
				</cfif>
			<cfelseif local.assocHandlesPayments>
				<cfif val(local.qryTotals.total) gt 0>
					<br/>Amount: <b>#dollarFormat(local.qryTotals.subTotal)# <cfif local.qryTotals.tax gt 0>+ #dollarformat(local.qryTotals.tax)# tax</cfif></b><br/>
				<cfelseif len(local.strBundle.qryBundle.freeRateDisplay)>
					<br/>Amount: <b>#local.strBundle.qryBundle.freeRateDisplay#</b><br/>
				</cfif>
				<cfif val(local.qryTotals.total) gt 0 and local.qryEnrollmentPaymentDetail.recordcount>
					Payment Details:<br/>
					<cfloop query="local.qryEnrollmentPaymentDetail">
						<div style="margin-left:20px;margin-bottom:5px;">
							<b>#dollarformat(local.qryEnrollmentPaymentDetail.allocSum)#<cfif val(local.qryEnrollmentPaymentDetail.processingFees)> (+ #dollarFormat(local.qryEnrollmentPaymentDetail.processingFees)# #local.qryEnrollmentPaymentDetail.processingFeeLabel#)</cfif></b> paid by:<br/>
							#local.qryEnrollmentPaymentDetail.firstname# #local.qryEnrollmentPaymentDetail.lastname#<cfif len(local.qryEnrollmentPaymentDetail.company)> - #local.qryEnrollmentPaymentDetail.company#</cfif><br/>
							#dollarFormat(local.qryEnrollmentPaymentDetail.payAmount)# #local.qryEnrollmentPaymentDetail.detail# made on #DateFormat(local.qryEnrollmentPaymentDetail.transactionDate,"m/d/yyyy")#
							<cfif local.qryEnrollmentPaymentDetail.statusID is 3><span class="red">(Pending Payment)</span></cfif>
						</div>
					</cfloop>
				</cfif>
			</cfif>
			<br/>

			#local.strProgramDetailsHTML#
			<br/><br/>

			<!--- Include custom text if enabled --->
			<cfif local.strBundle.qryBundle.customTextEnabled AND len(trim(local.strBundle.qryBundle.customTextContent))>
				#local.strBundle.qryBundle.customTextContent#<br/><br/>
			</cfif>

			#local.qryAssociation.emailFrom#<br/>
			<a href="mailto:#local.qryAssociation.supportEmail#">#local.qryAssociation.supportEmail#</a><br/>
			#local.qryAssociation.supportPhone#
			</cfoutput>
		</cfsavecontent>
		<cfset local.strEmailContent.html = replace(replace(replace(local.strEmailContent.html,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL")>

		<!--- receipt version --->
		<cfsavecontent variable="local.strEmailContent.receipt">
			<cfoutput>
			<div>
				#local.qryMember.firstname# #local.qryMember.lastname#:<br/><br/>
				You're registered for <br/><br/>
				<b>#encodeForHTML(local.strBundle.qryBundle.bundleName)#.</b><br/>
				<cfif len(local.CreditCompleteByDate) AND dateCompare(now(),local.CreditCompleteByDate, "n") LT 0>
					<p><i>Completion deadlines may vary by program.</i></p>
				</cfif>
				<cfif not local.assocHandlesPayments and local.qryGetTransactionData.recordCount>
					<cfif val(local.qryGetTransactionData.total)>
						Amount: <b>#dollarFormat(local.qryGetTransactionData.total)# <cfif local.qryGetTransactionData.tax gt 0>+ #dollarformat(local.qryGetTransactionData.tax)# tax</cfif></b><br/>
					<cfelseif len(local.strBundle.qryBundle.freeRateDisplay)>
						Amount: <b>#local.strBundle.qryBundle.freeRateDisplay#</b><br/>
					</cfif>
					<cfif len(local.SWChargeDetail)>
						Paid by <b>#local.SWChargeDetail# <span style="padding-left:2em;">#DateFormat(local.qryGetTransactionData.datePurchased,"m/d/yyyy")#</span></b><br/>
						<div style="margin-top:3px;">PLEASE KEEP A COPY OF THIS RECEIPT FOR YOUR RECORDS</div>
					</cfif>
				<cfelseif local.assocHandlesPayments>
					<cfif val(local.qryTotals.total) gt 0>
						Amount: <b>#dollarFormat(local.qryTotals.subTotal)# <cfif local.qryTotals.tax gt 0>+ #dollarformat(local.qryTotals.tax)# tax</cfif></b><br/>
					<cfelseif len(local.strBundle.qryBundle.freeRateDisplay)>
						Amount: <b>#local.strBundle.qryBundle.freeRateDisplay#</b><br/>
					</cfif>
					<cfif val(local.qryTotals.total) gt 0 and local.qryEnrollmentPaymentDetail.recordcount>
						Payment Details:<br/>
						<cfloop query="local.qryEnrollmentPaymentDetail">
							<div style="margin-left:20px;margin-bottom:5px;">
								<b>#dollarformat(local.qryEnrollmentPaymentDetail.allocSum)#<cfif val(local.qryEnrollmentPaymentDetail.processingFees)> (+ #dollarFormat(local.qryEnrollmentPaymentDetail.processingFees)# #local.qryEnrollmentPaymentDetail.processingFeeLabel#)</cfif></b> paid by:<br/>
								#local.qryEnrollmentPaymentDetail.firstname# #local.qryEnrollmentPaymentDetail.lastname#<cfif len(local.qryEnrollmentPaymentDetail.company)> - #local.qryEnrollmentPaymentDetail.company#</cfif><br/>
								#dollarFormat(local.qryEnrollmentPaymentDetail.payAmount)# #local.qryEnrollmentPaymentDetail.detail# made on #DateFormat(local.qryEnrollmentPaymentDetail.transactionDate,"m/d/yyyy")#
								<cfif local.qryEnrollmentPaymentDetail.statusID is 3><span class="red">(Pending Payment)</span></cfif>
							</div>
						</cfloop>
					</cfif>
				</cfif>
				<br/>

				#local.strProgramDetailsHTML#

				<br/><br/>
				<cfif NOT local.strBundle.qryBundle.isSWOD>	
					Need Help? Call #local.qryAssociation.supportPhone# for assistance.<br/><br/>
				</cfif>

				<!--- Include custom text if enabled --->
				<cfif local.strBundle.qryBundle.customTextEnabled AND len(trim(local.strBundle.qryBundle.customTextContent))>
					#local.strBundle.qryBundle.customTextContent#<br/><br/>
				</cfif>

				#local.qryAssociation.emailFrom#<br/>
				<a href="mailto:#local.qryAssociation.supportEmail#">#local.qryAssociation.supportEmail#</a><br/>
				#local.qryAssociation.supportPhone#
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfset local.strEmailContent.receipt = replace(replace(replace(local.strEmailContent.receipt,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL")>

		<!--- payment receipt --->
		<cfif local.assocHandlesPayments and val(local.qryTotals.total) gt 0>
			<cfsavecontent variable="local.strEmailContent.paymentReceipt">
				<cfoutput>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Registration</td>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;text-align:right;">Total</td>
				</tr>

				<tr>
					<td colspan="2" class="tsAppBodyText" style="padding:6px;">
						<div style="margin:10px 0 5px 0;padding-bottom:5px;" class="tsAppBB20">
							<b>#htmleditformat(local.strBundle.qryBundle.bundleName)#</b><br/>
							<cfif len(local.strBundle.qryBundle.bundleSubTitle)>
								<b>#htmleditformat(local.strBundle.qryBundle.bundleSubTitle)#</b><br/>
							</cfif>
							Registrant: #local.qryMember.firstname# #local.qryMember.lastname#:<br/>
							<cfif len(local.CreditCompleteByDate) AND dateCompare(now(),local.CreditCompleteByDate, "n") LT 0>
								<p><i>Completion deadlines may vary by program.</i></p>
							</cfif>
						</div>
					</td>
				</tr>

				<cfquery name="local.qrySWBRate" dbtype="query">
					select detail, amount
					from [local].qryEnrollmentTransactions
					where itemType = 'SWBRate'
				</cfquery>

				<tr>
					<td class="tsAppBodyText" style="padding:6px">
						#local.qrySWBRate.detail#
					</td>
					<td class="tsAppBodyText" align="right" style="padding: 6px" valign="top">
						<cfif local.qrySWBRate.amount gt 0>
							#dollarFormat(local.qrySWBRate.amount)#
						<cfelse>
							#local.strBundle.qryBundle.freeRateDisplay#
						</cfif>
					</td>
				</tr>
				</table>
				<br/><br/>
				<table width="100%" border="0" cellspacing="0" cellpadding="4" style="border:1px solid ##999;border-collapse:collapse;">
				<tr bgcolor="##DEDEDE">
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;"><b>Payment</b></td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText tsAppBB20" style="padding: 6px">
						<cfif local.qryEnrollmentPaymentDetail.recordcount gt 0>
							<cfloop query="local.qryEnrollmentPaymentDetail">
								<div style="margin-bottom:12px;">
									<b>#dollarformat(local.qryEnrollmentPaymentDetail.allocSum)#<cfif val(local.qryEnrollmentPaymentDetail.processingFees)> (+ #dollarFormat(local.qryEnrollmentPaymentDetail.processingFees)# #local.qryEnrollmentPaymentDetail.processingFeeLabel#)</cfif></b> 
									paid by:
									<div style="margin-left:20px;margin-top:6px;">
										#local.qryEnrollmentPaymentDetail.firstname# #local.qryEnrollmentPaymentDetail.lastname#<cfif len(local.qryEnrollmentPaymentDetail.company)> - #local.qryEnrollmentPaymentDetail.company#</cfif><br/>
										#dollarFormat(local.qryEnrollmentPaymentDetail.payAmount)# #local.qryEnrollmentPaymentDetail.detail# made on #DateFormat(local.qryEnrollmentPaymentDetail.transactionDate,"m/d/yyyy")#
										<cfif local.qryEnrollmentPaymentDetail.statusID is 3><span class="red">(Pending Payment)</span></cfif>
									</div>
								</div>
							</cfloop>
						<cfelseif local.qryEnrollmentPaymentDetail.recordcount is 0>
							No payment was made.
						<cfelse>
							No payment was due.
						</cfif>
						<br/>
					</td>
				</tr>
				</table>
				</cfoutput>
			</cfsavecontent>
			<cfset local.strEmailContent.paymentReceipt = trim(replace(replace(replace(local.strEmailContent.paymentReceipt,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
		</cfif>
		
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=local.qryAssociation.orgIdentityID)>
		
		<cfsavecontent variable="local.strEmailContent.emailFooter">
			<cfoutput>
                This message was sent by SeminarWeb on behalf of <a href='#local.qryOrgIdentity.website#'>#local.qryOrgIdentity.organizationName#</a><br/>
                <cfif len(trim(local.qryOrgIdentity.email))>
                    <a href='mailto:#local.qryOrgIdentity.email#' target="_blank">#local.qryOrgIdentity.email#</a>
                </cfif>
                <cfif len(trim(local.qryOrgIdentity.phone))>
                    | #local.qryOrgIdentity.phone#
                </cfif>
            </cfoutput>
		</cfsavecontent>

		<cfset local.strEmailContent.templateDisp = application.objEmailWrapper.wrapMessage(emailTitle=local.strEmailContent.emailTitle, emailContent=local.strEmailContent.html, emailFooter=local.strEmailContent.emailFooter, sitecode=local.qryAssociation.sitecode)>

		<cfreturn local.strEmailContent>
	</cffunction>
</cfcomponent>