<cfcomponent displayname="Member Account Page routines">

	<cffunction name="memberInit" access="public" returntype="struct" displayname="Init the Member Account Form" output="no">
		<cfargument name="depomemberdataID" type="numeric" required="yes">
		
		<cfset var MemberInit = StructNew()>

		<cfset MemberInit.qMembertype = application.objCommon.getMemberTypes()>
		<cfset MemberInit.qStates = application.objCommon.getStates()>
		<cfset MemberInit.qCountries = application.objCommon.getCountries()>
		<cfset MemberInit.qdepoMemberData = application.objCommon.getMemberData(depoMemberDataID=arguments.depomemberdataid)>
		<cfset MemberInit.qryMatches = getMatchingMembers(arguments.depomemberdataid)>
		<cfset MemberInit.qryMCMemberInfo = application.objCommon.getMemberDataMCInfo(depoMemberDataID=arguments.depomemberdataid)>

		<cfstoredproc datasource="#application.settings.dsn.trialsmith.dsn#" procedure="account_memberInit">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataid#" null="No">
			<cfprocresult name="MemberInit.FirmPlanInfo" resultset="1">
			<cfprocresult name="MemberInit.NoteTypes" resultset="2">
			<cfprocresult name="MemberInit.qCreditCard" resultset="3">
			<cfprocresult name="MemberInit.linkedSites" resultset="4">
			<cfprocresult name="MemberInit.accountsInWaiting" resultset="5">
			<cfprocresult name="MemberInit.orgCodesAll" resultset="6">
			<cfprocresult name="MemberInit.getnotes" resultset="7">
			<cfprocresult name="MemberInit.loginHistory" resultset="8">
		</cfstoredproc>

		<cfquery name="MemberInit.HasPendingTransactions" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select top 1 transactionID
			from dbo.depoTransactions 
			where depomemberdataid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.depomemberdataid#">
			and madeWhilePending = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset MemberInit.qCreditCard_TS = QueryFilter(MemberInit.qCreditCard,function(thisRow) { return thisRow.orgcode eq 'TS'; })>
		<cfset MemberInit.qCreditCard_SW = QueryFilter(MemberInit.qCreditCard,function(thisRow) { return thisRow.orgcode eq 'SW'; })>

		<cfquery name="MemberInit.qryBillingRecords" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select orgCode
			from dbo.membercentralBilling
			where DepoMemberDataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.depomemberdataid#">
			order by orgcode;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="MemberInit.orgCodes" dbtype="query">
			SELECT url, state
			FROM MemberInit.orgCodesAll 
			where isPortalCreated = 'Y' and state <> 'cp'
			order by state, url
		</cfquery>

		<cfquery name="MemberInit.MCSitesAll" datasource="#application.settings.dsn.membercentral.dsn#" cachedwithin="#CreateTimeSpan(1,0,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select s.siteid, s.sitecode, s.sitename, o.orgcode, ns.networkID as loginNetworkID
			from dbo.sites as s
			inner join dbo.networksites as ns on ns.siteID = s.siteID and ns.isLoginNetwork = 1
			inner join dbo.organizations as o on o.orgid = s.orgid
			order by s.siteCode;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="MemberInit.MCSitesAIW" dbtype="query">
			select siteid, sitecode, sitename, orgcode
			from MemberInit.MCSitesAll
			where orgcode = '#MemberInit.accountsInWaiting.orgcode#'
			order by siteCode
		</cfquery>

		<cfset MemberInit.linkedSites.addColumn("username","varchar",[])>
		<cfset MemberInit.linkedSites = MemberInit.linkedSites.map(function(row) {
			var isDefault = 0;
			cfstoredproc (procedure="ams_up_getUsername", datasource=application.settings.dsn.membercentral.dsn) {
				cfprocparam (cfsqltype="cf_sql_integer", value=arguments.row.siteID);
				cfprocparam (cfsqltype="cf_sql_integer", value=arguments.row.memberID);
				cfprocparam (cfsqltype="cf_sql_varchar", type="out", variable="arguments.row.username");
				cfprocparam (cfsqltype="cf_sql_bit", type="out", variable="isDefault");
			} 
			return arguments.row;
		})>

		<cfreturn MemberInit>
	</cffunction>

	<cffunction name="displayMemberForm" returntype="void" access="public" displayname="Display the Member Account form" output="yes">
		<cfargument name="strMemberInit" type="struct" required="yes">

		<cfif val(arguments.strMemberInit.qdepoMemberData.depomemberdataid) is 0>
			<cflocation url="MemberSubscription.cfm?depomemberDataID=0&useraction=join" addtoken="no">
		</cfif>

		<cfinclude template="dsp_MemberEdit_memberForm.cfm">
	</cffunction>

	<cffunction name="displayAllEmails" returntype="void" access="public" displayname="Display the Member Account E-mail addresses" output="yes">
		<cfargument name="depomemberdataid" type="numeric" required="Yes">

		<cfset var qResults = "">
		<cfset var qryAllEmail = "">

		<!--- Get account --->
		<cfset qResults = application.objCommon.getMemberData(arguments.depomemberdataid)>

		<!--- Get confirmed and pending emails --->
		<cfstoredproc datasource="#application.settings.dsn.trialsmith.dsn#" procedure="account_allEmails">
		<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataid#" null="No">
		<cfprocresult name="qryAllEmail" resultset="1">
		</cfstoredproc>

		<cfinclude template="dsp_MemberEdit_displayAllEmails.cfm">
	</cffunction>

	<cffunction name="displayWelcomeEmail" returntype="void" access="public" displayname="Preview and Send welcome email." output="yes">	
		<cfargument name="depomemberdataid" type="numeric" required="Yes">	

		<cfset var qResults = "">		

		<cfset qResults = application.objCommon.getMemberData(arguments.depomemberdataid)>
		<cfset qContentTrialSmithWelcomeEmailContent = application.objCommon.getOptions(contentTitle='Trialsmith_Options_TrialSmithWelcomeEmailContent')>
		<cfset var emailtext = trim("#qResults.firstname# #qResults.lastname#:#chr(10)##chr(10)##qContentTrialSmithWelcomeEmailContent.rawContent#")>
		
		<cfinclude template="dsp_MemberEdit_WelcomeEmail.cfm">	
	</cffunction>

	<cffunction name="sendWelcomeEmail" access="public" displayname="Send the welcome email to the user" returntype="void">	
		<cfargument name="formData" type="struct" required="Yes">	

		<cfset var getMember = application.objCommon.getMemberData(arguments.formdata.depoMemberDataID)>
		<cfset qContentTrialSmithWelcomeEmailContent = application.objCommon.getOptions(contentTitle='Trialsmith_Options_TrialSmithWelcomeEmailContent')>
		<cfset var emailtext = trim("#getMember.firstname# #getMember.lastname#:#chr(10)##chr(10)##qContentTrialSmithWelcomeEmailContent.rawContent#")>

		<cfif IsValid("regex", getMember.email, application.settings.regEx.email)>	
			<!--- Send login information to the user --->	
			<cfmail to="#getMember.email#" from="#arguments.formData.emailfrom#" subject="#arguments.formData.emailsubject#" type="HTML">	
			<html><head></head><body>	
			<font face="arial,helvetica" size="2">	
			#Replace(emailtext,chr(10),"<br/>","ALL")#	
			</font>	
			</body></html>	
			</cfmail>	

			<!--- Insert note --->	
			<cfset insertNote(getMember.depoMemberDataID,1,"Welcome email sent to #trim(getMember.email)# by #session.name#")>	
		</cfif>	
	</cffunction>

	<cffunction name="displayCCForm" returntype="void" access="public" displayname="Display the Member Credit Card form" output="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="Yes">
		<cfargument name="merchantOrgcode" type="string" required="Yes">
		<cfargument name="mode" type="string" required="No" default="update">

		<cfset var local = structNew()>
		<cfset local.isCardReplace = 0>
		<cfset local.qResults = application.objCommon.getMemberData(depoMemberDataID=arguments.depoMemberDataID)>

		<cfset local.objCC = CreateObject("component","models.trialsmith.tsChargeCardCIM")>

		<cfquery name="local.qryGetCard" datasource="#application.settings.dsn.trialsmith.dsn#">
			select top 2 payProfileID, depomemberdataid, customerProfileID, paymentProfileID
			from dbo.ccMemberPaymentProfiles 
			where depomemberdataid = <cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
			and orgcode = <cfqueryparam value="#arguments.merchantorgcode#" cfsqltype="CF_SQL_VARCHAR">
		</cfquery>

		<cfinclude template="dsp_MemberEdit_CCForm.cfm">
	</cffunction>

	<cffunction name="clearCard" access="public" displayname="Remove a credit card from an account." returntype="struct" output="no">
		<cfargument name="depoMemberDataID" type="numeric" required="Yes">
		<cfargument name="merchantOrgcode" type="string" required="Yes">
	
		<cfset var local = structNew()>
		<cfset local.strResult = structNew()>		
		<cfset local.strResult.success = false>
		<cfset local.strResult.msg = ''>

		<cfquery name="local.qryGetCIMInfo" datasource="#application.settings.dsn.trialsmith.dsn#">
			select authUsername, authTransKey
			from dbo.depoTLA 
			where [state] = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1
		</cfquery>
		<cfquery name="local.qrygetCard" datasource="#application.settings.dsn.trialsmith.dsn#">
			select top 1 payProfileID, detail, customerProfileID, paymentProfileID
			from dbo.ccMemberPaymentProfiles 
			where depomemberdataid = <cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
			and orgcode = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
		</cfquery>
		<cfif local.qryGetCIMInfo.recordcount is 1 and local.qrygetcard.recordcount is 1>
			<cfset local.strResponse = createObject("component","models.trialsmith.tsChargeCardCIM").removeCard(CIMUsername=local.qryGetCIMInfo.authUsername, CIMPassword=local.qryGetCIMInfo.authTransKey, customerProfileID=local.qrygetCard.customerProfileID, paymentProfileID=local.qrygetCard.paymentProfileID)>
			<cfif local.strResponse.responseCode is 1 or FindNoCase("[E00040]",local.strResponse.responseReasonText)>
				<cfquery name="local.qryclearCard" datasource="#application.settings.dsn.trialsmith.dsn#">
					EXEC dbo.ts_deleteCardOnFile 
						@payProfileID = <cfqueryparam value="#local.qrygetCard.payProfileID#" cfsqltype="CF_SQL_INTEGER">,
						@merchantOrgCode = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">;
				</cfquery>
				<cfset insertNote(arguments.depoMemberDataID,1,"#arguments.merchantOrgcode# Credit Card #local.qrygetCard.detail# removed by #session.name#")>
				<cfset local.strResult.success = true>
			<cfelse>
				<cfset local.strResult.msg = local.strResponse.responseReasonText>
			</cfif>
		<cfelse>
			<cfset local.strResult.msg = 'Invalid token'>
		</cfif>

		<cfreturn local.strResult>
	</cffunction>
	
	<cffunction name="displayChargeForm" returntype="void" access="public" displayname="Display the Member Credit Card Charge form" output="yes">
		<cfargument name="depomemberdataid" type="numeric" required="Yes">
		<cfargument name="orgcode" type="string" required="Yes">
	
		<cfset var qResults = "">
		<cfset var qCustomers = "">
	
		<!--- Get account --->
		<cfset qResults = application.objCommon.getMemberData(arguments.depomemberdataid)>
	
		<cfquery name="qCustomers" datasource="#application.settings.dsn.trialsmith.dsn#">
			SELECT d.depomemberdataID, d.FirstName AS depofirstname, d.LastName AS depolastname, SUM(t.AmountBilled) + SUM(t.salesTaxAmount) AS totaldue
			FROM dbo.depomemberdata d 
			INNER JOIN dbo.depoTransactions t ON d.depomemberdataid = t.depomemberdataid 
			INNER JOIN dbo.ccMemberPaymentProfiles c ON d.depomemberdataID = c.depoMemberDataID 
				and c.orgCode = <cfqueryparam value="#arguments.orgcode#" cfsqltype="CF_SQL_VARCHAR">
			WHERE d.depomemberdataid = <cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">
			GROUP BY d.depomemberdataid, d.FirstName, d.LastName
		</cfquery>
	
		<cfinclude template="dsp_MemberEdit_chargeForm.cfm">
	</cffunction>

	<cffunction name="runcards" access="public" displayname="Charge a member's credit card." output="no">
		<cfargument name="formData" type="struct" required="Yes">
	
		<cfset var local = StructNew()>
	
		<cfquery name="local.qryGetCIMInfo" datasource="#application.settings.dsn.trialsmith.dsn#">
			select authUsername, authTransKey
			from dbo.depoTLA 
			where [state] = <cfqueryparam value="#arguments.formdata.orgcode#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1
		</cfquery>
		<cfquery name="local.qrygetCard" datasource="#application.settings.dsn.trialsmith.dsn#">
			select payProfileID, detail, customerProfileID, paymentProfileID
			from dbo.ccMemberPaymentProfiles 
			where depomemberdataid = <cfqueryparam value="#arguments.formdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
			and orgcode = <cfqueryparam value="#arguments.formdata.orgcode#" cfsqltype="CF_SQL_VARCHAR">
			and declined = 0
		</cfquery>

		<cfquery name="local.qryTotalDue" datasource="#application.settings.dsn.trialsmith.dsn#">
			SELECT SUM(t.AmountBilled) + SUM(t.salesTaxAmount) AS totaldue, sum(t.salesTaxAmount) as taxDue
			FROM dbo.depomemberdata d 
			INNER JOIN dbo.depoTransactions t ON d.depomemberdataID = t.depomemberdataID 
			WHERE d.depomemberdataID = <cfqueryparam value="#arguments.formdata.depomemberdataID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfset local.chargeAmount = val(rereplace(arguments.formdata.amount,'[^0-9\.]','','ALL'))>
		<cfset local.dcfLink = "displayChargeForm#arguments.formdata.orgcode#">

		<cfif local.qrygetCard.recordcount is 1 and local.qryTotalDue.totaldue gt 0 and local.chargeAmount gt 0 and local.chargeAmount lte local.qryTotalDue.totaldue>
			<cfif arguments.formdata.orgcode eq "TS">
				<cfset local.payDetail = "TrialSmith Account Payment">
			<cfelse>
				<cfset local.payDetail = "SeminarWeb Payment">
			</cfif>

			<cfset local.strResponse = createObject("component","models.trialsmith.tsChargeCardCIM").chargeCard(CIMUsername=local.qryGetCIMInfo.authUsername, 
				CIMPassword=local.qryGetCIMInfo.authTransKey, customerProfileID=local.qrygetCard.customerProfileID, paymentProfileID=local.qrygetCard.paymentProfileID, 
				amount=local.chargeAmount, detail=local.payDetail, payProfileID=local.qrygetCard.payProfileID)>
			<cfset local.rcode = local.strResponse.responseCode>
			<cfset local.rText = local.strResponse.responseReasonText>

			<cfset insertNote(arguments.formdata.depomemberdataID,1,"#arguments.formdata.orgcode# Credit Card charge attempted for #DollarFormat(local.qryTotalDue.totaldue)# by #session.name#")>

			<cfset local.nextpage = "MemberEdit.cfm?depoMemberDataID=#arguments.formdata.depomemberdataID#&memAction=#local.dcfLink#&rcode=#local.rcode#&rtext=#urlencodedformat(local.rText)#">
			<cflocation url="#local.nextpage#" addtoken="no">
		<cfelse>
			<cflocation url="MemberEdit.cfm?depoMemberDataID=#arguments.formdata.depomemberdataID#&memAction=#local.dcfLink#" addtoken="no">
		</cfif>
	</cffunction>
	
	<cffunction name="approveAccount" access="public" displayname="Approves an account." returntype="void">
		<cfargument name="formData" type="struct" required="Yes">

		<cfset var updateTransactions = "">
		<cfset var getdata = application.objCommon.getMemberData(arguments.formdata.depoMemberDataID)>

		<!--- Remove pending flag from subscription related transactions --->
		<cfquery name="updateTransactions" datasource="#application.settings.dsn.trialsmith.dsn#">
			UPDATE depoTransactions
			SET madeWhilePending = 0
			WHERE madeWhilePending = 1 
			AND depoMemberdataid = <cfqueryparam value="#arguments.formdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<!--- Send Notification to Management at Trialsmith --->
		<cfmail To="#application.settings.email_orders#" From="<EMAIL>" Subject="TrialSmith Account Activation" type="HTML">
			<p>The account for member #getdata.FirstName# #getdata.LastName# has been activated. New information supplied for the account is listed below.</p>
			<p>Name: #getdata.FirstName# #getdata.LastName#<br/>
				Source ID: #getdata.sourceID#<br/>
				DepoMemberDataID: #getdata.depoMemberdataid#<br/>
				Email: #getdata.Email#<br/>
				Phone: #getdata.Phone#<br/>
				Fax: #getdata.Fax#</p>
		</cfmail>
		
		<!--- Send notification to user their account has been activated --->
		<!--- Send only if NOT an expert and NOT affiliated with an organization --->
		<cfif (getdata.signuporgcode eq "TS" or getdata.signuporgcode eq "") and IsValid("regex", getdata.Email, application.settings.regEx.email)>
	 		<cfmail To="#getdata.Email#" From="<EMAIL>" Subject="TrialSmith Account Activation" type="HTML">
				<html>
				<body>
				<p>Thank you for signing up with TrialSmith.</p>
				<p>Your new information supplied for the account is listed below.</p>
				<p>Name: #getdata.FirstName# #getdata.LastName#</p>
				<p>Login 24 hours a day at <a href="https://www.trialsmith.com">trialsmith.com</a>.</p>
				</body>
				</html>
			</cfmail>
		</cfif>	
	</cffunction>

	<cffunction name="denyAccount" access="public" hint="Denies an account." returntype="void" output="no">
		<cfargument name="formData" type="struct" required="Yes">

		<cfset var updateTransactions = "">
		<cfset var getTransactions = "">

		<!--- Reverse subscription related transactions with pending flag set --->
		<cfquery name="getTransactions" datasource="#application.settings.dsn.trialsmith.dsn#">
			select transactionID
			from depoTransactions
			where depoMemberdataid = <cfqueryparam value="#arguments.formdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
			and madeWhilePending = 1 
			and accountcode between 1000 and 1999
		</cfquery>
		<cfif getTransactions.recordcount gt 0>
			<cfset CreateObject("component","models.tsadmin.act_transactions").reverseTransactions(transactionIDs=valueList(getTransactions.transactionID), depoMemberDataID=arguments.formdata.depoMemberDataID)>
		</cfif>
		
		<!--- Remove pending flag from transactions --->
		<cfquery name="updateTransactions" datasource="#application.settings.dsn.trialsmith.dsn#">
			UPDATE depoTransactions
			SET madeWhilePending = 0
			WHERE madeWhilePending = 1 
			AND depoMemberdataid = <cfqueryparam value="#arguments.formdata.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
	</cffunction>

	<cffunction name="convertAccountInWaiting" access="public" returntype="void" output="no">
		<cfargument name="depomemberDataID" type="numeric" required="Yes">
		<cfargument name="siteid" type="numeric" required="Yes">

		<cfset var qrySiteInfo = "">
		<cfset var qrycreateNP = "">

		<cfquery name="qrySiteInfo" datasource="#application.settings.dsn.membercentral.dsn#">
			select top 1 sf.sitePasswords as sf_sitePasswords, ns.networkID
			from dbo.sites as s
			inner join dbo.siteFeatures as sf on sf.siteID = s.siteID
			inner join dbo.networkSites ns on ns.siteID = s.siteID and ns.isLoginNetwork = 1
			where s.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfquery name="qrycreateNP" datasource="#application.settings.dsn.membercentral.dsn#">
			set nocount on;

			declare @siteID int, @networkID int, @depomemberdataid int, @MCMemberIDTemp int,
				@username varchar(50), @password varchar(50), @profileID int;
			
			set @depomemberdataid = <cfqueryparam value="#arguments.depomemberdataID#" cfsqltype="CF_SQL_INTEGER">;
			set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
			set @networkID = <cfqueryparam cfsqltype="cf_sql_integer" value="#qrySiteInfo.networkID#">;

			select @MCMemberIDTemp = MCMemberIDTemp
			from trialsmith.dbo.depomemberdata
			where depomemberdataid = @depomemberdataid;

			exec dbo.ams_getDefaultUsername @siteID=@siteID, @defaultUsername=@username OUTPUT;
			exec dbo.ams_getDefaultPassword @defaultPassword=@password OUTPUT;

			update dbo.ams_memberSiteDefaults set status = 'D' where memberID = @MCMemberIDTemp;

			<!--- create network profile linked to depoid --->
			EXEC dbo.ams_createNetworkProfileFromDepoAccount
				@networkID=@networkID,
				@depomemberdataID=@depomemberdataid, 
				@username=@username,
				@password=@password,
				@status='A',
				@profileID=@profileID OUTPUT;
			
			IF @profileID > 0 BEGIN
				<!--- remove account in waiting identifier --->
				update trialsmith.dbo.depomemberdata
				set MCMemberIDTemp = null
				where depomemberdataID = @depomemberdataid;

				<cfif qrySiteInfo.sf_sitePasswords eq 1>
					EXEC dbo.ams_createMemberNetworkProfileWithCredentials @memberID=@MCMemberIDTemp, @profileID=@profileID, 
						@siteID=@siteID, @username=@username, @password=@password, @status='A';
				<cfelse>
					EXEC dbo.ams_createMemberNetworkProfile @memberID=@MCMemberIDTemp, @profileID=@profileID, @siteID=@siteID, @status='A';
				</cfif>
			END
		</cfquery>
	</cffunction>

	<cffunction name="clearAccountInWaiting" access="public" returntype="void" output="no">
		<cfargument name="depomemberDataID" type="numeric" required="Yes">

		<cfset var qryRemove = "">

		<cfquery name="qryRemove" datasource="#application.settings.dsn.trialsmith.dsn#">
			UPDATE dbo.depomemberdata
			SET MCMemberIDTemp = null
			WHERE depomemberdataid = <cfqueryparam value="#arguments.depomemberDataID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
	</cffunction>

	<cffunction name="removeMNP" access="public" returntype="void" output="no" displayname="Removes an org affiliation on new platform.">
		<cfargument name="depomemberDataID" type="numeric" required="Yes">
		<cfargument name="mnpID" type="numeric" required="Yes">

		<cfset var qryRemove = "">

		<cfquery name="qryRemove" datasource="#application.settings.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			
			DECLARE @itemCount int;

			EXEC dbo.ams_deleteMemberNetworkProfile <cfqueryparam value="#arguments.mnpID#" cfsqltype="CF_SQL_INTEGER">;

			EXEC dbo.ams_deleteOrphanedNetworkProfiles @itemCount=@itemCount OUTPUT;
		</cfquery>
	</cffunction>

	<cffunction name="addMCAffiliation" access="public" returntype="void" output="no">
		<cfargument name="depomemberDataID" type="numeric" required="Yes">
		<cfargument name="membernumber" type="string" required="no">
		<cfargument name="siteid" type="numeric" required="Yes">

		<cfstoredproc datasource="#application.settings.dsn.trialsmith.dsn#" procedure="account_addMCAffiliation">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberDataID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.membernumber#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="saveAccount" access="public" returntype="void" output="no" displayname="Saves the account form.">
		<cfargument name="formData" type="struct" required="Yes">
		
		<cfset var checkTS = "">
		<cfset var checkMember = "">
		<cfset var updateMember = "">
		<cfset var reason = "">

		<cfif arguments.formData.depoMemberDataID gt 0>
			<!--- Check ts_lastUpdate to see if the record changed after we loaded the account form --->
			<cfset checkMember = application.objCommon.getMemberData(arguments.formdata.depoMemberDataID)>
			<cfif ToBase64(checkMember.ts_lastUpdate) eq arguments.formData.ts_lastUpdate>
				<cfset arguments.formData.optOutTSMarketing = arguments.formData.optOutTSMarketing ?: 0>

				<!--- only save if something changed (use compare for case sensitive comparisons) --->
				<cfscript>
				if (compare(checkMember.FirstName,trim(arguments.formData.FNAME)))
					reason = ListAppend(reason,"FirstName changed from #checkMember.FirstNAME# to #arguments.formData.FNAME#",chr(10));
				if (compare(checkMember.LastName,trim(arguments.formData.LNAME)))
					reason = ListAppend(reason,"LastName changed from #checkMember.LastName# to #arguments.formData.LNAME#",chr(10));
				if (compare(checkMember.BillingFirm,trim(arguments.formData.IFIRM)))
					reason = ListAppend(reason,"BillingFirm changed from #checkMember.BillingFirm# to #arguments.formData.IFIRM#",chr(10));
				if (compare(checkMember.BillingADDRESS,trim(arguments.formData.IADDRESS1)))
					reason = ListAppend(reason,"BillingAddress changed from #checkMember.BillingADDRESS# to #arguments.formData.IADDRESS1#",chr(10));
				if (compare(checkMember.BillingADDRESS2,trim(arguments.formData.IADDRESS2)))
					reason = ListAppend(reason,"BillingAddress2 changed from #checkMember.BillingADDRESS2# to #arguments.formData.IADDRESS2#",chr(10));
				if (compare(checkMember.BillingCITY,trim(arguments.formData.ICITY)))
					reason = ListAppend(reason,"BillingCity changed from #checkMember.BillingCITY# to #arguments.formData.ICITY#",chr(10));
				if (compare(checkMember.BillingState,trim(arguments.formData.IST)))
					reason = ListAppend(reason,"BillingState changed from #checkMember.BillingState# to #arguments.formData.IST#",chr(10));
				if (compare(checkMember.BillingZip,trim(arguments.formData.IZIPCODE)))
					reason = ListAppend(reason,"BillingZip changed from #checkMember.BillingZip# to #arguments.formData.IZIPCODE#",chr(10));
				if (compare(checkMember.BillingCountry,trim(arguments.formData.ICOUNTRY)))
					reason = ListAppend(reason,"BillingCountry changed from #checkMember.BillingCountry# to #arguments.formData.ICOUNTRY#",chr(10));
				if (compare(checkMember.Phone,trim(arguments.formData.ITEL)))
					reason = ListAppend(reason,"Phone changed from #checkMember.Phone# to #arguments.formData.ITEL#",chr(10));
				if (compare(checkMember.FAX,trim(arguments.formData.IFAX)))
					reason = ListAppend(reason,"Fax changed from #checkMember.FAX# to #arguments.formData.IFAX#",chr(10));
				if (compare(checkMember.Email,trim(arguments.formData.INTERNET)))
					reason = ListAppend(reason,"Email changed from #checkMember.Email# to #arguments.formData.INTERNET#",chr(10));
				if (compare(checkMember.optOutTSMarketing,val(arguments.formData.optOutTSMarketing)))
					reason = ListAppend(reason,"Opt-out TrialSmith Marketing changed from #checkMember.optOutTSMarketing# to #val(arguments.formData.optOutTSMarketing)#",chr(10));
				if (compare(checkMember.TLAMemberState,trim(arguments.formData.IRECTYPE)))
					reason = ListAppend(reason,"Main Association changed from #checkMember.TLAMemberState# to #arguments.formData.IRECTYPE#",chr(10));
				if (compare(checkMember.membertype,trim(arguments.formData.MEMBERTYPE)))
					reason = ListAppend(reason,"Subscription Plan changed from #checkMember.MEMBERTYPE# to #arguments.formData.MEMBERTYPE#",chr(10));
				if (datecompare(checkMember.renewalDate,trim(arguments.formData.renewalDate),"d"))
					reason = ListAppend(reason,"Renewal Date changed from #dateformat(checkMember.renewalDate,'mm/dd/yyyy')# to #arguments.formData.renewalDate#",chr(10));
				if (compare(checkMember.MailCode,trim(arguments.formData.IMAILCODE)))
					reason = ListAppend(reason,"MailCode changed from #checkMember.MailCode# to #arguments.formData.IMAILCODE#",chr(10));
				if (compare(checkMember.Pending,trim(arguments.formData.Pending)))
					reason = ListAppend(reason,"Pending changed from #checkMember.Pending# to #arguments.formData.Pending#",chr(10));
				if (compare(trim(checkMember.PaymentType),trim(arguments.formData.PaymentType)))
					reason = ListAppend(reason,"PaymentType changed from #checkMember.PaymentType# to #arguments.formData.PaymentType#",chr(10));
				if (compare(checkMember.TSAllowed,VAL(arguments.formData.tsallowed)))
					reason = ListAppend(reason,"Search Tool Access changed from #checkMember.TSAllowed# to #val(arguments.formData.tsallowed)#",chr(10));
				if (compare(checkMember.isDocumentChatEnabled,VAL(arguments.formData.isDocumentChatEnabled)))
					reason = ListAppend(reason,"Document Chat changed from #checkMember.isDocumentChatEnabled# to #val(arguments.formData.isDocumentChatEnabled)#",chr(10));
				if (compare(checkMember.includeInSubscriberDepos,VAL(arguments.formData.includeInSubscriberDepos)))
					reason = ListAppend(reason,"Deposed Experts Bucket changed from #checkMember.includeInSubscriberDepos# to #val(arguments.formData.includeInSubscriberDepos)#",chr(10));
				if (compare(checkMember.juryResearchEnabled,VAL(arguments.formData.juryResearchEnabled)))
					reason = ListAppend(reason,"JurySmith Access changed from #checkMember.juryResearchEnabled# to #val(arguments.formData.juryResearchEnabled)#",chr(10));
				if (compare(checkMember.DepoContactFirstName,trim(arguments.formData.DEPOCONTACTFNAME)))
					reason = ListAppend(reason,"Depo Contact First Name changed from #checkMember.DepoContactFirstName# to #arguments.formData.DEPOCONTACTFNAME#",chr(10));
				if (compare(checkMember.DepoContactLastName,trim(arguments.formData.DEPOCONTACTLNAME)))
					reason = ListAppend(reason,"Depo Contact Last Name changed from #checkMember.DepoContactLastName# to #arguments.formData.DEPOCONTACTLNAME#",chr(10));
				if (compare(checkMember.DepoContactEmail,trim(arguments.formData.DEPOCONTACTEMAIL)))
					reason = ListAppend(reason,"Depo Contact Email changed from #checkMember.DepoContactEmail# to #arguments.formData.DEPOCONTACTEMAIL#",chr(10));
				if (compare(checkMember.DepoContactPhone,trim(arguments.formData.DEPOCONTACTPHONE)))
					reason = ListAppend(reason,"Depo Contact Phone changed from #checkMember.DepoContactPhone# to #arguments.formData.DEPOCONTACTPHONE#",chr(10));
				if (len(checkMember.DepoContactDate) and len(arguments.formData.DEPOCONTACTDATE) and datecompare(checkMember.DepoContactDate,trim(arguments.formData.DEPOCONTACTDATE),"d"))
					reason = ListAppend(reason,"Renewal Date changed from #dateformat(checkMember.DepoContactDate,'mm/dd/yyyy')# to #arguments.formData.DEPOCONTACTDATE#",chr(10));
				if (len(checkMember.DepoContactDate) and NOT len(arguments.formData.DEPOCONTACTDATE))
					reason = ListAppend(reason,"Renewal Date changed from #dateformat(checkMember.DepoContactDate,'mm/dd/yyyy')# to [blank]",chr(10));
				if (NOT len(checkMember.DepoContactDate) and len(arguments.formData.DEPOCONTACTDATE))
					reason = ListAppend(reason,"Renewal Date changed from [blank] to #arguments.formData.DEPOCONTACTDATE#",chr(10));
				if (compare(checkMember.BillingContactName,trim(arguments.formData.BILLINGCONTACTNAME)))
					reason = ListAppend(reason,"Billing Contact Name changed from #checkMember.BillingContactName# to #arguments.formData.BILLINGCONTACTNAME#",chr(10));
				if (compare(checkMember.BillingContactEmail,trim(arguments.formData.BILLINGCONTACTEMAIL)))
					reason = ListAppend(reason,"Billing Contact Email changed from #checkMember.BillingContactEmail# to #arguments.formData.BILLINGCONTACTEMAIL#",chr(10));
				</cfscript>
					
				<cfif ListLen(reason)>

					<cftransaction>
						<!--- Save account --->
						<cfquery name="updateMember" datasource="#application.settings.dsn.trialsmith.dsn#">
							SET NOCOUNT ON;

							DECLARE @depoMemberDataID int = <cfqueryparam value="#arguments.formData.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">;

							UPDATE depoMemberData 
							SET FirstNAME = <cfqueryparam value="#trim(arguments.formData.FNAME)#" cfsqltype="CF_SQL_VARCHAR">,
								LastName = <cfqueryparam value="#trim(arguments.formData.LNAME)#" cfsqltype="CF_SQL_VARCHAR">,
								BillingFirm = <cfqueryparam value="#trim(arguments.formData.IFIRM)#" cfsqltype="CF_SQL_VARCHAR">,
								BillingADDRESS = <cfqueryparam value="#trim(arguments.formData.IADDRESS1)#" cfsqltype="CF_SQL_VARCHAR">,
								BillingADDRESS2 = <cfqueryparam value="#trim(arguments.formData.IADDRESS2)#" cfsqltype="CF_SQL_VARCHAR">,
								BillingCITY = <cfqueryparam value="#trim(arguments.formData.ICITY)#" cfsqltype="CF_SQL_VARCHAR">,
								BillingState = <cfqueryparam value="#trim(arguments.formData.IST)#" cfsqltype="CF_SQL_VARCHAR">,
								BillingZip = <cfqueryparam value="#trim(arguments.formData.IZIPCODE)#" cfsqltype="CF_SQL_VARCHAR">,
								BillingCountry = <cfqueryparam value="#trim(arguments.formData.ICOUNTRY)#" cfsqltype="CF_SQL_INTEGER">,
								Phone = <cfqueryparam value="#trim(arguments.formData.ITEL)#" cfsqltype="CF_SQL_VARCHAR">,
								FAX = <cfqueryparam value="#trim(arguments.formData.IFAX)#" cfsqltype="CF_SQL_VARCHAR">,
								Email = <cfqueryparam value="#trim(arguments.formData.INTERNET)#" cfsqltype="CF_SQL_VARCHAR">,
								TLAMemberState = <cfqueryparam value="#trim(arguments.formData.IRECTYPE)#" cfsqltype="CF_SQL_VARCHAR">,
								MemberType = <cfqueryparam value="#arguments.formData.membertype#" cfsqltype="CF_SQL_INTEGER">,
								RenewalDate = <cfqueryparam value="#arguments.formData.RenewalDate#" cfsqltype="CF_SQL_DATE">,
								MailCode = <cfqueryparam value="#arguments.formData.IMAILCODE#" cfsqltype="CF_SQL_VARCHAR">,
								Pending = <cfqueryparam value="#arguments.formData.Pending#" cfsqltype="CF_SQL_VARCHAR">,
								PaymentType = <cfqueryparam value="#arguments.formData.PaymentType#" cfsqltype="CF_SQL_VARCHAR">,
								TSAllowed = <cfqueryparam value="#VAL(arguments.formData.tsallowed)#" cfsqltype="CF_SQL_BIT">,
								isDocumentChatEnabled = <cfqueryparam value="#VAL(arguments.formData.isDocumentChatEnabled)#" cfsqltype="CF_SQL_BIT">,
								includeInSubscriberDepos = <cfqueryparam value="#VAL(arguments.formData.includeInSubscriberDepos)#" cfsqltype="CF_SQL_BIT">,
								juryResearchEnabled = <cfqueryparam value="#VAL(arguments.formData.juryResearchEnabled)#" cfsqltype="CF_SQL_BIT">,
								DepoContactFirstName = <cfqueryparam value="#trim(arguments.formData.DEPOCONTACTFNAME)#" cfsqltype="CF_SQL_VARCHAR">, 
								DepoContactLastName = <cfqueryparam value="#trim(arguments.formData.DEPOCONTACTLNAME)#" cfsqltype="CF_SQL_VARCHAR">,
								DepoContactEmail = <cfqueryparam value="#trim(arguments.formData.DEPOCONTACTEMAIL)#" cfsqltype="CF_SQL_VARCHAR">, 
								DepoContactPhone = <cfqueryparam value="#trim(arguments.formData.DEPOCONTACTPHONE)#" cfsqltype="CF_SQL_VARCHAR">, 
								<cfif len(arguments.formData.DEPOCONTACTDATE) and IsDate(arguments.formData.DEPOCONTACTDATE)>
									DepoContactDate = <cfqueryparam value="#arguments.formData.DEPOCONTACTDATE#" cfsqltype="CF_SQL_DATE">,
								<cfelse>
									DepoContactDate = <cfqueryparam value="" cfsqltype="CF_SQL_DATE" null="Yes">,
								</cfif>
								BillingContactName = <cfqueryparam value="#trim(arguments.formData.BILLINGCONTACTNAME)#" cfsqltype="CF_SQL_VARCHAR">, 
								BillingContactEmail = <cfqueryparam value="#trim(arguments.formData.BILLINGCONTACTEMAIL)#" cfsqltype="CF_SQL_VARCHAR">,
								optOutTSMarketing = <cfqueryparam value="#val(arguments.formData.optOutTSMarketing)#" cfsqltype="CF_SQL_BIT">
							WHERE depomemberdataid = @depoMemberDataID;

							EXEC dbo.depomember_updateUniqueNames
								@firstName = <cfqueryparam value="#trim(arguments.formData.FNAME)#" cfsqltype="CF_SQL_VARCHAR">,
								@lastName = <cfqueryparam value="#trim(arguments.formData.LNAME)#" cfsqltype="CF_SQL_VARCHAR">,
								@depomemberdataID = @depoMemberDataID;

							<cfif VAL(arguments.formData.includeInSubscriberDepos) is 0>
								DELETE FROM searchMC.dbo.ts_subscriberDepositions
								WHERE depomemberDataID = @depoMemberDataID;
							</cfif>
						</cfquery>
				
						<!--- Insert note --->
						<cfscript>
						insertNote(arguments.formData.depoMemberDataID,1,"Account Updated by #session.name#:#chr(10)##reason#");
						</cfscript>
						
					</cftransaction>
				</cfif>

			<!--- Else, the record was updated by something else after the member page was loaded --->
			<cfelse>
				<!--- location to member account page to stop all further processing --->
				<cflocation url="MemberEdit.cfm?depomemberDataID=#arguments.formData.depoMemberDataID#&msg=9" addtoken="No">
			</cfif>

		</cfif>
	</cffunction>

	<cffunction name="cloneAccount" access="public" output="false" returntype="numeric">
		<cfargument name="formData" type="struct" required="Yes">

		<cfset var local = structNew()>
		<cfset local.cloneDepoMemberDataID = arguments.formData.depoMemberDataID>
		<cfset local.qryDepoMemberData = application.objCommon.getMemberData(depoMemberDataID=arguments.formData.depoMemberDataID)>
				
		<cfquery name="local.qryCloneMemberData" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @depomemberdataID int, @newDepoMemberDataID int, @sourceID int, @nowDate datetime = GETDATE();
				SET @depomemberdataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.cloneDepoMemberDataID#">;

				-- create sourceID that doesnt already exist
				SET @sourceID = 0;
				WHILE @sourceID = 0 BEGIN
					SET @sourceID = abs(cast(newid() as binary(6)) %999999) + 1;
					IF EXISTS (Select top 1 sourceid FROM dbo.depomemberdata WHERE sourceid = @sourceID)
						SET @sourceID = 0;
				END
				
				BEGIN TRAN;
					INSERT INTO dbo.depoMemberData (SourceID, Pending, UserAddress, BankAdminFlag, AdminFlag2, DepoConnectFlag, 
						MemberEnrollDate, BillingFirm, BillingAddress, BillingAddress2, BillingAddress3, BillingCity, BillingState, BillingZip, 
						RenewalDate, LastName, FirstName, MiddleName, suffix, Phone, Fax, Email, MailCode, TLAMemberState, membertype, RegCode, 
						viewerflag, optOutTSMarketing, faxflag, signuporgcode, TSAllowed, includeInSubscriberDepos, 
						paymenttype, linksource, linkterms, billingCountry, juryResearchEnabled, DepoContactFirstName, DepoContactLastName,
						DepoContactEmail, DepoContactPhone, DepoContactDate, BillingContactName, BillingContactEmail, uniqueNameID)
					SELECT @sourceID, Pending, UserAddress, BankAdminFlag, AdminFlag2, DepoConnectFlag, @nowDate, 
						BillingFirm, BillingAddress, BillingAddress2, BillingAddress3, BillingCity, BillingState, BillingZip, 
						RenewalDate, LastName, FirstName, MiddleName, suffix, Phone, Fax, Email, MailCode, TLAMemberState, 
						1, RegCode, viewerflag, optOutTSMarketing, faxflag, signuporgcode, 
						TSAllowed, includeInSubscriberDepos, paymenttype, '', '', billingCountry, juryResearchEnabled, 
						DepoContactFirstName, DepoContactLastName, DepoContactEmail, DepoContactPhone, DepoContactDate, BillingContactName, 
						BillingContactEmail, uniqueNameID
					FROM dbo.depoMemberData
					WHERE depomemberdataID = @depomemberdataID;

					SET @newDepoMemberDataID = SCOPE_IDENTITY();

					INSERT INTO dbo.depoTransactions (depoMemberdataid, amountbilled, salestaxamount, accountcode, datepurchased, 
						description, SourceState, orgcode, madeWhilePending, stateForTax, zipForTax)
					VALUES (@newDepoMemberDataID, 0, 0, '1000', @nowDate, 'Basic Plan Subscription Fee',
						<cfqueryparam value="#local.qryDepoMemberData.TLAMemberState#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#local.qryDepoMemberData.signuporgcode#" cfsqltype="CF_SQL_VARCHAR">,
						1, 
						<cfqueryparam value="#local.qryDepoMemberData.BillingState#" cfsqltype="CF_SQL_VARCHAR">,
						<cfif local.qryDepoMemberData.BillingCountry is 1>
							<cfqueryparam value="#left(local.qryDepoMemberData.BillingZip,5)#" cfsqltype="CF_SQL_VARCHAR">
						<cfelse>
							<cfqueryparam value="#local.qryDepoMemberData.BillingZip#" cfsqltype="CF_SQL_VARCHAR">
						</cfif>
					);


					INSERT INTO dbo.CustomerNotes (depomemberdataid, NoteTypeID, Note)
					VALUES (@newDepoMemberDataID, 1, <cfqueryparam value="Account created by #session.name# via clone of DepoID #local.cloneDepoMemberDataID#" cfsqltype="CF_SQL_VARCHAR">);
				COMMIT TRAN;

				SELECT @newDepoMemberDataID AS newDepoMemberDataID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryCloneMemberData.newDepoMemberDataID>
	</cffunction>

	<cffunction name="insertNote" access="public" displayname="Insert a note into a user's account" returntype="void">
		<cfargument name="depoMemberDataID" type="numeric" required="Yes">
		<cfargument name="noteTypeID" type="numeric" required="Yes">
		<cfargument name="note" type="string" required="Yes">

		<cfset var qryinsertNote = "">

		<!--- Insert note --->
		<cfquery name="qryinsertNote" datasource="#application.settings.dsn.trialsmith.dsn#">
			insert into CustomerNotes (depomemberdataid, NoteTypeID, Note)
			values(
				<cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">,
				<cfqueryparam value="#arguments.noteTypeID#" cfsqltype="CF_SQL_INTEGER">,
				<cfqueryparam value="#arguments.note#" cfsqltype="CF_SQL_VARCHAR">
			)
		</cfquery>
	</cffunction>

	<cffunction name="makeSuperUser" access="public" displayname="Make account SuperUser" returntype="void" output="yes">
		<cfargument name="membernumber" type="string" required="Yes">

		<cfset var qryConvert = "">

		<!--- TS Admin record must be affiliated with MC record (not in waiting) and that MC record must be in superuser group --->
		<cfquery name="qryConvert" datasource="#application.settings.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				declare @memberNumber varchar(50), @memberNumberOfAnExistingSuperAdmin varchar(50), @depomemberdataid int, @profileID int, @memberID int;
				set @memberNumber = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.membernumber#">;
				set @memberNumberOfAnExistingSuperAdmin = 'TS64960';

				select @depomemberdataid = np.depomemberdataID, @profileID = np.profileID, @memberID = m.memberID
				from dbo.ams_members as m
				inner join dbo.organizations as o on o.orgID = m.orgID
					and o.orgcode = 'MC'
					and m.memberNumber = @memberNumber
					and m.memberID = m.activeMemberID
				inner join dbo.ams_memberNetworkProfiles as mnp on mnp.memberID = m.memberID
				inner join dbo.ams_networkProfiles as np on np.profileID = mnp.profileID;

				if @depomemberdataid is not null
					update trialsmith.dbo.depomemberdata
					set AdminFlag2 = 'Y', RenewalDate = dateadd(yyyy,10,getdate()) 
					where depomemberdataid = @depomemberdataid;

				update np 
				set np.networkID = 1
				from dbo.ams_members as m
				inner join dbo.organizations as o on o.orgID = m.orgID
					and o.orgcode = 'MC'
					and m.memberNumber = @memberNumber
				inner join dbo.ams_memberNetworkProfiles as mnp on mnp.memberID = m.memberID
				inner join dbo.ams_networkProfiles as np on np.profileID = mnp.profileID;

				insert into dbo.ams_memberNetworkProfiles (memberID, profileID, siteID, status, dateCreated, dateSiteAgreement)
				select @memberID as memberID, @profileID as profileID, mnp.siteID, mnp.status, getdate(), getdate()
				from dbo.ams_members as m
				inner join dbo.organizations as o on o.orgID = m.orgID
					and o.orgcode = 'MC'
					and m.memberNumber = @memberNumberOfAnExistingSuperAdmin
				inner join dbo.ams_memberNetworkProfiles as mnp on mnp.memberID = m.memberID
				inner join dbo.ams_networkProfiles as np on np.profileID = mnp.profileID
				left outer join dbo.ams_memberNetworkProfiles as mnp2 on mnp2.siteID = mnp.siteID
					and mnp2.profileID = @profileID
				where mnp2.siteID is null;


				-- process conditions and groups
				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
					DROP TABLE ##tblMCQRun;
				CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
				values (1, @memberID, null);

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
					DROP TABLE ##tblMCQRun;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="loginAsMember" returntype="void" access="public" output="yes">
		<cfargument name="siteCode" type="string" required="yes">
		<cfargument name="membernumber" type="string" required="yes">

		<cfset var local = structNew()>

		<cfset local.strAPIResult = application.objMCAPI.api(siteCode=arguments.sitecode, method="POST", endpoint="member/#urlencodedformat(arguments.membernumber)#/loginurl")>
		<cfif local.strAPIResult.error eq false>
			<cfset local.lamURL = local.strAPIResult.data.loginurl>
		<cfelse>
			<cfset local.lamURL = "unable to generate">
		</cfif>

		<cfoutput>
		<div style="margin:10px;">
			<div id="pageTitle">Login as Member</div>
			<br/>
			<div>
				Use the following link to login as this member. <span class="tsAppBodyTextImportant">This link expires in 5 minutes.</span>
				<br/><br/>
				Because you may be currently logged into this site, we suggest you use this link in a different browser or incognito window to not cause conflicts with your current session.
				<br/><br/>
				<input type="text" id="loginAsMemberInput" readonly="readonly" value="#local.lamURL#" style="width:95%;" onclick="this.select();">
			</div>
		</div>
		</cfoutput>
	</cffunction>

	<cffunction name="zendeskSSO" returntype="void" access="public" output="yes">
		<cfargument name="siteCode" type="string" required="yes">
		<cfargument name="membernumber" type="string" required="yes">

		<cfset var local = structNew()>

		<cfset local.strAPIResult = application.objMCAPI.api(siteCode=arguments.sitecode, method="POST", endpoint="member/#urlencodedformat(arguments.membernumber)#/sso/zendesk")>
		
		<cfoutput>
		<cfif local.strAPIResult.error eq false>
			<form id="jwtForm" name="jwtForm" method="post" action="#local.strAPIResult.data.ssourl#">
				<input type="hidden" id="jwtInput" name="jwt" value="#local.strAPIResult.data.jwt#">
			</form>
			<script type="text/javascript">
				document.forms['jwtForm'].submit();
			</script>
		<cfelse>
			<div class="tsAppBodyTextImportant">Unable to generate ZenDesk URL</div>
		</cfif>
		</cfoutput>
	</cffunction>

	<cffunction name="saveNote" access="public" displayname="Save a note to a user account." returntype="void">
		<cfargument name="formData" type="struct" required="Yes">

		<cfscript>
		insertNote(arguments.formData.depoMemberDataID,arguments.formData.notetypeid,"#arguments.formData.NewNote# - by #session.name#");
		</cfscript>
	</cffunction>

	<cffunction name="getMatchingMembers" access="public" hint="Returns a query of matching members" returntype="query" output="no">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		
		<cfset var local = structNew()>	

		<cfquery name="local.qryMatching" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT count(distinct pm.depoMemberDataID) as MatchCount, count(distinct pm.depoMemberDataID)*100/7 as MatchPct, pm.SourceID, 
				pm.depomemberdataID, pm.LastName, pm.FirstName, pm.BillingFirm, pm.BillingState, pm.TLAMemberState, 
				pm.membertype, pm.signuporgcode, pm.renewaldate, pm.status, pm.isFinal, pm.hasPendingTransactions,
				MAX(mnp.dateLastLogin) as dateLastLogin
			FROM dbo.fn_Account_possibleMatches(<cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">) as pm
			LEFT OUTER JOIN membercentral.dbo.ams_networkProfiles as np 
				INNER JOIN membercentral.dbo.ams_memberNetworkProfiles as mnp on mnp.profileID = np.profileID and mnp.status = 'A'
				on np.depomemberdataID = pm.depomemberdataID and np.status = 'A'
			GROUP BY pm.SourceID, pm.depomemberdataID, pm.LastName, pm.FirstName, pm.BillingFirm, pm.BillingState, 
				pm.TLAMemberState, pm.membertype, pm.signuporgcode, pm.renewaldate, pm.status, pm.isFinal, pm.hasPendingTransactions
			ORDER BY MatchCount DESC, pm.LastName, pm.FirstName, pm.depomemberdataID desc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryMatching>
	</cffunction>

	<cffunction name="getMemberBySourceID" access="public" hint="Returns a member based on sourceID" returntype="struct" output="no">
		<cfargument name="sourceID" type="numeric" required="yes">
		
		<cfset var local = StructNew()>
		
		<cfquery name="local.qryLookup" datasource="#application.settings.dsn.trialsmith.dsn#">
			select top 1 depomemberdataid, sourceid, firstname, lastname
			from depomemberdata
			where sourceID = <cfqueryparam value="#arguments.sourceID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfscript>
		local.data = StructNew();
		local.data.success = true;
		local.data.qryLookup = local.qryLookup;
		</cfscript>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="affiliateTS" access="public" returntype="void" output="no">
		<cfargument name="formdata" type="struct" required="Yes">

		<cfset var memberNumber = CreateObject("component","models.trialsmith.tsJoinTrialSmith").affiliateTS(depoMemberDataID=arguments.formdata.depoMemberDataID)>

		<cflocation url="MemberEdit.cfm?depomemberDataID=#arguments.formData.depoMemberDataID#" addtoken="No">
	</cffunction>

</cfcomponent>