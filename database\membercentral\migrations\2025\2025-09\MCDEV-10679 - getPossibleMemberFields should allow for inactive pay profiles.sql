use membercentral
GO

ALTER FUNCTION dbo.fn_ams_getPossibleMemberFields (
	@orgID int
)
RETURNS @possibleFields TABLE (
	autoID int IDENTITY(1,1),
	sortGroup1 varchar(21), 
	sortGroup2 varchar(200), 
	sortGroup3 varchar(300), 
	sorting varchar(40), 
	dbObject varchar(40), 
	dbObjectalias varchar(5),
	db<PERSON><PERSON> varchar(128) PRIMARY KEY, 
	fieldcode varchar(40), 
	fieldLabel varchar(420), 
	displayTypeCode varchar(20), 
	dataTypeCode varchar(20)
)
AS
BEGIN

	DECLARE @orgCode varchar(10), @hasPrefix bit, @usePrefixList bit, @hasMiddleName bit, @hasSuffix bit, @hasProfessionalSuffix bit,
		@hasdistrictMatchingForAnyType bit = 0, @hasdistrictMatchingForAnyTag bit = 0;

	select @orgcode=orgcode, @hasPrefix=hasPrefix, @usePrefixList=usePrefixList, @hasMiddleName=hasMiddleName,
		@hasSuffix=hasSuffix, @hasProfessionalSuffix=hasProfessionalSuffix
	from dbo.organizations 
	where orgID = @orgid;

	-- ams_members
	INSERT INTO @possibleFields
	select 'Member Data' as sortGroup1, 'Demographic Data' as sortGroup2, null as sortGroup3, '001.001' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'prefix' as dbField, 'm_prefix' as fieldCode, 'Prefix' as fieldLabel, case when @usePrefixList = 0 then 'TEXTBOX' else 'SELECT' end as displayTypeCode, 'STRING' as dataTypeCode where @hasPrefix = 1
		union all
	select 'Member Data' as sortGroup1, 'Demographic Data' as sortGroup2, null as sortGroup3, '001.002' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'firstname' as dbField, 'm_firstname' as fieldCode, 'First Name' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'Demographic Data' as sortGroup2, null as sortGroup3, '001.003' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'middlename' as dbField, 'm_middlename' as fieldCode, 'Middle Name' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode where @hasMiddleName = 1
		union all
	select 'Member Data' as sortGroup1, 'Demographic Data' as sortGroup2, null as sortGroup3, '001.004' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'lastname' as dbField, 'm_lastname' as fieldCode, 'Last Name' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'Demographic Data' as sortGroup2, null as sortGroup3, '001.005' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'suffix' as dbField, 'm_suffix' as fieldCode, 'Suffix' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode where @hasSuffix = 1
		union all
	select 'Member Data' as sortGroup1, 'Demographic Data' as sortGroup2, null as sortGroup3, '001.006' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'professionalsuffix' as dbField, 'm_professionalsuffix' as fieldCode, 'Professional Suffix' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode where @hasProfessionalSuffix = 1
		union all
	select 'Member Data' as sortGroup1, 'Demographic Data' as sortGroup2, null as sortGroup3, '001.007' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'company' as dbField, 'm_company' as fieldCode, 'Company' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'Demographic Data' as sortGroup2, null as sortGroup3, '001.008' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'membernumber' as dbField, 'm_membernumber' as fieldCode, 'MemberNumber' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'MemberCentral Data' as sortGroup2, null as sortGroup3, '002.001' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'recordtypeid' as dbField, 'm_recordtypeid' as fieldCode, 'MCRecordType' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'MemberCentral Data' as sortGroup2, null as sortGroup3, '002.002' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'status' as dbField, 'm_status' as fieldCode, 'MCAccountStatus' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'MemberCentral Data' as sortGroup2, null as sortGroup3, '002.003' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'membertypeid' as dbField, 'm_membertypeid' as fieldCode, 'MCAccountType' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'MemberCentral Data' as sortGroup2, null as sortGroup3, '002.004' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'earliestdatecreated' as dbField, 'm_earliestdatecreated' as fieldCode, 'Created Date' as fieldLabel, 'DATE' as displayTypeCode, 'DATE' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'MemberCentral Data' as sortGroup2, null as sortGroup3, '002.005' as sorting, 'ams_members' as dbObject, 'm' as dbObjectAlias, 'datelastupdated' as dbField, 'm_datelastupdated' as fieldCode, 'Last Updated Date' as fieldLabel, 'DATE' as displayTypeCode, 'DATE' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'MemberCentral Data' as sortGroup2, null as sortGroup3, '002.006' as sorting, 'ml' as dbObject, 'ml' as dbObjectAlias, 'ml_datelastlogin_0' as dbField, 'ml_datelastlogin_0' as fieldCode, 'Last Login' as fieldLabel, 'DATE' as displayTypeCode, 'DATE' as dataTypeCode
		union all
	select 'Member Data' as sortGroup1, 'MemberCentral Data' as sortGroup2, null as sortGroup3, '002.' + right('000'+cast(ROW_NUMBER() OVER(order by s.siteName)+6 as varchar(10)),3) as sorting, 'ml' as dbObject, 'ml' as dbObjectAlias, 'ml_datelastlogin_' + cast(s.siteID as varchar(10)) as dbField, 'ml_datelastlogin_' + cast(s.siteID as varchar(10)) as fieldCode, 'Last Login on ' + s.siteName as fieldLabel, 'DATE' as displayTypeCode, 'DATE' as dataTypeCode
	from dbo.sites as s
	where s.orgID = @orgID;

	-- accounting
	INSERT INTO @possibleFields
	select 'Accounting' as sortGroup1, null as sortGroup2, null as sortGroup3, '003.001' as sorting, 'acct' as dbObject, 'acct' as dbObjectAlias, 'acct_balance_0' as dbField, 'acct_balance_0' as fieldCode, 'Total Credit Balance' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		union all
	select 'Accounting' as sortGroup1, null as sortGroup2, null as sortGroup3, '003.' + right('000'+cast(ROW_NUMBER() OVER(order by mp.profileName)+1 as varchar(10)),3) as sorting, 'acct' as dbObject, 'acct' as dbObjectAlias, 'acct_balance_' + cast(mp.profileID as varchar(10)) as dbField, 'acct_balance_' + cast(mp.profileID as varchar(10)) as fieldCode, mp.profileName + ' Credit Balance' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
	from dbo.mp_profiles as mp
	inner join dbo.sites as s on s.siteID = mp.siteID
	inner join dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
	where s.orgID = @orgID
	and mp.status IN ('A','I')
	and g.isActive = 1
	and g.gatewayType not in ('PayLater');

	-- addresses
	if exists (select 1 from dbo.ams_memberAddressTypes where orgID = @orgID and districtMatching = 1)
		set @hasdistrictMatchingForAnyType = 1;
	if exists (select 1 from dbo.ams_memberAddressTagTypes where orgID = @orgID and districtMatching = 1)
		set @hasdistrictMatchingForAnyTag = 1;

	declare @tblAddrTypes table (isTag bit, addressTypeID int, addressType varchar(71), addressTypeOrder int, hasAttn bit, hasAddress2 bit, hasAddress3 bit, hasCounty bit, districtMatching bit);

	insert into @tblAddrTypes
	select 0, addressTypeID, addressType, addressTypeOrder, hasAttn, hasAddress2, hasAddress3, hasCounty,
		case when districtMatching = 1 or @hasdistrictMatchingForAnyTag = 1 then 1 else 0 end as districtMatching
	from dbo.ams_memberAddressTypes 
	where orgID = @orgID;

	insert into @tblAddrTypes
	select 1, addressTagTypeID, 'Designated ' + addressTagType, 100+addressTagTypeOrder, 
		case when exists (select addressTypeID from @tblAddrTypes where hasAttn = 1) then 1 else 0 end,
		case when exists (select addressTypeID from @tblAddrTypes where hasAddress2 = 1) then 1 else 0 end,
		case when exists (select addressTypeID from @tblAddrTypes where hasAddress3 = 1) then 1 else 0 end,
		case when exists (select addressTypeID from @tblAddrTypes where hasCounty = 1) then 1 else 0 end,
		case when @hasdistrictMatchingForAnyType = 1 or @hasdistrictMatchingForAnyTag = 1 then 1 else 0 end
	from dbo.ams_memberAddressTagTypes 
	where orgID = @orgID;

	INSERT INTO @possibleFields
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.001' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_addressType' as dbField, 'mat_' + cast(mat.addressTypeID as varchar(10)) + '_addresstype' as fieldCode, mat.addressType + ' Address Type' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		where mat.isTag = 1
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.002' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_attn' as dbField, case when isTag = 1 then 'mat_' else 'ma_' end + cast(mat.addressTypeID as varchar(10)) + '_attn' as fieldCode, mat.addressType + ' Attn' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		where mat.hasAttn = 1
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.003' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_address1' as dbField, case when isTag = 1 then 'mat_' else 'ma_' end + cast(mat.addressTypeID as varchar(10)) + '_address1' as fieldCode, mat.addressType + ' Address' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.004' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_address2' as dbField, case when isTag = 1 then 'mat_' else 'ma_' end + cast(mat.addressTypeID as varchar(10)) + '_address2' as fieldCode, mat.addressType + ' Address 2' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		where mat.hasAddress2 = 1
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.005' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_address3' as dbField, case when isTag = 1 then 'mat_' else 'ma_' end + cast(mat.addressTypeID as varchar(10)) + '_address3' as fieldCode, mat.addressType + ' Address 3' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		where mat.hasAddress3 = 1
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.006' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_city' as dbField, case when isTag = 1 then 'mat_' else 'ma_' end + cast(mat.addressTypeID as varchar(10)) + '_city' as fieldCode, mat.addressType + ' City' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.007' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_stateprov' as dbField, case when isTag = 1 then 'mat_' else 'ma_' end + cast(mat.addressTypeID as varchar(10)) + '_stateprov' as fieldCode, mat.addressType + ' State' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.008' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_postalcode' as dbField, case when isTag = 1 then 'mat_' else 'ma_' end + cast(mat.addressTypeID as varchar(10)) + '_postalcode' as fieldCode, mat.addressType + ' Postal Code' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.009' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_county' as dbField, case when isTag = 1 then 'mat_' else 'ma_' end + cast(mat.addressTypeID as varchar(10)) + '_county' as fieldCode, mat.addressType + ' County' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat
		where mat.hasCounty = 1
		union all
	select 'Addresses' as sortGroup1, mat.addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(mat.addressTypeOrder as varchar(10)),3) + '.010' as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, mat.addressType + '_country' as dbField, case when isTag = 1 then 'mat_' else 'ma_' end + cast(mat.addressTypeID as varchar(10)) + '_country' as fieldCode, mat.addressType + ' Country' as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode
		from @tblAddrTypes as mat;

	-- phones (under addresses)
	INSERT INTO @possibleFields
	select 'Addresses' as sortGroup1, addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(addressTypeOrder as varchar(10)),3) + '.' + right('000'+cast(10+row as varchar(10)),3) as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, addressType + '_' + phoneType as dbField, case when isTag = 1 then 'mpt_' else 'mp_' end + cast(addressTypeID as varchar(10)) + '_' + cast(phoneTypeID as varchar(10)) as fieldCode, addressType + ' ' + phoneType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
	from (
		select mat.isTag, mat.addressTypeID, mpt.phoneTypeID, mat.addressType, mpt.phoneType, mat.addressTypeOrder, 
			ROW_NUMBER() OVER (ORDER BY mat.addressTypeOrder, mpt.phoneTypeOrder) as row
		from dbo.ams_memberPhoneTypes as mpt
		cross join @tblAddrTypes as mat
		where mpt.orgID = @orgID
	) as tmpPhones;

	-- district matching (under addresses)
	INSERT INTO @possibleFields
	select 'Addresses' as sortGroup1, addressType as sortGroup2, null as sortGroup3, '004.' + right('000'+cast(addressTypeOrder as varchar(10)),3) + '.' + right('000'+cast(30+row as varchar(10)),3) as sorting, 'vw_' + @orgcode + '_ma' as dbObject, 'vwma' as dbObjectAlias, addressType + '_' + districtType as dbField, case when isTag = 1 then 'madt_' else 'mad_' end + cast(addressTypeID as varchar(10)) + '_' + cast(districtTypeID as varchar(10)) as fieldCode, addressType + ' ' + districtType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
	from (
		select mat.isTag, mat.addressTypeID, mdt.districtTypeID, mat.addressType, mdt.districtType, mat.addressTypeOrder, 
			ROW_NUMBER() OVER (ORDER BY mat.addressTypeOrder, mdt.districtTypeOrder) as row
		from dbo.ams_memberDistrictTypes as mdt
		cross join @tblAddrTypes as mat
		where mdt.orgID = @orgID
		and mat.districtMatching = 1
	) as tmpDistricts;

	-- custom fields
	INSERT INTO @possibleFields
	select 'Custom Fields' as sortGroup1, null as sortGroup2, null as sortGroup3, '005.' + right('000'+cast(ROW_NUMBER() OVER(order by mdc.columnName) as varchar(10)),3) as sorting, 'vw_' + @orgcode + '_md' as dbObject, 'vwmd' as dbObjectAlias, mdc.columnName as dbField, 'md_' + cast(mdc.columnID as varchar(10)) as fieldCode, mdc.columnName as fieldLabel, dt.displayTypeCode, ddt.dataTypeCode
		from dbo.ams_memberdatacolumns as mdc
		inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mdc.displayTypeID
		inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = mdc.dataTypeID
		where mdc.orgID = @orgID;
		
	-- emails
	INSERT INTO @possibleFields
	select 'E-mails' as sortGroup1, emailType as sortGroup2, null as sortGroup3, '006.' + right('000'+cast(emailTypeOrder as varchar(10)),3) + '.001' as sorting, 'vw_' + @orgcode + '_me' as dbObject, 'vwme' as dbObjectAlias, emailType as dbField, 'me_' + cast(emailTypeID as varchar(10)) + '_email' as fieldCode, emailType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberEmailTypes
		where orgID = @orgID
			union
	select 'E-mails' as sortGroup1, 'Designated ' + emailTagType as sortGroup2, null as sortGroup3, '006.' + right('000'+cast(100+emailTagTypeOrder as varchar(10)),3) + '.001' as sorting, 'vw_' + @orgcode + '_me' as dbObject, 'vwme' as dbObjectAlias, 'Designated ' + emailTagType + '_emailType' as dbField, 'met_' + cast(emailTagTypeID as varchar(10)) + '_emailType' as fieldCode, 'Designated ' + emailTagType + ' Email Type' as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberEmailTagTypes
		where orgID = @orgID
		union
	select 'E-mails' as sortGroup1, 'Designated ' + emailTagType as sortGroup2, null as sortGroup3, '006.' + right('000'+cast(100+emailTagTypeOrder as varchar(10)),3) + '.002' as sorting, 'vw_' + @orgcode + '_me' as dbObject, 'vwme' as dbObjectAlias, 'Designated ' + emailTagType as dbField, 'met_' + cast(emailTagTypeID as varchar(10)) + '_email' as fieldCode, 'Designated ' + emailTagType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberEmailTagTypes
		where orgID = @orgID;

	-- groups
	INSERT INTO @possibleFields
	select 'Group Memberships' as sortGroup1, null as sortGroup2, null as sortGroup3, '007.' + right('0000'+cast((ROW_NUMBER() OVER (order by g.groupPathSortOrder)) as varchar(20)),4) as sorting, 'grps' as dbObject, 'grps' as dbObjectAlias, 'grp_' + cast(g.groupID as varchar(10)) as dbField, 'grp_' + cast(g.groupID as varchar(10)) as fieldCode, left(g.groupPathExpanded,420) as fieldLabel, 'SELECT' as displayTypeCode, 'STRING' as dataTypeCode 
		from ams_groups g
		where g.orgID = @orgID 
		and g.hideOnGroupLists = 0
		AND g.status = 'A';

	-- professional licenses
	INSERT INTO @possibleFields
	select 'Professional Licenses' as sortGroup1, mplt.PLName as sortGroup2, null as sortGroup3, '008.' + right('000'+cast(mplt.orderNum as varchar(10)),3) + '.001' as sorting, 'vw_' + @orgcode + '_mpl' as dbObject, 'vwmpl' as dbObjectAlias, mplt.PLName + '_licenseNumber' as dbField, 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_licenseNumber' as fieldCode, mplt.PLName + ' ' + o.profLicenseNumberLabel as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes as mplt
		inner join dbo.organizations as o on o.orgID = mplt.orgID
		where mplt.orgID = @orgID
		union all
	select 'Professional Licenses' as sortGroup1, mplt.PLName as sortGroup2, null as sortGroup3, '008.' + right('000'+cast(mplt.orderNum as varchar(10)),3) + '.002' as sorting, 'vw_' + @orgcode + '_mpl' as dbObject, 'vwmpl' as dbObjectAlias, mplt.PLName + '_activeDate' as dbField, 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_activeDate' as fieldCode, mplt.PLName + ' ' + o.profLicenseDateLabel as fieldLabel, 'DATE' as displayTypeCode, 'DATE' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes as mplt
		inner join dbo.organizations as o on o.orgID = mplt.orgID
		where mplt.orgID = @orgID
		union all
	select 'Professional Licenses' as sortGroup1, mplt.PLName as sortGroup2, null as sortGroup3, '008.' + right('000'+cast(mplt.orderNum as varchar(10)),3) + '.003' as sorting, 'vw_' + @orgcode + '_mpl' as dbObject, 'vwmpl' as dbObjectAlias, mplt.PLName + '_status' as dbField, 'mpl_' + cast(mplt.PLTypeID as varchar(10)) + '_status' as fieldCode, mplt.PLName + ' ' + o.profLicenseStatusLabel as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberProfessionalLicenseTypes as mplt
		inner join dbo.organizations as o on o.orgID = mplt.orgID
		where mplt.orgID = @orgID;

	-- websites
	INSERT INTO @possibleFields
	select 'Websites' as sortGroup1, null as sortGroup2, null as sortGroup3, '010.' + right('000'+cast(mwt.websiteTypeOrder as varchar(10)),3) as sorting, 'vw_' + @orgcode + '_mw' as dbObject, 'vwmw' as dbObjectAlias, mwt.websiteType as dbField, 'mw_' + cast(mwt.websiteTypeID as varchar(10)) + '_website' as fieldCode, mwt.websiteType as fieldLabel, 'TEXTBOX' as displayTypeCode, 'STRING' as dataTypeCode
		from dbo.ams_memberWebsiteTypes as mwt
		where mwt.orgID = @orgID;

	RETURN;
END
GO
