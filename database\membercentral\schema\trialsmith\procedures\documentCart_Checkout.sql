ALTER PROC dbo.documentCart_Checkout
@depomemberdataID int,
@billingstate varchar(5),
@billingzip varchar(25),
@orgCode varchar(10),
@linksource varchar(50),
@linkterms varchar(100),
@oneClickPurchaseUID uniqueIdentifier = NULL,
@notificationEmail varchar(255) = NULL,
@statsSessionID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @transactionID int, @caseref varchar(600), @membertype int, @defaultUID uniqueIdentifier, @pcstart int, @pcend int,
		@verdictAmount numeric(18,2), @verdictTaxAmount numeric(18,2), @minCartID int, @itemtypeid int, 
		@documentid varchar(20), @pprice numeric(9,2), @psalestaxamount numeric(9,2), @amountOwed numeric(18,2),
		@allowPCuse bit, @prevTransactionID int, @transgroup int, @searchID int;

	-- get membertype
	select @membertype = membertype from depomemberdata where depomemberdataid = @depomemberdataid;

	-- UID used for comparison purposes only in query below
	set @defaultUID = '0226ECE7-C81F-432F-A125-B27F8117EC0D';

	BEGIN TRAN;
		-- get cart items
		select *, null as success
		into #tmpCart
		from dbo.fn_Cart_getDocuments(@depomemberdataID,@membertype,@billingstate,@billingzip,@orgCode,@oneClickPurchaseUID);

		-- get opening purchase credit and last transactionID recorded
		select @pcstart = balance from dbo.fn_Documents_getPurchaseCredits(@depomemberdataID);
		select @prevTransactionID = max(transactionID) from dbo.depoTransactions where depoMemberDataID = @depomemberdataID;
		if @prevTransactionID is null
			set @prevTransactionID = 0;

		/******************************************
		loop over remaining cart items and buy them
		*******************************************/
		SELECT @minCartID = min(cartID) from #tmpCart where success is null;
		WHILE @minCartID is not null BEGIN
			-- get info from cart
			select @itemtypeid = itemtypeid, @documentid = documentID, 
				@allowPCuse = CASE WHEN transactioncreditallowed = 'Y' then 1 else 0 end,
				@pprice = pprice, @psalestaxamount = psalestaxamount, @caseref = caseref,
				@searchID = searchID
			from #tmpCart
			where cartID = @minCartID;

			-- if disc action
			IF @itemtypeid = 6
				EXEC disiplinaryactions.dbo.up_buyActions @documentID, @depomemberdataID, @billingstate, @billingzip, @linksource, @linkterms, @orgcode, @pprice, @psalestaxamount, @caseref, @statsSessionID, @transactionID OUTPUT

			-- if mdex action
			IF @itemtypeid = 5
				EXEC daubertTracker.dbo.up_buyDaubert @documentID, @depomemberdataID, @billingstate, @billingzip, @linksource, @linkterms, @orgcode, @pprice, @psalestaxamount, @caseref, @statsSessionID, @transactionID OUTPUT

			-- if medline action
			IF @itemtypeid = 8
				EXEC searchMC.dbo.up_buyMedline @documentID, @depomemberdataID, @billingstate, @billingzip, @linksource, @linkterms, @orgcode, @pprice, @psalestaxamount, @caseref, @notificationEmail, @statsSessionID, @transactionID OUTPUT

			-- else if depo, courtdoc
			IF @itemtypeid IN (1,2,7)
				EXEC dbo.up_document_buy @documentID=@documentID, @depomemberdataID=@depomemberdataID, @membertype=@membertype, @billingstate=@billingstate, @billingzip=@billingzip, 
					@orgcode=@orgcode, @allowPCuse=@allowPCuse, @incFeePerUpload=0, @billingPlanID=NULL, @caseref=@caseref, @linksource=@linksource, @linkterms=@linkterms, 
					@statsSessionID=@statsSessionID, @transactionID=@transactionID OUTPUT;

			-- set success flag	
			UPDATE #tmpCart
			SET success = CASE WHEN @transactionID > 0 THEN 1 else 0 end
			where cartID = @minCartID;

			IF @transactionID > 0 AND @oneClickPurchaseUID IS NOT NULL BEGIN
				SELECT @amountOwed = NULL, @transgroup = NULL;

				SELECT @amountOwed = AmountBilled + salesTaxAmount, @transgroup = transgroup
				FROM dbo.depoTransactions
				WHERE transactionID = @transactionID;

				-- consider transgroup transactions
				SELECT @amountOwed = @amountOwed + ISNULL(SUM(AmountBilled + salesTaxAmount),0)
				FROM dbo.depoTransactions
				WHERE transgroup = @transgroup
				AND transactionID <> @transactionID
				AND depomemberdataID = @depomemberdataID
				AND documentID = @documentID
				AND (Reversable = 'Y' OR PurchaseCreditFlag = 'Y');

				IF @amountOwed > 0 
					INSERT INTO dbo.oneClickPurchases (purchaseDate, depoMemberDataID, documentID, depoTransactionID)
					VALUES (GETDATE(), @depomemberdataID, @documentID, @transactionID);
			END

			-- purchase resulted from search results
			IF @transactionID > 0 AND ISNULL(@searchID,0) > 0
				UPDATE dbo.depoTransactions
				SET searchID = @searchID
				WHERE TransactionID = @transactionID;

			SELECT @minCartID = min(cartID) from #tmpCart where success is null and cartID > @minCartID;
		END

		-- get ending purchase credit
		select @pcend = balance from dbo.fn_Documents_getPurchaseCredits(@depomemberdataID);

		-- return #tmpCart
		SELECT *, @pcstart-@pcend as pcUsed
		FROM #tmpCart 
		where success = 1;

		-- return transactions created during this proc
		select transactionID
		from dbo.depoTransactions
		where depoMemberDataID = @depomemberdataID
		and transactionID > @prevTransactionID;

		-- return amount to charge if necessary
		select sum(AmountBilled+salesTaxAmount) as amtOwed
		from dbo.depoTransactions
		where depoMemberDataID = @depomemberdataID
		and transactionID > @prevTransactionID;

		-- remove items from cart
		delete from dbo.documentCart
		where depomemberdataid = @depomemberdataID
		and cartID in (select cartID from #tmpCart where success = 1)
		AND isnull(oneClickPurchaseUID,@defaultUID) = isnull(@oneClickPurchaseUID,@defaultUID);
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpCart') IS NOT NULL
		DROP TABLE #tmpCart;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
