<cfsavecontent variable="local.cleHistoryJS">
	<cfoutput>
	#application.objWebEditor.showEditorHeadScripts()#
	<script language="javascript">
		var #ToScript(local.link.permsGotoLink,"mca_perms_link")#
		function getCredits(postData){
			$('.loadingWrap').removeClass('d-none');
			$('div.creditsWrap').html('');
			$.ajax({
				type : 'post',
				data : postData,
				url : '#local.link.getCreditsByAuthorityOptions#&mode=stream'
			}).done(function(r){
				$('div.creditsWrap').html(r);
				mca_setupSelect2();
				$('.loadingWrap').addClass('d-none');
			}).error(function(err){
				$('div.creditsWrap').html('');
				$('.loadingWrap').addClass('d-none');
			});
		}
		function getSWCredits(postData){
			$('.loadingWrap').removeClass('d-none');
			$('div.swCreditsWrap').html('');
			$.ajax({
				type : 'post',
				data : postData,
				url : '#local.link.getSWCreditsByAuthorityOptions#&mode=stream'
			}).done(function(r){
				$('div.swCreditsWrap').html(r);
				mca_setupSelect2();
				$('.loadingWrap').addClass('d-none');
			}).error(function(err){
				$('div.swCreditsWrap').html('');
				$('.loadingWrap').addClass('d-none');
			});
		}
		function onAuthorityChange(){
			var data = { "clePageId":$('##clePageId').val(), "authID":$('##authority').val() ? $('##authority').val().join(',') : ''};
			getCredits(data);
		}
		function onSWAuthorityChange(){
			var data = { "clePageId":$('##clePageId').val(), "authID":$('##swAuthority').val() ? $('##swAuthority').val().join(',') : ''};
			getSWCredits(data);
		}
		function onEventOptionChange(thisEl){
			var evOpt = $(thisEl).data('ev');
			if($(thisEl).is(":checked")){
				$('.'+evOpt+'Sec').removeClass('d-none');
			} else {
				$('.'+evOpt+'Sec').addClass('d-none');
				$('.'+evOpt+'Sec input').val('');
				$('.'+evOpt+'Sec textarea').val('');
			}
		}
		function validateForm(formObj) {
			var arrReq = new Array();
			mca_hideAlert('err_cle');

			var pageDesc = "";
			if(CKEDITOR.instances['pageDesc'] != null)
				pageDesc = CKEDITOR.instances['pageDesc'].getData().trim();
			else 
				pageDesc = $('textarea[name="pageDesc"]').val().trim();

			if($('##pageTitle').val() == '') arrReq[arrReq.length] = 'Page Title is a required field.';
			if(pageDesc == '') arrReq[arrReq.length] = 'Page Instructions is a required field.';
			if($('##crdEarnDateFrm').val() == '') arrReq[arrReq.length] = 'Credit Earned From is a required field.';
			if($('##crdEarnDateTo').val() == '') arrReq[arrReq.length] = 'Credit Earned To is a required field.';

			arrReq = arrReq.concat(validateRollingDates());
			
			if(arrReq.length){
				mca_showAlert('err_cle', arrReq.join('<br/>'), true);
				return false;
			}

			$('.btnSaveCle').attr('disabled', true);
			return true;
		}

		$(document).ready(function(){
			mca_setupDatePickerField('crdEarnDateFrm');
			mca_setupDatePickerField('crdEarnDateTo');
			mca_setupCalendarIcons('frmCleHistory');

		    rollingDataDefaultValue = {
				"creditFromAdvanceDateAFID" : "#local.qryCLESettings.creditFromAdvanceDateAFID#",
				"creditToAdvanceDateAFID" : "#local.qryCLESettings.creditToAdvanceDateAFID#",
				"advanceDate" : "#DateFormat(local.qryCLESettings.advanceDate,"m/d/yyyy")#",
				"advanceDateAFID" : "#local.qryCLESettings.advanceDateAFID#"
			};

			setTimeout(function(){ $('##successAlert').hide(); },3000);
			
			setTimeout(function(){
				<cfif len(local.qryCLESettings.advanceDate)>
					$('##rolldate1').prop('checked','checked');
					$('##rolldate1').trigger('click');
					$('##roll_crdEarnDateFrm_afid').trigger('change');
					$('##roll_crdEarnDateTo_afid').trigger('change');
					$('##roll_adv_afid').trigger('change');
				<cfelse>
					$('##rolldate0').prop('checked','checked');
				</cfif>
			},1000);
			
			$(document).on('change','.evOption',function(){ onEventOptionChange(this); });

			<cfif len(trim(local.qryCLESettings.authorityIDs))>
				onAuthorityChange();
			</cfif>
			<cfif len(trim(local.qryCLESettings.swAuthorityIDs))>
				onSWAuthorityChange();
			</cfif>

			mca_setupSelect2();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.cleHistoryJS)#">

<cfoutput>
<div id="divCLEHistoryFormContainer">
	<form name="frmCleHistory" id="frmCleHistory" action="#this.link.edit#&clePageId=#local.qryCLESettings.clePageId#" method="POST" onsubmit="return validateForm(this)">
		<input type="hidden" name="clePageId" id="clePageId" value="#local.qryCLESettings.clePageId#">
		<input type="hidden" name="pageAction" value="save">
		<input type="hidden" name="instructionContentID" value="#arguments.event.getValue('instructionContentID')#">
		<input type="hidden" name="eventContentID" value="#arguments.event.getValue('eventContentID')#">
		<input type="hidden" name="swlContentID" value="#arguments.event.getValue('swlContentID')#">
		<input type="hidden" name="swodContentID" value="#arguments.event.getValue('swodContentID')#">
		<input type="hidden" name="storeContentID" value="#arguments.event.getValue('storeContentID')#">

		<div class="row">
			<div class="col">
			<h4>Editing MyCE History - #local.qryCLESettings.pageName#</h4></div>
			
		</div>
	

		<div id="err_cle" class="alert alert-danger mb-3 d-none"></div>
		<div class="accordion mt-3">
			<div id="pageParent" class="card card-box accordion rounded">
				<div class="card-header bg-light">
				<button type="button" class="btn btn-md d-flex align-items-center justify-content-between pl-3 collapsed font-weight-bold" data-toggle="collapse" aria-expanded="false" aria-controls="divMCFSTab_1286" style="cursor:default;">
				<span>Page Settings </span> 
				</button>
				</div>
				<div class="col-auto text-right mt-3">
					<cfif local.saveResult NEQ ''>
						<cfif local.saveResult EQ 'success'>
							<span id="successAlert" class="mr-2 text-green">Data saved successfully.</span>
						<cfelse>
							<span id="errorAlert" class="mr-2 text-danger">#local.saveResult#</span>
						</cfif>
					</cfif>
					<button type="button" class="btn btn-sm btn-secondary" onclick="mca_showPermissions(#local.cleData.SITERESOURCEID#,'#encodeForJavaScript(local.cleData.APPLICATIONINSTANCENAME)#');">Permissions</button>
					<button name="btnSaveCle" id="btnSaveCle" type="submit" class="btn btn-sm btn-primary btnSaveCle">Save</button>
				</div>
				<div id="divPageSetting" class="p-3 " aria-labelledby="pageParent" data-parent="pageParent">
					<div>
						<div class="form-group">
							<div class="form-label-group">
								<input type="text" name="pageTitle" id="pageTitle" value="#replace(arguments.event.getValue('pageTitle'),chr(34),'&quot;','ALL')#" class="form-control" autocomplete="off">				
								<label for="pageTitle">Page Title <span class="text-danger">*</span></label>
							</div>
						</div>
						<div class="form-group row mb-3">
							<div class="col-sm-12">
								#application.objWebEditor.showContentBoxWithLinks(fieldname='pageDesc', fieldlabel='Instructions <span class="text-danger">*</span>', contentID=arguments.event.getValue('instructionContentID'), content=arguments.event.getValue('pageDesc'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
							</div>
						</div>
						<div class="form-group row mb-3">
							<div class="col font-weight-bold">Default Date Filter</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="row no-gutters align-items-center">
									<div class="col-md col-sm-12 pr-md-0">
										<div class="form-label-group">
											<div class="input-group dateFieldHolder">
												<input type="text" name="crdEarnDateFrm" id="crdEarnDateFrm" value="#DateFormat(local.qryCLESettings.dtEarnedFrom,"m/d/yyyy")#" class="form-control dateControl rolldate" mcrdtxt="Credit Earned From" autocomplete="off">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="dateStart"><i class="fa-solid fa-calendar"></i></span>
												</div>
												<label for="crdEarnDateFrm">Credit Earned Start <span class="text-danger">*</span></label>
											</div>
										</div>
									</div>
									<div class="col-md-auto px-md-2">and</div>
									<div class="col-md col-sm-12 pr-md-0">
										<div class="form-label-group">
											<div class="input-group dateFieldHolder">
												<input type="text" name="crdEarnDateTo" id="crdEarnDateTo" value="#DateFormat(local.qryCLESettings.dtEarnedTo,"m/d/yyyy")#" class="form-control dateControl rolldate" mcrdtxt="Credit Earned To" autocomplete="off">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="dateEnd"><i class="fa-solid fa-calendar"></i></span>
												</div>
												<label for="crdEarnDateTo">Credit Earned End <span class="text-danger">*</span></label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group row">
							<div class="col">
								#showStepRollingDates(event=arguments.event)#
							</div>
						</div>
						<div class="form-group row mb-3">
							<div class="col font-weight-bold">Member Display</div>
						</div>
						<div class="mb-1">Field Set for Member Data</div>
						<div class="mb-3">#local.strMemberDataFSSelector.html#</div>
						<div class="form-group">
							<div class="form-label-group">
								<div class="input-group input-group">
									<input type="text" name="justview1" id="justview1" value="/?pg=#local.qryCLESettings.pageName#" class="form-control" readonly="readonly" onclick="this.select();"/>
									<div class="input-group-append">
										<span class="input-group-text"><a target="_blank" href="/?pg=#local.qryCLESettings.pageName#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
									</div>
									<label for="justview1">Internal URL</label>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group">
								<div class="input-group input-group">
									<input type="text" name="justview2" id="justview2" value="#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?pg=#local.qryCLESettings.pageName#" class="form-control" readonly="readonly" onclick="this.select();"/>
									<div class="input-group-append">
										<span class="input-group-text"><a target="_blank" href="/?pg=#local.qryCLESettings.pageName#"><i class="fa-solid fa-up-right-from-square"></i></a></span>
									</div>
									<label for="justview2">External URL</label>
								</div>
							</div>
						</div>
					</div>
				</div>    
			</div>
		</div>
		<!----- second well --->
		<div class="accordion mt-3">
			<div id="mc_section" class="card card-box accordion rounded">
				<div class="card-header bg-light">
				<button type="button" class="btn btn-md d-flex align-items-center justify-content-between pl-3 collapsed font-weight-bold" data-toggle="collapse" aria-expanded="false" aria-controls="divMCFSTab_1286" style="cursor:default;">
				<span>MemberCentral Events and Store</span> 
				</button>
				</div>
				<div id="divMCFSTab_1286" class="p-3 " aria-labelledby="mc_section" data-parent="mc_section">
					<div>
						<div class="form-group row mb-3">
							<div class="col font-weight-bold">Credit Authorities and Types To Display</div>
						</div>
						<div class="form-group">
							<div class="form-label-group">
								<div class="input-group flex-nowrap">
									<select name="authority" id="authority" class="form-control form-control-sm" onchange="onAuthorityChange();" multiple="true" data-toggle="custom-select2" placeholder="Choose One or More Credit Authorities">
										<cfloop query="local.qryAuthorities">
											<option value="#local.qryAuthorities.authorityID#" <cfif ListFindNoCase(local.qryCLESettings.authorityIDs,local.qryAuthorities.authorityID,'|')>selected</cfif>>#local.qryAuthorities.authorityName#</option>
										</cfloop>
									</select>
									<label for="authority">Credit Authorities</label>
								</div>
							</div>
						</div>
						<div class="form-group row loadingWrap d-none">
							<div class="col"><b>Loading...</b></div>
						</div>
						<div class="creditsWrap"></div>
						<div class="form-group row mb-3">
							<div class="col">
								<div class="form-check">
									<input type="checkbox" name="chrShowEvent" id="chrShowEvent" value="1" class="form-check-input evOption" data-ev="event" <cfif local.qryCLESettings.canShowEvents is  1>checked</cfif>>
									<label for="chrShowEvent" class="form-check-label font-weight-bold">Show Events</label>
								</div>
							</div>
						</div>
						<div class="form-group eventSec <cfif local.qryCLESettings.canShowEvents neq 1>d-none</cfif>">
							<div class="form-label-group">
								<input type="text" name="eventSectionTitle" id="eventSectionTitle" value="#replace(arguments.event.getValue('eventSectionTitle'),chr(34),'&quot;','ALL')#" class="form-control" autocomplete="off">			
								<label for="eventSectionTitle">Section Title</label>
							</div>
						</div>
						<div class="form-group row mb-3 eventSec <cfif local.qryCLESettings.canShowEvents neq 1>d-none</cfif>">
							<div class="col-sm-12">
								#application.objWebEditor.showContentBoxWithLinks(fieldname='eventContent', fieldlabel='Section Instructions', contentID=arguments.event.getValue('eventContentID'), content=arguments.event.getValue('eventContent'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
							</div>
						</div>
						<div class="form-group eventSec <cfif local.qryCLESettings.canShowEvents neq 1>d-none</cfif>">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="canShowEventCode" id="canShowEventCode" value="1" class="custom-control-input" <cfif local.qryCLESettings.canShowEventCode is 1>checked</cfif>>
								<label class="custom-control-label" for="canShowEventCode">Include Event Code in Results</label>		
							</div>
						</div>
						<div class="form-group row mt-3 mb-3">
							<div class="col">
								<div class="form-check">
									<input type="checkbox" name="chrShowStore" id="chrShowStore" value="1" class="form-check-input evOption" data-ev="store" <cfif local.qryCLESettings.canShowStore is  1>checked</cfif>>
									<label for="chrShowStore" class="form-check-label font-weight-bold">Show Store</label>
								</div>
							</div>
						</div>
						<div class="form-group storeSec <cfif local.qryCLESettings.canShowStore neq 1>d-none</cfif>">
							<div class="form-label-group">
								<input type="text" name="storeSectionTitle" id="storeSectionTitle" value="#replace(arguments.event.getValue('storeSectionTitle'),chr(34),'&quot;','ALL')#" class="form-control" autocomplete="off">		
								<label for="storeSectionTitle">Section Title</label>
							</div>
						</div>
						<div class="form-group row mb-3 storeSec <cfif local.qryCLESettings.canShowStore neq 1>d-none</cfif>">
							<div class="col-sm-12">
								#application.objWebEditor.showContentBoxWithLinks(fieldname='storeSectionContent', fieldlabel='Section Instructions', contentID=arguments.event.getValue('storeContentID'), content=arguments.event.getValue('storeSectionContent'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
							</div>
						</div>				
					</div>
				</div>    
			</div>
		</div>
		<!---- third well --->
		<cfif local.isSWLParticipant OR local.isSWODParticipant>
			<div class="accordion mt-3">
				<div id="sw_section" class="card card-box accordion rounded">
					<div class="card-header bg-light">
					<button type="button" class="btn btn-md d-flex align-items-center justify-content-between pl-3 collapsed font-weight-bold" data-toggle="collapse" aria-expanded="false" aria-controls="divMCFSTab_1286" style="cursor:default;">
					<span>SeminarWeb On Demand and Live</span> 
					</button>
					</div>
					<div id="divMCFSTab_1286" class="p-3 " aria-labelledby="sw_section" data-parent="sw_section">
						<div>
							<div class="form-group row mb-3">
								<div class="col font-weight-bold">Credit Authorities and Types To Display</div>
							</div>
							<div class="form-group">
								<div class="form-label-group">
									<div class="input-group flex-nowrap">
										<select name="swAuthority" id="swAuthority" class="form-control form-control-sm" onchange="onSWAuthorityChange();" multiple="true" data-toggle="custom-select2" placeholder="Choose One or More Credit Authorities">
											<cfloop query="local.qrySWAuthorities">
												<option value="#local.qrySWAuthorities.CSALinkID#" <cfif ListFindNoCase(local.qryCLESettings.swAuthorityIDs,local.qrySWAuthorities.CSALinkID,'|')>selected</cfif>> #local.qrySWAuthorities.authorityName#</option>
											</cfloop>
										</select>
										<label for="swAuthority">Credit Authorities</label>
									</div>
								</div>
							</div>
							<div class="form-group row loadingWrap d-none">
								<div class="col"><b>Loading...</b></div>
							</div>
							<div class="swCreditsWrap"></div>
							<cfif local.isSWLParticipant>
								<div class="form-group row mt-3">
									<div class="col">
										<div class="form-check">
											<input type="checkbox" name="chrShowSWL" id="chrShowSWL" value="1" class="form-check-input evOption" data-ev="swl" <cfif local.qryCLESettings.canShowSWL is  1>checked</cfif>>
											<label for="chrShowSWL" class="form-check-label font-weight-bold">Show SeminarWeb Live</label>
										</div>
									</div>
								</div>
								<div class="form-group swlSec <cfif local.qryCLESettings.canShowSWL neq 1>d-none</cfif>">
									<div class="form-label-group">
										<input type="text" name="swlSectionTitle" id="swlSectionTitle" value="#replace(arguments.event.getValue('swlSectionTitle'),chr(34),'&quot;','ALL')#" class="form-control" autocomplete="off">
										<label for="swlSectionTitle">Section Title</label>
									</div>
								</div>
								<div class="form-group row mb-3 swlSec <cfif local.qryCLESettings.canShowSWL neq 1>d-none</cfif>">
									<div class="col-sm-12">
										#application.objWebEditor.showContentBoxWithLinks(fieldname='swlSectionContent', fieldlabel='Section Instructions', contentID=arguments.event.getValue('swlContentID'), content=arguments.event.getValue('swlSectionContent'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
									</div>
								</div>
							<cfelse>
								<input type="hidden" name="chrShowSWL" value="0">
								<input type="hidden" name="swlSectionTitle" value="">
								<input type="hidden" name="swlSectionContent" value="">
							</cfif>

							<cfif local.isSWODParticipant>
								<div class="form-group row mt-3 mb-3">
									<div class="col">
										<div class="form-check">
											<input type="checkbox" name="chrShowSWOD" id="chrShowSWOD" value="1" class="form-check-input evOption" data-ev="swod" <cfif local.qryCLESettings.canShowSWOD is  1>checked</cfif>>
											<label for="chrShowSWOD" class="form-check-label font-weight-bold">Show SeminarWeb On Demand</label>
										</div>
									</div>
								</div>
								<div class="form-group swodSec <cfif local.qryCLESettings.canShowSWOD neq 1>d-none</cfif>">
									<div class="form-label-group">
										<input type="text" name="swodSectionTitle" id="swodSectionTitle" value="#replace(arguments.event.getValue('swodSectionTitle'),chr(34),'&quot;','ALL')#" class="form-control" autocomplete="off">
										<label for="swodSectionTitle">Section Title</label>
									</div>
								</div>
								<div class="form-group row mb-3 swodSec <cfif local.qryCLESettings.canShowSWOD neq 1>d-none</cfif>">
									<div class="col-sm-12">
										#application.objWebEditor.showContentBoxWithLinks(fieldname='swodSectionContent', fieldlabel='Section Instructions', contentID=arguments.event.getValue('swodContentID'), content=arguments.event.getValue('swodSectionContent'), allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
									</div>
								</div>
							<cfelse>
								<input type="hidden" name="chrShowSWOD" value="0">
								<input type="hidden" name="swodSectionTitle" value="">
								<input type="hidden" name="swodSectionContent" value="">
							</cfif>
						</div>
					</div>    
				</div>
			</div>
		<cfelse>
			<input type="hidden" name="chrShowSWL" value="0">
			<input type="hidden" name="swlSectionTitle" value="">
			<input type="hidden" name="swlSectionContent" value="">	
			<input type="hidden" name="chrShowSWOD" value="0">
			<input type="hidden" name="swodSectionTitle" value="">
			<input type="hidden" name="swodSectionContent" value="">
		</cfif>	

		<div class="col-auto text-right mt-3">
			<div class="offset-sm-3">
				<button name="btnSaveCle" type="submit" class="btn btn-sm btn-primary btnSaveCle">Save Changes</button>
			</div>
		</div>

		<div class="form-group row mt-3">
			<div class="col">
				<div class="alert alert-info mb-0">
					<table class="table table-sm table-borderless">
						<tr><td colspan="2"><b>Note</b>: CSS properties can be applied to page title, sub title text, and buttons from Website CSS using following classes:</td></tr>
						<tr><td width="110"><b>clePageTitle</b></td><td>for managing Page Title</td></tr>
						<tr><td><b>cleSubHeader</b></td><td>for managing Subtitle Text</td></tr>
						<tr><td><b>cleClearBtn</b></td><td>for managing Date Filter Clear Text</td></tr>
						<tr><td><b>cleButton</b></td><td>for managing Buttons</td></tr>
					</table>
				</div>
			</div>
		</div>
	</form>
</div>
</cfoutput>