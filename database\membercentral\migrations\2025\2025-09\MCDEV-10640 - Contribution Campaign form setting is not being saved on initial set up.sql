USE membercentral
GO

ALTER PROC dbo.cp_createCampaign
@siteID int,
@programID int,
@campaignName varchar(400),
@status char(1),
@isHidden bit,
@intakeFormTitle varchar(400),
@confirmationEmailSubjectLine varchar(400),
@intakeFormInstContent varchar(max),
@intakeFormNonQualContent varchar(max),
@startDate date,
@endDate date,
@goalAmount decimal(18,2),
@distribIDList varchar(max),
@allowSetStartDate bit,
@skipMemberLookup bit,
@newAcctFieldsetID int,
@newAcctFormTitle varchar(400),
@showProfLicensesForNewAcct bit,
@enteredByMemberID int,
@campaignID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @appCreatedContentResourceTypeID int, @defaultLanguageID int, @intakeFormInstContentID int, @intakeFormNonQualContentID int, @trashID int;

	set @campaignID = null;
	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
	select @defaultLanguageID = defaultLanguageID from dbo.sites where siteID = @siteID;

	IF EXISTS (select top 1 campaignID from dbo.cp_campaigns where programID = @programID and campaignName = @campaignName)
		RAISERROR('Campaign Name already in use', 16, 1);

	BEGIN TRAN
		
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent=@intakeFormInstContent, 
			@memberID=@enteredByMemberID, @contentID=@intakeFormInstContentID OUTPUT, @siteResourceID=@trashID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent=@intakeFormNonQualContent, 
			@memberID=@enteredByMemberID, @contentID=@intakeFormNonQualContentID OUTPUT, @siteResourceID=@trashID OUTPUT;


		-- add campaign
		insert into dbo.cp_campaigns (programID, campaignName, [status], isHidden, allowSetStartDate, intakeFormTitle, confirmationEmailSubjectLine, intakeFormInstContentID, intakeFormNonQualContentID, startDate, endDate,
			goalAmount, skipMemberLookup, newAcctFieldsetID, newAcctFormTitle, showProfLicensesForNewAcct)
		values (@programID, @campaignName, @status, @isHidden, @allowSetStartDate, @intakeFormTitle, @confirmationEmailSubjectLine, @intakeFormInstContentID, @intakeFormNonQualContentID, @startDate, @endDate,
			@goalAmount, @skipMemberLookup, NULLIF(@newAcctFieldsetID,0), NULLIF(@newAcctFormTitle,''), @showProfLicensesForNewAcct);
		select @campaignID = SCOPE_IDENTITY();

		IF ISNULL(@distribIDList,'') <> ''
			insert into dbo.cp_campaignDistributions (campaignID, distribID)
			select @campaignID, listitem
			from dbo.fn_intListToTable(@distribIDList,',');
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
