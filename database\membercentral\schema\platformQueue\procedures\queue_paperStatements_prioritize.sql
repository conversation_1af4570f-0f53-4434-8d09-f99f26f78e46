CREATE PROC dbo.queue_paperStatements_prioritize

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = getdate();;
	EXEC dbo.queue_getQueueTypeID @queueType='paperStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpQueueItemsPriority') IS NOT NULL 
		DROP TABLE #tmpQueueItemsPriority;
	CREATE TABLE #tmpQueueItemsPriority (itemID int, totalQueued int, minutesInQueue int, priority int);

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	INSERT INTO #tmpQueueItemsPriority (itemID, totalQueued, minutesInQueue)
	SELECT itemID, COUNT(detailID), minsInQueue = datediff(minute,MIN(dateUpdated),@nowDate)
	FROM dbo.queue_paperStatementsDetail
	WHERE queueStatusID in (@statusReady,@statusGrabbed)
	GROUP BY itemID;

	UPDATE temp 
	SET priority = 
			CASE 
				WHEN totalQueued = 1 THEN -100
				WHEN minutesInQueue > 360 THEN (totalQueued / 50) + 1
				WHEN minutesInQueue > 90 THEN (totalQueued / 25) - 10
				WHEN minutesInQueue > 30 THEN (totalQueued / 25) + 2
				WHEN totalQueued < 500 THEN (totalQueued / 25)
				ELSE (totalQueued / 25) + 10
            END
	FROM #tmpQueueItemsPriority as temp;

	 -- delete rows where the priority hasn't changed
	delete temp
	from #tmpQueueItemsPriority as temp
	inner join dbo.queue_paperStatements as qid on temp.itemID = qid.itemID
	where temp.priority = qid.queuePriority;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- update queuePriority
	UPDATE qid
	SET qid.queuePriority = temp.priority
	FROM dbo.queue_paperStatements as qid
	INNER JOIN #tmpQueueItemsPriority as temp on temp.itemID = qid.itemID;

	IF OBJECT_ID('tempdb..#tmpQueueItemsPriority') IS NOT NULL 
		DROP TABLE #tmpQueueItemsPriority;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
