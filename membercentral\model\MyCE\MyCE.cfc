<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = "controller">
	<cfset variables.applicationReservedURLParams = "ra,panel">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		local.dataStruct = StructNew();
		local.dataStruct.orgCode = arguments.event.getValue('mc_siteInfo.orgCode');
		local.dataStruct.orgID = arguments.event.getValue('mc_siteinfo.orgID');
		local.dataStruct.siteCode = arguments.event.getValue('mc_siteinfo.siteCode');
		local.dataStruct.siteID = arguments.event.getValue('mc_siteinfo.siteID');
		local.dataStruct.thisAction = arguments.event.getValue('panel','clehistory');
		local.dataStruct.isFilter = arguments.event.getValue('btnSubmit','');
		
		// superuser and memberkey support
		if (application.objUser.isSuperUser(cfcuser=session.cfcuser))
			local.memberIDToUse = session.cfcuser.memberdata.memberID;
		else
			local.memberIDToUse = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=local.dataStruct.orgID);

		// super/site admins can lookup other members by membernumber
		local.dataStruct.allowMemberNumberLookup = application.objUser.isSiteAdmin(cfcuser=session.cfcuser) OR application.objUser.isSuperUser(cfcuser=session.cfcuser);

		// membernumber lookup support
		if (local.dataStruct.allowMemberNumberLookup and arguments.event.getTrimValue('membernumber','') neq '')
			local.memberIDToUse = application.objMember.getMemberIDByMemberNumber(memberNumber=arguments.event.getTrimValue('membernumber'), orgID=local.dataStruct.orgID);

		// if not logged in, no memberkey, and no membernumber
		if (NOT local.memberIDToUse)
			application.objCommon.redirect('/?pg=login');
				
		local.viewToUse = "echo";
		local.viewData = '';
		local.dataStruct.baseURL = "/?#getBaseQueryString(false)#";
		local.dataStruct.today = now();
		</cfscript>
		
		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
		</cfif>
		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory')>
		
		<cfswitch expression="#local.dataStruct.thisAction#">			
			<cfcase value="viewEVCert">
				<cfscript>
					// get encrypted registrantid
					local.encryptedRID = arguments.event.getValue('rid','');
					
					// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
					try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
					catch (any e) { local.decryptedRID = 0; }
				
					// generate certificate for registrantID
					local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
				</cfscript>
				<!--- redirect to pdf --->
				<cfif len(local.strCertificate.certificateURL)>
					<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
				<cfelse>
					<cflocation url="#local.dataStruct.baseURL#&panel=certErr&mode=direct" addtoken="no">
				</cfif>
			</cfcase>
			<cfcase value="viewStoreCert">
				<cfscript>
					// get encrypted affirmationID
					local.encryptedAID = arguments.event.getValue('aid','');
					
					// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
					try { local.decryptedAID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedAID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
					catch (any e) { local.decryptedAID = 0; }
					
					// generate certificate for registrantID
					local.strCertificate = CreateObject("component","model.admin.store.certificate").generateCertificate(affirmationID=local.decryptedAID);
				</cfscript>
				<!--- redirect to pdf --->
				<cfif len(local.strCertificate.certificateURL)>
					<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
				<cfelse>
					<cflocation url="#local.dataStruct.baseURL#&panel=certErr&mode=direct" addtoken="no">
				</cfif>
			</cfcase>
			<cfcase value="certErr">
				<cfset local.viewToUse = "myCE/#local.viewDirectory#/dsp_certificateErr">
			</cfcase>
			<cfcase value="clehistory">
				<cfset local.dataStruct.memberData = getMemberData(orgID=local.dataStruct.orgID, memberID=local.memberIDToUse)>
				<cfset local.dataStruct.cleData = getCLESettings(siteID=local.dataStruct.siteID)>
				
				<cfquery name="local.dataStruct.qryAuthorities" datasource="#application.dsn.membercentral.dsn#">
					SELECT distinct ca.authorityID, ca.authorityName
					FROM dbo.cms_myCEPageCreditTypes as pct
					INNER JOIN dbo.crd_authoritySponsorTypes as ast on ast.ASTID = pct.ASTID
					INNER JOIN dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
					INNER JOIN dbo.crd_authorities as ca on ca.authorityID = cat.authorityID
					WHERE pct.clePageId = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.dataStruct.cleData.clePageId#">
					ORDER BY ca.authorityName
				</cfquery>
				<cfquery name="local.dataStruct.qrySWAuthorities" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					SELECT distinct pct.csaLinkID, ca.authorityID, ca.authorityName
					FROM membercentral.dbo.cms_myCEPageCreditTypesSW as pct
					INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = pct.CSALinkID
					INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
					INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID
					WHERE pct.clePageId = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.dataStruct.cleData.clePageId#">
					ORDER BY ca.authorityName
				</cfquery>
				<cfset local.dataStruct.mcAID = arguments.event.getTrimValue('mcAID','')>
				<cfset local.dataStruct.swAID = arguments.event.getTrimValue('swAID','')>
				<cfset local.dataStruct.periodStartDate = arguments.event.getTrimValue('periodStartDate',local.dataStruct.cleData.dtEarnedFrom)>
				<cfset local.dataStruct.periodEndDate = arguments.event.getTrimValue('periodEndDate',local.dataStruct.cleData.dtEarnedTo)>

				<cfset local.dataStruct.fieldSetUID  = ''>
				<cfset local.dataStruct.fieldSetData  = ''>
				<cfif local.dataStruct.cleData.mainFieldSetId gt 0>
					<cfset local.dataStruct.fieldSetUID = getFieldSetUIDByID(fieldsetID=local.dataStruct.cleData.mainFieldSetId)>
					<cfset local.dataStruct.fieldSetData = getFieldSetDataByID(orgID=local.dataStruct.orgID, memberID=local.dataStruct.memberData.memberid, 
						membernumber=local.dataStruct.memberData.membernumber, fieldsetID=local.dataStruct.cleData.mainFieldSetId)>
					
					<cfif local.dataStruct.fieldSetData.recordCount>
						<cfset local.dataStruct.xmlResultFields = local.dataStruct.fieldSetData.mc_outputFieldsXML[1]>
						<cfset local.dataStruct.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.dataStruct.xmlResultFields)>

						<!--- remove fields from qryOutputFields that are handled manually --->
						<cfquery name="local.dataStruct.qryOutputFieldsForLoop" dbtype="query">
							SELECT *
							FROM [local].dataStruct.qryOutputFields
							WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
						</cfquery>
								
						<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=local.dataStruct.orgID, includeTags=1)>
						<cfset local.dataStruct.strOrgAddressTypes = structNew()>
						<cfloop query="local.orgAddressTypes">
							<cfif local.orgAddressTypes.isTag is 1>
								<cfset local.dataStruct.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
							<cfelse>
								<cfset local.dataStruct.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
							</cfif>
						</cfloop>
					</cfif>
				</cfif>
				
				<cfif local.dataStruct.cleData.canShowEvents eq 1>				
					<cfset local.dataStruct.strEvents = getEventCredits(orgID=local.dataStruct.orgID, siteID=local.dataStruct.siteID, 
						timezoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'), 
						clePageID=local.dataStruct.cleData.clePageId, memberID=local.dataStruct.memberData.memberID, authorityIDList=local.dataStruct.mcAID, 
						startDate=local.dataStruct.periodStartDate, endDate=local.dataStruct.periodEndDate)>
				</cfif>

				<cfif local.dataStruct.cleData.canShowStore eq 1>				
					<cfset local.dataStruct.strStore = getStoreCredits(orgID=local.dataStruct.orgID, siteID=local.dataStruct.siteID, 
						clePageID=local.dataStruct.cleData.clePageId, memberID=local.dataStruct.memberData.memberID, authorityIDList=local.dataStruct.mcAID, 
						startDate=local.dataStruct.periodStartDate, endDate=local.dataStruct.periodEndDate)>
				</cfif>

				<cfif local.dataStruct.cleData.canShowSWL eq 1 OR local.dataStruct.cleData.canShowSWOD eq 1>
					<cfset local.strSeminars = getSeminarWebCredits(siteID=local.dataStruct.siteID, siteCode=local.dataStruct.siteCode, 
						clePageID=local.dataStruct.cleData.clePageId, memberID=local.dataStruct.memberData.memberID, authorityIDList=local.dataStruct.swAID, 
						startDate=local.dataStruct.periodStartDate, endDate=local.dataStruct.periodEndDate)>
					<cfif local.dataStruct.cleData.canShowSWL eq 1 and StructKeyExists(local.strSeminars, "arrSWL")>
						<cfset local.dataStruct.strSWL = { arrSeminars=local.strSeminars.arrSWL, qryAllCredits=local.strSeminars.qryAllSWLCredits, qryCETotals=local.strSeminars.qryCETotalsSWL }>
					</cfif>
					<cfif local.dataStruct.cleData.canShowSWOD eq 1 and StructKeyExists(local.strSeminars, "arrSWOD")>
						<cfset local.dataStruct.strSWOD = { arrSeminars=local.strSeminars.arrSWOD, qryAllCredits=local.strSeminars.qryAllSWODCredits, qryCETotals=local.strSeminars.qryCETotalsSWOD }>
					</cfif>
				</cfif>

				<cfset local.viewToUse = "myCE/#local.viewDirectory#/frm_cleHistory">
			</cfcase>
		</cfswitch>
		
		<cfif local.viewToUse EQ 'echo'>
			<cfreturn returnAppStruct(local.viewData,"echo")>
		<cfelse>
			<cfreturn returnAppStruct(local.dataStruct,local.viewToUse)>
		</cfif>
	</cffunction>

	<cffunction name="getEventCredits" access="private" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="timezoneID" type="numeric" required="true">
		<cfargument name="clePageID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="authorityIDList" type="string" required="true">
		<cfargument name="startDate" type="date" required="true">
		<cfargument name="endDate" type="date" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = StructNew()>		

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCE">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @clePageID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.clePageID#">, 
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">, 
				@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">, 
				@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">, 
				@timeZoneID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.timezoneID#">, 
				@startDate date = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.startDate#">, 
				@endDate datetime = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.endDate# 23:59:59.997">;
				
			IF OBJECT_ID('tempdb..##tmpAuthorityTypes') IS NOT NULL 
				DROP TABLE ##tmpAuthorityTypes;
			IF OBJECT_ID('tempdb..##tmpCertParentEvents') IS NOT NULL 
				DROP TABLE ##tmpCertParentEvents;
			IF OBJECT_ID('tempdb..##tmpCertRegEvents') IS NOT NULL 
				DROP TABLE ##tmpCertRegEvents;
			CREATE TABLE ##tmpAuthorityTypes (authorityID int, authorityName varchar(200), ASTID int, typeName varchar(255));
			CREATE TABLE ##tmpCertParentEvents (eventID int, certificateID int);
			CREATE TABLE ##tmpCertRegEvents (registrantID int, certificateID int);

			-- authorities/types defined by page, filtered by passed in authority if provided
			INSERT INTO ##tmpAuthorityTypes (authorityID, authorityName, ASTID, typeName)
			select ca.authorityID, ca.authorityName, pct.ASTID, isnull(ast.ovTypeName,cat.typeName)
			from dbo.cms_myCEPageCreditTypes as pct
			inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = pct.ASTID
			inner join dbo.crd_authoritySponsors as cas on cas.ASID = ast.ASID
			inner join dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
			inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
			where pct.clePageID = @clePageID
			<cfif listLen(arguments.authorityIDList)>
				and ca.authorityID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.authorityIDList#" list="true">)
			</cfif>;

			-- look for certificate offerings
			INSERT INTO ##tmpCertParentEvents (eventID, certificateID)
			select subEvent.parentEventID, max(c.certificateID) as certificateID
			from dbo.ev_registrants as r
			inner join dbo.ev_registration rn on rn.registrationID = r.registrationID and rn.siteID = @siteID and rn.status = 'A'
			inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1
			inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
			inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
			inner join dbo.crd_certificates as c on c.certificateID = ecast.LiveApprovedCertificateID
			inner join dbo.ev_subEvents as subEvent on subEvent.eventID = rn.eventID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = r.memberID and m.activeMemberID = @memberID
			where r.status = 'A'
			and r.attended = 1
			group by subEvent.parentEventID;
			
			INSERT INTO ##tmpCertRegEvents (registrantID, certificateID)
			select r.registrantID, max(c.certificateID) as certificateID
			from dbo.ev_registrants as r
			inner join dbo.ev_registration rn on rn.registrationID = r.registrationID and rn.siteID = @siteID and rn.status = 'A'
			inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1
			inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
			inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
			inner join dbo.crd_certificates as c on c.certificateID = ecast.LiveApprovedCertificateID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = r.memberID and m.activeMemberID = @memberID
			where r.status = 'A'
			and r.attended = 1
			group by r.registrantID;

			-- registrants and their awarded credits
			SELECT r.registrantID, e.eventid, et.startTime as eventStart, cl.contentTitle as eventTitle,
				tat.authorityID, tat.authorityName, tat.typeName, rc.creditValueAwarded, datepart(yyyy,et.startTime) as CLEYear,
				case when certs.certificateID is not null or certs2.certificateID is not null then 1 else 0 end as showCert, e.reportCode
			FROM dbo.ev_registrants as r
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = r.memberID	
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID AND m2.memberID = @memberID
			INNER JOIN dbo.ev_registration as evr on evr.registrationID = r.registrationID and evr.siteID = @siteID and evr.status = 'A'
			INNER JOIN dbo.ev_events as e on e.siteID = @siteID and e.eventid = evr.eventid
			INNER JOIN dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = @timeZoneID
				AND et.startTime between @startDate and @endDate
			INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID AND cl.languageID = 1
			INNER JOIN dbo.crd_requests as rc on rc.registrantID = r.registrantID AND rc.creditAwarded = 1
			INNER JOIN dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
			INNER JOIN ##tmpAuthorityTypes as tat on tat.ASTID = ect.ASTID
			LEFT OUTER JOIN ##tmpCertRegEvents as certs on certs.registrantID = r.registrantID
			LEFT OUTER JOIN ##tmpCertParentEvents certs2 on certs2.eventID = e.eventID
			WHERE r.[status] = 'A'
			AND r.attended = 1
			ORDER BY et.startTime desc, e.eventid, tat.authorityName, tat.typeName;

			IF OBJECT_ID('tempdb..##tmpAuthorityTypes') IS NOT NULL 
				DROP TABLE ##tmpAuthorityTypes;
			IF OBJECT_ID('tempdb..##tmpCertParentEvents') IS NOT NULL 
				DROP TABLE ##tmpCertParentEvents;
			IF OBJECT_ID('tempdb..##tmpCertRegEvents') IS NOT NULL 
				DROP TABLE ##tmpCertRegEvents;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfquery name="local.qryCETotals" dbtype="query">
			SELECT CLEYear, typeName, authorityName, sum(creditValueAwarded) as totalCLE
			from [local].qryCE
			GROUP BY CLEYear, typeName, authorityName 
			ORDER BY CLEYear desc, authorityName, typeName
		</cfquery>

		<cfset local.strReturn = { qryCETotals=local.qryCETotals, qryCE=local.qryCE }>

		<cfreturn local.strReturn>		
	</cffunction>	

	<cffunction name="getStoreCredits" access="private" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="clePageID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="authorityIDList" type="string" required="true">
		<cfargument name="startDate" type="date" required="true">
		<cfargument name="endDate" type="date" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = StructNew()>		

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCE">
			SET NOCOUNT ON;

			declare @clePageID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.clePageID#">, 
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">, 
				@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">, 
				@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">, 
				@startDate date = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.startDate#">, 
				@endDate datetime = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.endDate# 23:59:59.997">, 
				@storeID int;

			select @storeID = s.storeID
			from dbo.store s
			inner join dbo.cms_applicationInstances ai on ai.siteID = @siteID and ai.applicationInstanceID = s.applicationInstanceID
			inner join dbo.cms_siteResources sr on sr.siteID = @siteID and sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			where s.siteID = @siteID;

			IF OBJECT_ID('tempdb..##tmpAuthorityTypes') IS NOT NULL 
				DROP TABLE ##tmpAuthorityTypes;
			CREATE TABLE ##tmpAuthorityTypes (authorityID int, authorityName varchar(200), ASTID int, typeName varchar(255));

			-- authorities/types defined by page, filtered by passed in authority if provided
			INSERT INTO ##tmpAuthorityTypes (authorityID, authorityName, ASTID, typeName)
			select ca.authorityID, ca.authorityName, pct.ASTID, isnull(ast.ovTypeName,cat.typeName)
			from dbo.cms_myCEPageCreditTypes as pct
			inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = pct.ASTID
			inner join dbo.crd_authoritySponsors as cas on cas.ASID = ast.ASID
			inner join dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
			inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
			where pct.clePageID = @clePageID
			<cfif listLen(arguments.authorityIDList)>
				and ca.authorityID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.authorityIDList#" list="true">)
			</cfif>;

			-- affirmations and their awarded credits
			SELECT aff.affirmationid, aff.dateClaimed, cl.contentTitle as productTitle, so.orderNumber,
				tat.authorityID, tat.authorityName, tat.typeName, rc.creditValueAwarded, datepart(yyyy,aff.dateClaimed) as CLEYear
			FROM dbo.crd_affirmations as aff
			INNER JOIN dbo.crd_affirmationTypes as afft on afft.affirmationTypeID = aff.affirmationTypeID and afft.affirmationType = 'paper'
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = aff.assignToMemberID	
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID AND m2.memberID = @memberID
			INNER JOIN dbo.store_productFormats as spf on spf.formatID = aff.assignToFormatID
			INNER JOIN dbo.store_products as sp on sp.itemid = spf.itemid and sp.storeID = @storeID
			INNER JOIN dbo.store_orders as so on so.orderid = aff.orderid and so.storeID = @storeID
			INNER JOIN dbo.cms_contentLanguages as cl on cl.contentid = sp.productContentID and cl.languageID = 1
			INNER JOIN dbo.crd_requests as rc on rc.affirmationID = aff.affirmationID AND rc.creditAwarded = 1
			INNER JOIN dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
			INNER JOIN ##tmpAuthorityTypes as tat on tat.ASTID = ect.ASTID
			WHERE aff.[status] = 'A'
			AND aff.dateClaimed between @startDate and @endDate
			ORDER BY aff.dateClaimed desc, aff.affirmationid, tat.authorityName, tat.typeName;

			IF OBJECT_ID('tempdb..##tmpAuthorityTypes') IS NOT NULL 
				DROP TABLE ##tmpAuthorityTypes;
		</cfquery>
		
		<cfquery name="local.qryCETotals" dbtype="query">
			SELECT CLEYear, typeName, authorityName, sum(creditValueAwarded) as totalCLE
			from [local].qryCE
			GROUP BY CLEYear, typeName, authorityName 
			ORDER BY CLEYear desc, authorityName, typeName
		</cfquery>

		<cfset local.strReturn = { qryCETotals=local.qryCETotals, qryCE=local.qryCE }>

		<cfreturn local.strReturn>	
	</cffunction>

	<cffunction name="getSeminarWebCredits" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="clePageID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="authorityIDList" type="string" required="true">
		<cfargument name="startDate" type="date" required="true">
		<cfargument name="endDate" type="date" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = {
			"arrSWL":[], 
			"arrSWOD":[],
			"qryAllSWLCredits":QueryNew(''),
			"qryAllSWODCredits":QueryNew(''),
			"qryCETotalsSWL":QueryNew(''),
			"qryCETotalsSWOD":QueryNew('')
		}>

		<cfstoredproc procedure="ams_getTLASITESDepoMemberDataIDByMemberID" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.depoMemberDataID">
		</cfstoredproc>

		<cfif local.depoMemberDataID is 0>
			<cfreturn local.strReturn>
		</cfif>

		<cfset local.strAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.siteCode)>
		<cfif NOT structKeyExists(local.strAssociation, "qryAssociation") OR (local.strAssociation.qryAssociation.isSWL EQ 0 AND local.strAssociation.qryAssociation.isSWOD EQ 0)>
			<cfreturn local.strReturn>
		</cfif>

		<cfset local.strHistory = CreateObject("component","model.semwebcatalog.semwebcatalog").getMyHistory(siteID=arguments.siteID, catalogOrgCode=arguments.siteCode, 
			memberID=arguments.memberID, depoMemberDataID=local.depoMemberDataID, qrySWP=local.strAssociation.qryAssociation, 
			fStartDate='', fEndDate='', fStartDateCompleted=arguments.startDate, fEndDateCompleted=arguments.endDate)>

		<!--- SW doesnt use the same credit system as MC, so here we need to limit to only those authorities and types defined in the myCE app by their exact names (ug) --->
		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryAppAuthorityTypes">
			SET NOCOUNT ON;

			declare @clePageID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.clePageID#">;

			-- authorities/types defined by page, filtered by passed in authority if provided
			SELECT ca.authorityName + '...' + CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') AS AuthorityType,
				CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') as creditType, 
				convert(varchar,pct.CSALinkID,30)+'_'+pct.creditType as selected
			FROM membercentral.dbo.cms_myCEPageCreditTypesSW as pct
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON pct.CSALinkID = csa.CSALinkID
			INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID	
			CROSS APPLY ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') AS CRDT(credittype)
			WHERE CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') = pct.creditType
			AND pct.clePageID = @clePageID
			<cfif listLen(arguments.authorityIDList)>
				and pct.CSALinkID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.authorityIDList#" list="true">)
			</cfif>;
		</cfquery>
		<cfset var delim = "~~">
		<cfset var SWAuthorityTypeList = ValueList(local.qryAppAuthorityTypes.selected,delim)>
		<cfif structKeyExists(local.strHistory,"qryAllEnrollmentCredits") AND local.strHistory.qryAllEnrollmentCredits.recordCount>
			<cfset local.qryAllEnrollmentCredits = queryFilter(local.strHistory.qryAllEnrollmentCredits, function(row) { 
				return ListFindNoCase(SWAuthorityTypeList,trim(arguments.row.creditedTypes),delim);
			})>
			<cfset var AllEnrollmentCreditsEnrollmentIDList = local.qryAllEnrollmentCredits.recordCount ? ValueList(local.qryAllEnrollmentCredits.enrollmentID) : "">

			<!--- Filter the SWL and SWOD array for items that have credits awarded for the defined types --->
			<cfset local.strReturn.arrSWL = arrayFilter(local.strHistory.arrSWL, function(item){ 
				return len(arguments.item.dspCredits) AND listFind(AllEnrollmentCreditsEnrollmentIDList,arguments.item.enrollmentID);
			})>
			<cfset local.strReturn.arrSWOD = arrayFilter(local.strHistory.arrSWOD, function(item){ 
				return len(arguments.item.dspCredits) AND listFind(AllEnrollmentCreditsEnrollmentIDList,arguments.item.enrollmentID);
			})>

			<!--- Get the enrollmentIDs based on the filtered seminars --->
			<cfset var SWLenrollmentIDList = arrayReduce(local.strReturn.arrSWL, function(prev,item) { return listAppend(arguments.prev,arguments.item.enrollmentID); }, "")>
			<cfset var SWODenrollmentIDList = arrayReduce(local.strReturn.arrSWOD, function(prev,item) { return listAppend(arguments.prev,arguments.item.enrollmentID); }, "")>

			<!--- Filter the all credits query to only those enrollments we are showing --->
			<cfset local.strReturn.qryAllSWLCredits = queryFilter(local.qryAllEnrollmentCredits, function(row) { return listFind(SWLenrollmentIDList,arguments.row.enrollmentID); })>
			<cfset local.strReturn.qryAllSWODCredits = queryFilter(local.qryAllEnrollmentCredits, function(row) { return listFind(SWODenrollmentIDList,arguments.row.enrollmentID); })>

			<!--- get totals by format --->
			<cfquery name="local.strReturn.qryCETotalsSWL" dbtype="query">
				SELECT CLEYear, creditType, authorityName, sum(creditValueAwarded) as totalCLE
				from [local].strReturn.qryAllSWLCredits
				GROUP BY CLEYear, creditType, authorityName 
				ORDER BY CLEYear desc, authorityName, creditType
			</cfquery>
			<cfquery name="local.strReturn.qryCETotalsSWOD" dbtype="query">
				SELECT CLEYear, creditType, authorityName, sum(creditValueAwarded) as totalCLE
				from [local].strReturn.qryAllSWODCredits
				GROUP BY CLEYear, creditType, authorityName 
				ORDER BY CLEYear desc, authorityName, creditType
			</cfquery>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getMemberData" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		
		<cfset var qryMemberData = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryMemberData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

			select m.memberID, m.firstName, m.middleName, m.lastName, m.suffix, m.professionalSuffix, m.company, m.membernumber, me.email
			from dbo.ams_members as m 
			inner join dbo.ams_memberEmails as me on me.orgID = @orgID
				and m.memberID = me.memberID
			inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
				and metag.memberID = me.memberID
				and metag.emailTypeID = me.emailTypeID
			inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
				and metagt.emailTagTypeID = metag.emailTagTypeID
				and metagt.emailTagType = 'Primary'
			where m.orgID = @orgID
			and m.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
			and m.status <> 'D';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- if recordcount is 0, because the user is a superuser with no account for that org. set memberID to 0 --->
		<cfif qryMemberData.recordcount is 0 and queryAddRow(qryMemberData)>
			<cfset querySetCell(qryMemberData, "memberID", 0)>
		</cfif>

		<cfreturn qryMemberData>
	</cffunction>	

	<cffunction name="getCLESettings" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var qryCLESettings = "">

		<cfquery name="qryCLESettings" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT ai.applicationInstanceName, ai.applicationInstanceDesc, pg.pageName, instructionContent.contentTitle as pageTitle, instructionContent.rawContent as pageDesc,
				pg.pageID, cp.clePageId, cp.applicationInstanceID, convert(VARCHAR,cp.dtEarnedFrom,101) as [dtEarnedFrom], convert(VARCHAR,cp.dtEarnedTo,101) as [dtEarnedTo],
				cp.canShowEvents, cp.canShowEventCode, eventContent.contentTitle as eventSectionTitle, eventContent.rawContent as eventSectionDesc,
				cp.canShowSWL, swlContent.contentTitle as SWLSectionTitle, swlContent.rawContent as SWLSectionDesc, cp.canShowSWOD,
				swodContent.contentTitle as SWODSectionTitle, swodContent.rawContent as SWODSectionDesc, cp.canShowStore,
				storeContent.contentTitle as StoreSectionTitle, storeContent.rawContent as StoreSectionDesc, cp.mainFieldSetId
			FROM dbo.cms_applicationTypes at 
			INNER JOIN dbo.cms_applicationInstances ai ON ai.siteID = @siteID 
				and ai.applicationTypeID = at.applicationTypeID
				and ai.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#this.appInstanceID#">
			INNER JOIN dbo.cms_myCEPages cp ON cp.applicationInstanceID = ai.applicationInstanceID
			INNER JOIN dbo.cms_pages pg ON pg.siteID = @siteID and pg.pageID = cp.pageID 
			INNER JOIN dbo.cms_pageLanguages cpl on cpl.pageID = pg.pageID and cpl.languageID = 1
			cross apply dbo.fn_getContent(cp.instructionContentID,1) as instructionContent
			cross apply dbo.fn_getContent(cp.eventContentID,1) as eventContent
			cross apply dbo.fn_getContent(cp.swlContentID,1) as swlContent
			cross apply dbo.fn_getContent(cp.swodContentID,1) as swodContent
			cross apply dbo.fn_getContent(cp.storeContentID,1) as storeContent;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryCLESettings>
	</cffunction>

	<cffunction name="getFieldSetDataByID" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="membernumber" type="string" required="true">		
		<cfargument name="fieldSetID" type="numeric" required="true">
		
		<cfset var qryFieldSetData = "">

		<cfquery name="qryFieldSetData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
				DROP TABLE ##tmp_membersForFS;
			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
				DROP TABLE ##tmpMembers;
			CREATE TABLE ##tmp_membersForFS (memberid int, memberNumber varchar(50));
			CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);

			DECLARE @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">,
				@fieldsetID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldsetID#">,
				@outputFieldsXML xml;
			
			INSERT INTO ##tmp_membersForFS (memberID, memberNumber)
			VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">, <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.membernumber#">);
			
			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
					@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;
			
			SELECT top 1 *, @outputFieldsXML AS mc_outputFieldsXML
			FROM ##tmpMembers;

			IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
				DROP TABLE ##tmp_membersForFS;
			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
				DROP TABLE ##tmpMembers;
		</cfquery>

		<cfreturn qryFieldSetData>			
	</cffunction>

	<cffunction name="getFieldSetUIDByID" access="private" output="false" returntype="string">
		<cfargument name="fieldSetID" type="numeric" required="true">
		
		<cfset var qryFieldSet = "">

		<cfquery name="qryFieldSet" datasource="#application.dsn.membercentral.dsn#">
			select uid 
			FROM dbo.ams_memberFieldSets
			where fieldsetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldSetID#">
		</cfquery>

		<cfreturn qryFieldSet.uid>
	</cffunction>

	<cffunction name="showAppInstanceForm" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="appInfo" type="query">

		<cfset var local = structNew()>

		<cfset local.appInfo = arguments.appInfo>
		<cfset local.allow = FALSE>
		<cfset variables.isMultiInstanceReady = XMLSearch(local.appInfo.settingsXML,"string(//setting[@name='isMultiInstanceReady']/@value)")/>
		<cfif local.appInfo.instancesCreated EQ 0>
			<cfset local.allow = TRUE>
		<cfelse>
			<cfif variables.isMultiInstanceReady AND (local.appInfo.maxInstancesPerSite - local.appInfo.instancesCreated) GT 0>
				<cfset local.allow = TRUE>
			</cfif>
		</cfif>

		<cfif local.allow>
			<cfscript>				
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			local.objSection = CreateObject("component","model.system.platform.section");
			local.objPageAdmin = CreateObject("component","model.admin.pages.pageAdmin");
			local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryModes = local.objPageAdmin.getAvailableModes();
			local.qryLanguages = local.objPageAdmin.getAvailableLanguages();
			if( local.appInfo.recordCount ) variables.allowPageNameChange = local.appInfo.allowPageNameChange;
			</cfscript>
			
			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">	
					function setDuplicateMessage(boxEl, messageEl, iconEl, success, message){
						iconEl.toggleClass('fa-circle-check', success).toggleClass('fa-circle-exclamation', !success);
						messageEl.html(message);
						boxEl.toggleClass('text-green', success).toggleClass('text-danger', !success).removeClass('d-none');
					}
					function doesPageExist(pageName) {
						var boxEl = $('##pageBox');
						var messageEl = $('##pageText');
						var iconEl = $('##pageImg');
						var re = /[^a-zA-Z0-9\-_]/;
						mca_hideAlert('err_createapp');

						var existsResult = function(r) {
							if (r.success && r.success.toLowerCase() == 'true'){
								setDuplicateMessage(boxEl, messageEl, iconEl, !r.pageexists, r.pageexists ? 'Page Name already used!' : 'Passed!');
							} else {
								boxEl.addClass('d-none');
								mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
							}
						};

						if (pageName.length > 0) {
							if(re.test(pageName)){
								setDuplicateMessage(boxEl, messageEl, iconEl, false, 'Only letters, numbers, underscores, and dashses are allowed');
							}
							else {
								checkPageExists(pageName,existsResult);
							}
						}
						else {
							boxEl.addClass('d-none');
							return false;
						}
					}
					function checkPageExists(pageName,callback) {
						var objParams = { pageID:#val(arguments.event.getValue('pageID',0))#, pageName:pageName };
						TS_AJX('PAGE','pageExists',objParams,callback,callback,10000,callback);
					}				
					function validatePageForm() {
						toggleFinishButton(false);
						mca_hideAlert('err_createapp');
						var thisForm = document.forms["frmCreateApp"];	
						var arrPromises = [];					
						var arrReq = new Array();

						if($('##pageName').length && $.trim($('##pageName').val()).length == 0 && $('##pageName').is(':visible')) {
							arrReq[arrReq.length]	= 'Enter a valid name for this page.';
						}
						if($('##pageTitle').length && $.trim($('##pageTitle').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a page title.';
						}
						if($('##pageDesc').length && $.trim($('##pageDesc').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a page description.';
						}
						if($('##appInstanceName').length && $.trim($('##appInstanceName').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a name for this #local.appInfo.applicationTypeDesc#.';
						}
						if($('##appInstanceDesc').length && $.trim($('##appInstanceDesc').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a description for this #local.appInfo.applicationTypeDesc#.';
						}
						
						if (arrReq.length > 0) {							
							mca_showAlert('err_createapp', arrReq.join('<br/>'), true);
							toggleFinishButton(true);
							return false;
						}
						<cfif variables.allowPageNameChange>
							arrPromises.push(
								new Promise(function(resolve, reject) {
									var checkPageNameResult = function(r) {
										if (r.success && r.success.toLowerCase() == 'true'){
											if(r.pageexists == true) {
												setDuplicateMessage($('##pageBox'), $('##pageText'), $('##pageImg'), false, 'Page Name already used!');
												mca_showAlert('err_createapp', 'Page Name already used.', true);
												toggleFinishButton(true);
												reject();
											}
											else resolve();
										} else {
											mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
											toggleFinishButton(true);
											reject();
										}
									};
									if($.trim($('##pageName').val()).length) {
										checkPageExists($.trim($('##pageName').val()),checkPageNameResult);
									}
									else resolve();
								})
							);	
						</cfif>						
						Promise.all(arrPromises).then(function(){
							thisForm.submit();
						}).catch((error) => {
							return false;
						});			
						return false;
					}					
				</script>			
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
			
			<cfoutput>
			<div class="mt-4">
				<div id="err_createapp" class="alert alert-danger mb-4 mt-2 d-none"></div>
				<cfform action="/?#cgi.QUERY_STRING#" method="POST" name="frmCreateApp" id="frmCreateApp" onsubmit="return validatePageForm();">
					<cfinput type="hidden" name="lid"  id="lid" value="#arguments.event.getValue('lid')#">
					<cfinput type="hidden" name="pageTemplateID"  id="pageTemplateID" value="#arguments.event.getValue('pageTemplateID')#">
					<cfinput type="hidden" name="allowReturnAfterLogin"  id="allowReturnAfterLogin" value="#arguments.event.getValue('allowReturnAfterLogin')#">

					<cfif len(trim(arguments.event.getValue('error.errorMessage')))>
						<div class="alert alert-danger mb-2">
							Correct the following errors:<br/>
							<cfloop list="#arguments.event.getValue('error.errorMessage')#" delimiters="|" index="local.currentMessage">
								- #local.currentMessage#<br />
							</cfloop>
						</div>
					</cfif>	

					<div>
						<cfif variables.allowPageNameChange>
							<div class="form-row">
								<div class="col">
									<div class="form-label-group">
										<input type="text" name="pageName" id="pageName" class="form-control" autocomplete="off" value="#arguments.event.getValue('pageName')#" onblur="doesPageExist(this.value);" maxlength="50">                      
										<label for="pageName">Page Name <span class="text-danger">*</span></label>
									</div>
									<div id="pageBox" class="form-text small mb-2 d-none">
										<i class="fa-solid" id="pageImg"></i> <span id="pageText"></span>
									</div>
								</div>
							</div>
						<cfelse>
							<div class="form-group row">
								<label for="pageName" class="col-sm-4 col-form-label-sm font-size-md">Page Name <span class="text-danger">*</span>:</label>
								<div class="col-sm-7">
									#local.appInfo.suggestedPageName# <cfinput type="hidden" name="pageName"  id="pageName" value="#local.appInfo.suggestedPageName#">
								</div>
							</div>
						</cfif>
						<div class="form-group" >
							<div class="form-label-group">
								<select name="sectionID" id="sectionID" class="form-control">
									<cfloop query="local.getSections">
										<option value="#local.getSections.sectionID#"<cfif arguments.event.getValue('sectionID') EQ local.getSections.sectionID> SELECTED</cfif>>#local.getSections.thePathExpanded#</option>
									</cfloop>
								</select>
								<label for="sectionID">Section</label>
							</div>
						</div>	
						<div class="form-group" >
							<div class="form-label-group">
								<select name="pageModeID" id="pageModeID" class="form-control">
									<option value="0">No Override</option>
									<cfloop query="local.qryModes">
										<option value="#local.qryModes.modeID#"<cfif arguments.event.getValue('pageModeID') EQ local.qryModes.modeID> SELECTED</cfif>>#local.qryModes.modeName#</option>
									</cfloop>
								</select>
								<label for="pageModeID">Mode Override</label>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-label-group">
									<input type="text" name="pageTitle" id="pageTitle" class="form-control" autocomplete="off" value="#arguments.event.getValue('pageTitle')#">                      
									<label for="pageTitle">Page Title <span class="text-danger">*</span></label>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group">
								<textarea name="pageDesc" id="pageDesc" class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('pageDesc')#</textarea>
								<label for="pageDesc">Page Description <span class="text-danger">*</span></label>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-label-group">
									<input type="text" name="appInstanceName" id="appInstanceName" class="form-control" autocomplete="off" value="#arguments.event.getValue('appInstanceName')#">                      
									<label for="appInstanceName">#local.appInfo.applicationTypeDesc# Name <span class="text-danger">*</span></label>
								</div>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-label-group">
									<textarea name="appInstanceDesc" id="appInstanceDesc" class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('appInstanceDesc')#</textarea>
									<label for="appInstanceDesc">#local.appInfo.applicationTypeDesc# Description <span class="text-danger">*</span></label>
								</div>
							</div>
						</div>
						<div class="form-group text-right">
							<button type="submit" name="btnSaveEventDetails" id="btnSaveEventDetails" class="btn btn-sm btn-primary d-none">Save Information</button>
						</div>
					</div>
				</cfform>	
			</div>
			</cfoutput>
		<cfelse>
			<cfoutput>
			<div class="alert alert-warning">
				<h4>Unable to add #local.appInfo.applicationTypeName#</h4>
				<p>You may not add this application to your website at this time.</p>
			</div>	
			</cfoutput>
		</cfif>
	</cffunction>

	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">
		<cfargument name="returnToAppAdmin" type="boolean" required="false" default="0">

		<cfscript>
			var local = structNew();		
			// SET EVENT SPECIFICATION ----------------------------------------------		
			arguments.event.paramValue('appTypeID','0');
			// LOAD OBJECTS ---------------------------------------------------------
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			//queries ---------------------------------------------------------------
			local.appInfo = arguments.appInfo;
			// call the contruct to do all the page validation and form params ------			
			contructAppInstanceForm(arguments.event,local.appInfo);			
		</cfscript>
		
		<cfif cgi.request_method eq "POST" AND NOT arguments.event.getValue('error.formErrors')>		
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
					SET nocount ON

					DECLARE 
				      	@siteID	int,
				      	@languageID int,
				      	@sectionID int,
				      	@isVisible bit,
				      	@pageName varchar(50), 
				      	@pageTitle varchar(200),
				      	@pagedesc varchar(400),
				      	@zoneID int,
				      	@pageTemplateID int,
				      	@pageModeID int,
				      	@pgResourceTypeID int,
				      	@allowReturnAfterLogin bit,
				      	@applicationInstanceName varchar(100),
				      	@applicationInstanceDesc varchar(200),	      	
				      	@applicationInstanceID int, 
				      	@siteResourceID int, 
				      	@pageID int,
						@clePageID int;
							
					SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					SET @languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('lid')#">;
					SET @isVisible = 1;
					SELECT @pageName = dbo.fn_regexReplace(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageName')#">,'[^A-Z0-9\-]+','');
					SET @pageTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageTitle')#">;
					SET @pagedesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageDesc')#">;
					SELECT @zoneID = dbo.fn_getZoneID('Main');
					SET @pageTemplateID = NULL;
					SET @pageModeID = <cfif arguments.event.getValue('pageModeID','0') EQ 0>NULL<cfelse><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('pageModeID')#"></cfif>;
					SET @allowReturnAfterLogin = 1;
					SET @applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceName')#">;
					SET @applicationInstanceDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceDesc')#">;
					SET @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('sectionID')#">;
				    SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('MyCE');
				      
					EXEC dbo.cms_createApplicationInstanceMyCE @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
						@isVisible=@isVisible, @pageName=@pageName, @pageTitle=@pageTitle, @pagedesc=@pagedesc, @zoneID=@zoneID,
						@pageTemplateID=@pageTemplateID, @pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID,
						@allowReturnAfterLogin=@allowReturnAfterLogin, @applicationInstanceName=@applicationInstanceName,
						@applicationInstanceDesc=@applicationInstanceDesc, @applicationInstanceID=@applicationInstanceID OUTPUT,
						@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT, @clePageID=@clePageID OUTPUT;

					select @applicationInstanceID as applicationInstanceID, @siteResourceID as siteResourceID, @pageID as pageID, @clePageID as clePageID;
				</cfquery>	
				<cfset local.message = 1>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>					
					<cfset local.message = 2>
				</cfcatch>
			</cftry>	
			<cfset application.objSiteInfo.triggerClusterWideReload()>
			<cfif arguments.returnToAppAdmin>
				<cfoutput>
					<script language="javascript">
						top.reloadMyCEHistoryTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			<cfelse>
				<cfoutput>
					<script language="javascript">
						top.reloadPageTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>		
			</cfif>	 
		<cfelse>
			<cfoutput>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getMode">
					select dbo.fn_getmodeId('Full') as pageModeID
				</cfquery>
				<cfset arguments.event.setValue('pageModeID',local.getMode.pageModeID)>
				<cfset showAppInstanceForm(arguments.event,local.appInfo)>
			</cfoutput>
		</cfif>		
	</cffunction>
</cfcomponent>