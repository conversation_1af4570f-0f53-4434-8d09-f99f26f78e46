<cfcomponent extends="model.admin.admin" output="no">
	<cfscript>
		defaultEvent = 'controller';
		variables.appResourceType = 'MyCE';
		variables.instanceSettings = structNew();
	</cfscript>
	
	<cffunction name="init" access="public" returntype="void" output="false">
		<cfset variables.instanceSettings = getInstanceSettings(this.appInstanceID)>
	</cffunction>

	<cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// Load Objects for the page ---------------------------------------------------------------- ::
			// init variables
			if( ListFindNoCase('home,message',arguments.event.getValue('mca_ta')) ){ }
			else{
				this.cleData = getResourceData(siteID=arguments.event.getValue('mc_siteInfo.siteID'), clePageId=arguments.event.getValue('clePageId',0));
				this.appInstanceID = this.cleData.applicationInstanceID;
			}
			
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
			
			// Build Quick Links for SectionAdmin ------------------------------------------------------- ::
			this.link.home = buildCurrentLink(arguments.event,"home");
			this.link.message = buildCurrentLink(arguments.event,"message");
			this.link.edit	= buildCurrentLink(arguments.event,"editMyCE");
			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			this.languageID	= getDefaultLanguageID(arguments.event.getValue('mc_siteInfo.siteID'));
			
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="home" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.MyCELink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=MyCEJSON&meth=getMyCE&mode=stream";
			local.appTypeID = application.objApplications.getApplicationTypeIDFromName('MyCE');
			local.addpageLink = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='PageAdmin',mca_ta='addPage');
			local.canAddInstance = CreateObject("component","model.admin.pages.appCreationProcess").canAddAppInstance(siteID=arguments.event.getValue('mc_siteInfo.siteID'), applicationTypeID=local.appTypeID);
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_MyCE.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="editMyCE" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			arguments.event.paramValue('clePageId',0);

			if (val(this.appInstanceID) is 0)
				application.objCommon.redirect("#this.link.message#&message=1");

			local.rc = arguments.event.getCollection();
			local.saveSettings = buildCurrentLink(arguments.event,"saveSettings");	
			
			local.saveResult = '';
			local.cleData = this.cleData;
			local.link.getCreditsByAuthorityOptions = buildCurrentLink(arguments.event,"getCreditsByAuthorityOptions");
			local.link.getSWCreditsByAuthorityOptions = buildCurrentLink(arguments.event,"getSWCreditsByAuthorityOptions");
			local.link.permsGotoLink = super.buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';

			if (StructKeyExists(local.rc ,'pageAction')) {
				if(local.rc.pageAction eq 'save')
					local.saveResult = this.saveSettings(event = arguments.event).data;
			}
			local.qryCLESettings = getSettings(event=arguments.event);

			arguments.event.setValue('instructionContentID',local.qryCLESettings.instructionContentID);
			arguments.event.setValue('pageTitle',local.qryCLESettings.pageTitle);
			arguments.event.setValue('pageDesc',local.qryCLESettings.pageDesc);
			arguments.event.setValue('eventContentID',local.qryCLESettings.eventContentID);
			arguments.event.setValue('eventSectionTitle',local.qryCLESettings.eventSectionTitle);
			arguments.event.setValue('eventContent',local.qryCLESettings.eventContent);
			arguments.event.setValue('swlContentID',local.qryCLESettings.swlContentID);
			arguments.event.setValue('swlSectionTitle',local.qryCLESettings.swlSectionTitle);
			arguments.event.setValue('swlSectionContent',local.qryCLESettings.swlSectionContent);
			arguments.event.setValue('swodContentID',local.qryCLESettings.swodContentID);
			arguments.event.setValue('swodSectionTitle',local.qryCLESettings.swodSectionTitle);
			arguments.event.setValue('swodSectionContent',local.qryCLESettings.swodSectionContent);
			arguments.event.setValue('storeContentID',local.qryCLESettings.storeContentID);
			arguments.event.setValue('storeSectionTitle',local.qryCLESettings.storeSectionTitle);
			arguments.event.setValue('storeSectionContent',local.qryCLESettings.storeSectionContent);

			local.strMemberDataFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="MainFieldSet", selectedValue=val(local.qryCLESettings.mainFieldSetID));
			
			local.strAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgCode=local.rc.mc_siteInfo.siteCode);
			if (local.strAssociation.keyExists("qryAssociation")) {
				local.qrySWP = local.strAssociation.qryAssociation;
				local.isSWLParticipant = local.qrySWP.isSWL is 1;
				local.isSWODParticipant = local.qrySWP.isSWOD is 1;
			} else {
				local.isSWLParticipant = false;
				local.isSWODParticipant = false;
			}
			
			appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML(local.cleData.pageName) });
		</cfscript>

		<cfquery name="local.qryAuthorities" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @sponsorId INT;
			SELECT TOP 1 @sponsorId = sponsorID FROM dbo.crd_sponsors WHERE orgId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgId')#">;

			SELECT ca.authorityID, ca.authorityName
			FROM dbo.crd_authorities AS ca
			INNER JOIN dbo.crd_authoritySponsors AS cas ON cas.authorityID = ca.authorityID AND cas.sponsorID = @sponsorId
			ORDER BY ca.authorityName;
		</cfquery>

		<cfquery name="local.qrySWAuthorities" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select csa.CSALinkID, ca.authorityName
			from dbo.tblCreditAuthorities as ca
			inner join dbo.tblCreditSponsorsAndAuthorities as csa on csa.authorityID = ca.authorityID
			inner join dbo.tblCreditSponsors as cs on cs.sponsorID = csa.sponsorID
			where cs.orgCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteInfo.siteCode')#">
			order by ca.authorityName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_cleHistory.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSettings" access="public" output="true" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = structNew()>
		
		<cftry>
			<cfquery name="local.qrySaveCLE" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @clePageID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('clePageId')#">,
						@languageID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#this.languageID#">,
						@memberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberid#">;

					BEGIN TRAN
				
						UPDATE dbo.cms_myCEPages
						SET	dtEarnedFrom = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('crdEarnDateFrm')#">,
							dtEarnedTo = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('crdEarnDateTo')#">,
							canShowEvents = <cfif arguments.event.valueExists('chrShowEvent')>1<cfelse>0</cfif>,
							canShowEventCode = <cfqueryparam cfsqltype="cf_sql_bit" value="#int(val(arguments.event.getTrimValue('canShowEventCode',0)))#"> ,
							canShowSWL = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getTrimValue('chrShowSWL',0)#">,
							canShowSWOD = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getTrimValue('chrShowSWOD',0)#">,
							canShowStore = <cfif arguments.event.valueExists('chrShowStore')>1<cfelse>0</cfif>,
							mainFieldSetID = <cfif arguments.event.getTrimValue('MainFieldSet',0) gt 0><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('MainFieldSet')#"><cfelse>NULL</CFIF>,
							creditFromAdvanceDateAFID = 
								<cfif arguments.event.valueExists('rolldate') AND arguments.event.getTrimValue('rolldate') IS 1 AND arguments.event.getTrimValue('roll_crdEarnDateFrm_afid',0) GT 0>
									<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('roll_crdEarnDateFrm_afid')#">
								<cfelse>
									null
								</cfif>,
							creditToAdvanceDateAFID = 
								<cfif arguments.event.valueExists('rolldate') AND arguments.event.getTrimValue('rolldate') IS 1 AND arguments.event.getTrimValue('roll_crdEarnDateTo_afid',0) GT 0>
									<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('roll_crdEarnDateTo_afid')#">
								<cfelse>
									null
								</cfif>,
							advanceDate = 
								<cfif arguments.event.valueExists('rolldate') AND arguments.event.getTrimValue('rolldate') IS 1 AND len(arguments.event.getTrimValue('roll_adv',''))>
									<cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('roll_adv')#">
								<cfelse>
									null
								</cfif>,
							advanceDateAFID = 
								<cfif arguments.event.valueExists('rolldate') AND arguments.event.getTrimValue('rolldate') IS 1 AND arguments.event.getTrimValue('roll_adv_afid',0) GT 0>
									<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('roll_adv_afid')#">
								<cfelse>
									null
								</cfif>
						WHERE clePageId = @clePageID;

						DELETE FROM dbo.cms_myCEPageCreditTypes 
						WHERE clePageId = @clePageID;
				
						<cfif ListLen(arguments.event.getTrimValue('authority',''))>
							<cfloop list="#arguments.event.getTrimValue('authority')#" index="local.authorityID">
								<cfif len(arguments.event.getTrimValue('creditsSelected_#local.authorityID#',''))>
									INSERT INTO dbo.cms_myCEPageCreditTypes (clePageId, ASTID) 
									SELECT @clePageID, listItem
									FROM dbo.fn_IntListToTable(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('creditsSelected_#local.authorityID#')#">,',')
									WHERE listItem is not null;
								</cfif>
							</cfloop>
						</cfif>

						DELETE FROM dbo.cms_myCEPageCreditTypesSW 
						WHERE clePageId = @clePageID;
				
						<cfif ListLen(arguments.event.getTrimValue('swAuthority',''))>
							<cfloop list="#arguments.event.getTrimValue('swAuthority')#" index="local.authorityID">
								<cfif len(arguments.event.getTrimValue('swcreditsSelected_#local.authorityID#',''))>
									<cfset local.delim = "~">
									<cfset local.thisSWCreditsSelected = replace(replace(arguments.event.getTrimValue('swcreditsSelected_#local.authorityID#'),',',local.delim,'all'),chr(7),',','all')>
									
									INSERT INTO dbo.cms_myCEPageCreditTypesSW (clePageId, CSALinkID, creditType) 
									SELECT @clePageID, LEFT(listItem, CHARINDEX('_', listItem) - 1), RIGHT(listItem, LEN(listItem) - CHARINDEX('_', listItem))
									FROM dbo.fn_varcharListToTable(<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.thisSWCreditsSelected#">,'#local.delim#')
									WHERE listItem IS NOT NULL;
								</cfif>
							</cfloop>
						</cfif>

						EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('instructionContentID',0)#">, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="cf_sql_varchar" value="#replace(arguments.event.getTrimValue('pageTitle'),'&quot;',chr(34),'ALL')#">, @contentDesc='',
							@rawContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getTrimValue('pageDesc')#">,
							@memberID=@memberID;
						EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eventContentID',0)#">, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="cf_sql_varchar" value="#replace(arguments.event.getTrimValue('eventSectionTitle'),'&quot;',chr(34),'ALL')#">, @contentDesc='',
							@rawContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getTrimValue('eventContent')#">,
							@memberID=@memberID;
						EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('swlContentID',0)#">, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="cf_sql_varchar" value="#replace(arguments.event.getTrimValue('swlSectionTitle'),'&quot;',chr(34),'ALL')#">, @contentDesc='',
							@rawContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getTrimValue('swlSectionContent')#">,
							@memberID=@memberID;
						EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('swodContentID',0)#">, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="cf_sql_varchar" value="#replace(arguments.event.getTrimValue('swodSectionTitle'),'&quot;',chr(34),'ALL')#">, @contentDesc='',
							@rawContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getTrimValue('swodSectionContent')#">,
							@memberID=@memberID;
						EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('storeContentID',0)#">, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="cf_sql_varchar" value="#replace(arguments.event.getTrimValue('storeSectionTitle'),'&quot;',chr(34),'ALL')#">, @contentDesc='',
							@rawContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getTrimValue('storeSectionContent')#">,
							@memberID=@memberID;
					COMMIT TRAN

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.data = 'success'>
		
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.data = 'Error Saving My CLE Settings'>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSettings" access="private" output="true" returntype="query">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryObjCLE" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @clePageID INT = <cfqueryparam cfsqltype="cf_sql_integer" value="#this.cleData.CLEPAGEID#">,
				@languageID INT = <cfqueryparam cfsqltype="cf_sql_integer" value="#this.languageID#">;

			SELECT ai.applicationInstanceName, ai.applicationInstanceDesc, cmsp.pageName, cmsp.pageID, p.clePageId,
				p.applicationInstanceID, convert(VARCHAR,p.dtEarnedFrom,101) AS [dtEarnedFrom], convert(VARCHAR,p.dtEarnedTo,101) AS [dtEarnedTo],
				p.canShowEvents, p.canShowEventCode, p.canShowSWL, p.canShowSWOD, p.canShowStore, p.creditFromAdvanceDateAFID,
				p.creditToAdvanceDateAFID, convert(VARCHAR,p.advanceDate,101) AS [advanceDate], p.advanceDateAFID, p.mainFieldSetID,
				(
					SELECT STRING_AGG(authorityID,'|')
					FROM (
						SELECT distinct cat.authorityID
						FROM dbo.cms_myCEPageCreditTypes AS pct
						INNER JOIN dbo.crd_authoritySponsorTypes AS ast ON ast.ASTID = pct.ASTID
						INNER JOIN dbo.crd_authorityTypes AS cat ON cat.typeID = ast.typeID
						WHERE pct.clePageId = p.clePageId
					) as tmp
				) AS authorityIDs,
				(
					SELECT STRING_AGG(CSALinkID,'|')
					FROM (
						SELECT distinct swct.CSALinkID
						FROM dbo.cms_myCEPageCreditTypesSW AS swct	
						WHERE swct.clePageId = p.clePageId
					) as tmp
				) AS swAuthorityIDs,
				p.instructionContentID, instructionContent.contentTitle as pageTitle, instructionContent.rawContent as pageDesc,
				p.eventContentID, eventContent.contentTitle as eventSectionTitle, eventContent.rawContent as eventContent,
				p.swlContentID, swlContent.contentTitle as swlSectionTitle, swlContent.rawContent as swlSectionContent,
				p.swodContentID, swodContent.contentTitle as swodSectionTitle, swodContent.rawContent as swodSectionContent,
				p.storeContentID, storeContent.contentTitle as storeSectionTitle, storeContent.rawContent as storeSectionContent
			FROM dbo.cms_myCEPages AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID
			INNER JOIN dbo.cms_pages AS cmsp ON cmsp.pageID = p.pageID
			INNER JOIN dbo.cms_pageLanguages AS pl ON pl.pageID = p.pageID and pl.languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#">
			cross apply dbo.fn_getContent(p.instructionContentID,@languageID) as instructionContent
			cross apply dbo.fn_getContent(p.eventContentID,@languageID) as eventContent
			cross apply dbo.fn_getContent(p.swlContentID,@languageID) as swlContent
			cross apply dbo.fn_getContent(p.swodContentID,@languageID) as swodContent
			cross apply dbo.fn_getContent(p.storeContentID,@languageID) as storeContent
			WHERE p.clePageId = @clePageID;
		</cfquery>
		
		<cfreturn local.qryObjCLE>
	</cffunction>

	<cffunction name="getResourceData" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="clePageId" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			SET NOCOUNT ON; 
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID INT, @clePageId INT;

			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteid#">;
			SET @clePageId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.clePageId#">;			

			SELECT ai.applicationInstanceID, ai.applicationInstanceName, sr.siteResourceID, sr.siteResourceStatusID AS srStatusID,
				srs.siteResourceStatusDesc AS srsStatus, cmsp.pageName, cmsp.pageID, p.clePageId
			FROM dbo.cms_myCEPages AS p
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = p.applicationInstanceID AND ai.siteID = @siteID
			INNER JOIN dbo.cms_siteResources AS sr ON ai.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = sr.parentSiteResourceID
			INNER JOIN dbo.cms_pages AS cmsp ON cmsp.pageID = p.pageID
			INNER JOIN dbo.cms_pageLanguages AS pl ON pl.pageID = p.pageID and pl.languageID = 1
			WHERE sr.siteResourceStatusID = 1 and p.clePageId = @clePageId;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.cleData = structNew()>
		<cfloop list="#local.data.columnList#" index="local.thisField">
			<cfset local.cleData[local.thisField] = local.data[local.thisField]>
		</cfloop>
		
		<cfreturn local.cleData>
	</cffunction>

	<cffunction name="showStepRollingDates" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="title" type="string" required="no" default="Rolling Date Support">
		<cfargument name="desc" type="string" required="no" default="">
	
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_rollingDates.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getCreditsByAuthorityOptions" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfif listLen(arguments.event.getTrimValue('authID',''))>
			<cfquery name="local.qryPossibleTypes" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @sponsorId INT;
				SELECT TOP 1 @sponsorId = sponsorID FROM dbo.crd_sponsors WHERE orgId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgId')#">;

				SELECT ca.authorityID, ca.authorityName, ast.ASTID, ISNULL(ast.ovTypeName,cat.typeName) AS typeName
				FROM dbo.crd_authorities AS ca
				INNER JOIN dbo.crd_authoritySponsors AS cas ON cas.authorityID = ca.authorityID and cas.sponsorID = @sponsorId
				INNER JOIN dbo.crd_authoritySponsorTypes AS ast ON ast.ASID = cas.ASID
				INNER JOIN dbo.crd_authorityTypes AS cat ON cat.typeID = ast.typeID
				WHERE ca.authorityID in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.event.getTrimValue('authID',0)#">)
				ORDER BY ca.authorityName;
			</cfquery>

			<cfquery name="local.qrySelectedTypes" datasource="#application.dsn.membercentral.dsn#">
				SELECT ASTID
				FROM dbo.cms_myCEPageCreditTypes
				WHERE clePageId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('clePageId',0)#">
			</cfquery>
			<cfset local.qrySelectedTypesList = valueList(local.qrySelectedTypes.ASTID)>
			
			<cfsavecontent variable="local.data">
				<cfoutput query="local.qryPossibleTypes" group="authorityID">
					<div class="form-group">
						<div class="form-label-group">
							<div class="input-group flex-nowrap">
								<select name="creditsSelected_#local.qryPossibleTypes.authorityID#" id="creditsSelected_#local.qryPossibleTypes.authorityID#" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Select Credit Types">
									<cfoutput>
										<option value="#local.qryPossibleTypes.ASTID#" <cfif listFind(local.qrySelectedTypesList,local.qryPossibleTypes.ASTID)>selected</cfif>>#local.qryPossibleTypes.typeName#</option>
									</cfoutput>
								</select>
								<label for="creditsSelected_#local.qryPossibleTypes.authorityID#">Limit credit types for <i>#local.qryPossibleTypes.authorityName#</i></label>
							</div>
						</div>
					</div>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "">
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getSWCreditsByAuthorityOptions" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfif listLen(arguments.event.getTrimValue('authID',''))>
			<cfquery name="local.qryPossibleTypes" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SELECT csa.CSALinkID, ca.authorityName, ca.authorityID, 
					CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') AS creditType,
					convert(varchar,csa.CSALinkID,30)+'_'+CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') as possibleTypes
				FROM dbo.tblCreditSponsorsAndAuthorities AS csa 
				INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID	
				CROSS APPLY ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') AS CRDT(credittype)
				WHERE csa.CSALinkID in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.event.getTrimValue('authID',0)#">)
				ORDER BY ca.jurisdiction, ca.authorityName, cs.sponsorName
			</cfquery>

			<cfquery name="local.qrySelectedTypes" datasource="#application.dsn.membercentral.dsn#">
				SELECT CSALinkID, creditType, convert(varchar,CSALinkID,30)+'_'+creditType as selected
				FROM dbo.cms_myCEPageCreditTypesSW
				WHERE clePageId = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('clePageId',0)#">
			</cfquery>
			<cfset local.delim = "~">
			<cfset local.qrySelectedTypesList = valueList(local.qrySelectedTypes.selected,local.delim)>
			
			<cfsavecontent variable="local.data">
				<cfoutput query="local.qryPossibleTypes" group="authorityID">
					<div class="form-group">
						<div class="form-label-group">
							<div class="input-group flex-nowrap">
								<select name="swCreditsSelected_#local.qryPossibleTypes.CSALinkID#" id="swCreditsSelected_#local.qryPossibleTypes.CSALinkID#" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Select Credit Types">
									<cfoutput>
										<option value="#local.qryPossibleTypes.CSALinkID#_#replace(local.qryPossibleTypes.creditType,',',chr(7),'all')#" <cfif listFindNoCase(local.qrySelectedTypesList,trim(local.qryPossibleTypes.possibleTypes),local.delim)>selected</cfif>>#local.qryPossibleTypes.creditType#</option>
									</cfoutput>
								</select>
								<label for="swCreditsSelected_#local.qryPossibleTypes.authorityID#">Limit credit types for <i>#local.qryPossibleTypes.authorityName#</i></label>
							</div>
						</div>
					</div>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "">
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Get Messages for Application--->
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this page.</b></cfcase>
							<cfdefaultcase>
								<b>There was a problem displaying the information.</b><br/><br/>
								If you continue to see this error, contact Support for further assistance.
							</cfdefaultcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getDefaultLanguageID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySiteInfo">
			SELECT defaultLanguageID
			FROM dbo.sites
			WHERE siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>
		<cfreturn local.qrySiteInfo.defaultLanguageID>
	</cffunction>

</cfcomponent>