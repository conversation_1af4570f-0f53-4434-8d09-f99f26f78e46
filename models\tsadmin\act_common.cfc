<cfcomponent displayname="Common methods used in the TrialSmith Admin">
	<cffunction name="redirect" access="public" displayname="Redirects user to a new page">
		<cfargument name="urlToGoTo" type="string" required="Yes">
		<cflocation url="#arguments.urlToGoTo#" addtoken="No">
	</cffunction>

	<cffunction name="getMemberData" returntype="query" displayName="Returns member data based on a depoMemberDataID" output="No">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">

		<cfset var qryMemberData = "">
		
		<cfquery name="qryMemberData" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT d.depomemberdataID, d.<PERSON>, d<PERSON>, d<PERSON>, d<PERSON>, d<PERSON>, d.<PERSON>, d.<PERSON>, d<PERSON>, 
				d.<PERSON>, d<PERSON>, d<PERSON>, d.<PERSON>ddress2, d.BillingAddress3, d.BillingCity, d.BillingState, d.BillingZip, d.RenewalDate, d.LastName, 
				d.FirstName, d.MiddleName, d.suffix, d.Phone, d.Fax, d.Email, d.MailCode, d.TLAMemberState, d.membertype, d.RegCode, d.viewerflag, d.optOutTSMarketing,
				d.faxflag, d.signuporgcode, d.orgMemberDataIdTemp, d.TSAllowed, d.includeInSubscriberDepos, d.paymenttype, d.linksource, d.linkterms, 
				d.ts_LastUpdate, d.billingCountry, c.country as billingCountryName, d.juryResearchEnabled, d.DepoContactFirstName, d.DepoContactLastName,
				d.DepoContactEmail, d.DepoContactPhone, d.DepoContactDate, d.BillingContactName, d.BillingContactEmail, d.isDocumentChatEnabled
			FROM dbo.depomemberdata d
			LEFT OUTER JOIN dbo.countries c on c.countryid = d.billingcountry
			WHERE d.depomemberdataID = <cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMemberData>
	</cffunction>

	<cffunction name="checkBillingZIP" returntype="boolean" output="No">
		<cfargument name="billingState" type="string" required="yes">
		<cfargument name="billingZip" type="string" required="yes">

		<cfset var qryCheckZIP = "">

		<cfquery name="qryCheckZIP" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT zipCode
			FROM dbo.avalaraTaxTable
			WHERE [state] = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.billingState#"> 
			AND zipCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.BillingZip#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCheckZIP.recordCount is 1>
	</cffunction>

	<cffunction name="getMemberDataMCInfo" returntype="query" output="No">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">

		<cfset var qryMCMemberInfo = "">

		<cfquery name="qryMCMemberInfo" datasource="#application.settings.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT memberID, dbo.fn_getSiteHostNameBySiteCode('TS','#application.settings.environment#') as hostName
			FROM dbo.ams_members
			WHERE orgID = 1
			AND memberNumber = <cfqueryparam cfsqltype="cf_sql_varchar" value="TSDEPOID_#arguments.depomemberdataid#"> 
			AND status <> 'D';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMCMemberInfo>
	</cffunction>

	<cffunction name="getAccountCodes" returntype="query" displayName="Returns the account codes" output="No">
		<cfset var qryAccountCodes = "">
		
		<cfquery name="qryAccountCodes" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#CreateTimeSpan(1,0,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT acctcode, description
			FROM dbo.depoDocumentTypes
			WHERE acctcode is not null
				union
			SELECT acctcode, membertype
			FROM dbo.membertype
			WHERE acctcode is not null
			ORDER BY acctcode;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryAccountCodes>
	</cffunction>

	<cffunction name="getMemberTypePriceGroups" returntype="query" displayName="Returns the member type price groups" output="No">
		<cfset var qryPriceGroups = "">
		
		<cfquery name="qryPriceGroups" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#CreateTimeSpan(0,1,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT pricegroupID, priceGroupName, priceGroupDesc, orderBy, isExpertGroup = 
				CASE WHEN EXISTS (SELECT mtpg.priceGroupID 
								FROM dbo.membertypePriceGroups as mtpg
								INNER JOIN dbo.depoTLA AS tla ON mtpg.priceGroupID = tla.PriceGroupID
								WHERE tla.state = 'EX'
								AND mtpg.priceGroupID = memberTypePriceGroups.pricegroupID) THEN 1
				ELSE 0
				END
			FROM dbo.memberTypePriceGroups
			ORDER BY orderBy;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryPriceGroups>
	</cffunction>

	<cffunction name="getMemberTypePriceGroup" returntype="query" displayName="Returns the member type price group based on a priceGroupID" output="No">
		<cfargument name="priceGroupID" type="numeric" required="yes">

		<cfset var qryPriceGroup = getMemberTypePriceGroups()>
		<cfset var argpriceGroupID = arguments.priceGroupID>

		<cfreturn QueryFilter(qryPriceGroup,function(thisRow) { return thisRow.pricegroupID eq argpriceGroupID; })>	
	</cffunction>

	<cffunction name="getMemberTypes" returntype="query" displayName="Returns the member types" output="No">
		<cfset var qryMemberTypes = "">
		
		<cfquery name="qryMemberTypes" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT membertypeID, membertype, Description, Description2, Price, rights, AcctCode, notifyemail, PercentViewable, 
				MinPages, DisplayVersion, joinoption, depoprice, courtdocprice
			FROM memberType
			ORDER BY price;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMemberTypes>
	</cffunction>

	<cffunction name="getMemberType" returntype="query" displayName="Returns the member type based on a memberTypeID" output="No">
		<cfargument name="memberTypeID" type="numeric" required="yes">

		<cfset var qryGetMemberType = getMemberTypes()>
		<cfset var argmemberTypeID = arguments.memberTypeID>

		<cfreturn QueryFilter(qryGetMemberType,function(thisRow) { return thisRow.memberTypeID eq argmemberTypeID; })>
	</cffunction>

	<cffunction name="getPromoCodes" returntype="query" displayName="Returns the promo Codes" output="No">
		<cfargument name="userAction" type="string" required="No" default="">

		<cfset var qryGetPromoCodes = "">
		
		<cfquery name="qryGetPromoCodes" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT promocodeID, upper(promocode) as promocode, codename, description, joinValid, renewValid, upgradeValid, upper(promocode) + ' - ' + codename as Combined
			FROM dbo.promoCodes
			<cfif arguments.userAction eq "join">
				WHERE joinValid = <cfqueryparam value="1" cfsqltype="CF_SQL_BIT">
			<cfelseif arguments.userAction eq "renew">
				WHERE renewValid = <cfqueryparam value="1" cfsqltype="CF_SQL_BIT">
			<cfelseif arguments.userAction eq "upgrade">
				WHERE upgradeValid = <cfqueryparam value="1" cfsqltype="CF_SQL_BIT">
			</cfif>
			ORDER BY promoCode;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryGetPromoCodes>
	</cffunction>

	<cffunction name="getPromoCode" returntype="query" displayName="Returns the promo code information based on a promo code" output="No">
		<cfargument name="promoCode" type="string" required="yes">

		<cfset var qryGetPromoCode = getPromoCodes(userAction="")>
		<cfset var argpromoCode = arguments.promoCode>

		<cfreturn QueryFilter(qryGetPromoCode,function(thisRow) { return thisRow.promoCode eq argpromoCode; })>
	</cffunction>

	<cffunction name="getAssociationsWithMembers" returntype="query" displayName="Returns the associations with members" output="No">
		<cfset var qryAssociations = "">
		
		<cfquery name="qryAssociations" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#CreateTimeSpan(0,0,5,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT DISTINCT M.TLAMemberState, TLA.shortname, TLA.Description
			FROM dbo.depomemberdata M 
			INNER JOIN dbo.depoTLA TLA ON M.TLAMemberState = TLA.State
			WHERE M.TLAMemberState <> ''
			ORDER BY M.TLAMemberState;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryAssociations>
	</cffunction>

	<cffunction name="getAssociations" returntype="query" displayName="Returns the associations with or without members" output="No">
		<cfset var qryAssociations = "">
		
		<cfquery name="qryAssociations" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#CreateTimeSpan(1,0,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT state as TLAMemberState, shortname, Description, url
			FROM dbo.depoTLA
			ORDER BY state;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryAssociations>
	</cffunction>

	<cffunction name="getHostedWebsites" returntype="query" displayName="Returns the sites that we host" output="No">
		<cfset var qryAssociations = "">
		
		<cfquery name="qryAssociations" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#CreateTimeSpan(1,0,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT state as orgcode, shortname, Description, url
			FROM dbo.depoTLA
			where isPortalLaunched = 'Y'
			ORDER BY state;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryAssociations>
	</cffunction>

	<cffunction name="getDepoGroups" returntype="query" displayName="Returns the groups" output="No">
		<cfset var qryGroups = "">
		
		<cfquery name="qryGroups" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#CreateTimeSpan(1,0,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select description, groupid
			from dbo.depogroups
			where groupID > 0
			order by description;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryGroups>
	</cffunction>

	<cffunction name="QueryToCsv" returntype="string" displayName="Converts a query to CSV" output="No">
		<cfargument name="objQuery" type="query" required="yes">
		<cfargument name="headerList" type="string" required="No" default="">
		<cfargument name="colList" type="string" required="No" default="">

		<cfscript>
		var headers = "";
		var cols = "";
		var csv = "";
		var i = 1;
		var j = 1;
		
		if (len(arguments.headerList)) headers = arguments.headerList;
		if (len(arguments.colList)) cols = arguments.colList;
		
		if (len(cols) is 0) cols = arguments.objQuery.columnList;
		if (len(headers) is 0) headers = cols;
		
		headers = listToArray(headers);

		for(i=1; i lte arrayLen(headers); i=i+1){
			csv = csv & """" & headers[i] & """";
			if (i lt arrayLen(headers)) csv = csv & ",";
		}

		csv = csv & chr(13) & chr(10);
		
		cols = listToArray(cols);
		
		for(i=1; i lte arguments.objQuery.recordCount; i=i+1){
			for(j=1; j lte arrayLen(cols); j=j+1){
				csv = csv & """" & trim(arguments.objQuery[cols[j]][i]) & """";
				if (j lt arrayLen(cols)) csv = csv & ",";
			}		
			csv = csv & chr(13) & chr(10);
		}
		</cfscript>

		<cfreturn csv>
	</cffunction>

	<cffunction name="getStates" returntype="query" displayName="Returns a query of states" output="No">
		<cfset var qryStates = "">
		
		<cfquery name="qryStates" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#createTimeSpan(1,0,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select code, name
			from dbo.states
			order by orderpref, name;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryStates>
	</cffunction>

	<cffunction name="getCountries" returntype="query" displayName="Returns a query of countries" output="No">
		<cfset var qryCountries = "">
		
		<cfquery name="qryCountries" datasource="#application.settings.dsn.trialsmith.dsn#" cachedwithin="#createTimeSpan(1,0,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select countryID, country
			from dbo.countries
			order by orderPref, country;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryCountries>
	</cffunction>

	<cffunction name="mergePDFs" access="public" returntype="void" output="no">
		<cfargument name="pdfPath" type="string" required="yes">
		<cfargument name="listofPDFs" type="string" required="yes">
		<cfargument name="finalOutPutFile" type="string" required="yes">
		
		<cfset var local = StructNew()>

		<cfscript> 
		if (right(arguments.pdfPath,1) neq "/") arguments.pdfPath = arguments.pdfPath & "/";
		
		local.pdfCopy = createObject("java", "com.lowagie.text.pdf.PdfCopy"); 
		local.pdfReader = createObject("java","com.lowagie.text.pdf.PdfReader"); 
		local.pageSize = createObject("java", "com.lowagie.text.PageSize").init(); 
		local.bookMark = createObject("java","com.lowagie.text.pdf.SimpleBookmark"); 
		local.pdfDocument = createObject("java","com.lowagie.text.Document"); 
		local.newPDF = createObject("java","java.io.FileOutputStream").init(arguments.pdfPath & arguments.finalOutPutFile);
		local.pageOffset = 0; 
		local.PDFs = listToArray(arguments.listOfPDFs);
		local.master = arrayNew(1);

		for (local.i=1; local.i LTE arrayLen(local.PDFs); local.i=local.i+1) { 
			local.reader = ""; // clobber reader 
			local.pdfFile = arguments.pdfPath & local.PDFs[local.i]; 
			local.reader = local.pdfReader.init(local.pdfFile); 
			local.reader.removeUnusedObjects();
			local.reader.consolidateNamedDestinations(); 
			local.pages = local.reader.getNumberOfPages();
			local.pageOffset = local.pageOffset + local.pages; 

			if (local.i is 1) { 
				local.pdfDocument.init(local.reader.getPageSizeWithRotation(1)); 
				local.pdfCopy.init(local.pdfDocument, local.newPDF); 
				local.pdfDocument.open();             
			}
			// now add pages to new PDF 
			for (local.p=1; local.p LTE local.pages; local.p=local.p+1){ 
				local.page=local.pdfCopy.getImportedPage(local.reader,javacast("int",local.p)); 
				local.pdfCopy.addPage(local.page); 
			}
			// special case: does this thing have any forms? 
			local.acroForm=local.reader.getAcroForm(); 
			if (isDefined("local.acroForm")) local.pdfCopy.copyAcroForm(local.reader); 
		}
		if (arraylen(local.master) GT 0) local.pdfCopy.setOutlines(local.master); 
		local.pdfDocument.close();
		</cfscript> 
	</cffunction>

	<cffunction name="getLastDayOfClosedBillingPeriod" access="public" returntype="date" output="no">
		<cfset var lastDay = "">

		<cfstoredproc procedure="up_getLastDayOfClosedBillingPeriod" datasource="#application.settings.dsn.trialsmith.dsn#">
			<cfprocparam type="out" cfsqltype="CF_SQL_TIMESTAMP" variable="lastDay">
		</cfstoredproc>

		<cfreturn lastDay>
	</cffunction>
	
	<cffunction name="getOptions" returntype="query" output="No">
		<cfargument name="contentTitle" required="false" type="string">

		<cfset var qryOptions = "">

		<cfquery name="qryOptions" datasource="#application.settings.dsn.membercentral.dsn#">			
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			declare @siteResourceStatusID int;
			select @siteResourceStatusID = membercentral.dbo.fn_getResourceStatusID('Active');
			
			select top 1 c.contentID, c.siteResourceID, cv.rawContent
			from membercentral.dbo.admin_toolTypes tt
			inner join membercentral.dbo.admin_sitetools st on st.toolTypeID = tt.toolTypeID
				and tt.toolType = 'SearchAdsAdmin'
			inner join dbo.sites s on s.siteID = st.siteID
				and s.siteCode = 'TS'
			inner join membercentral.dbo.cms_siteResources sr on sr.siteResourceID = st.siteResourceID
				and sr.siteResourceStatusID = @siteResourceStatusID
			inner join membercentral.dbo.cms_siteResources contentSR on contentSR.parentSiteResourceID = sr.siteResourceID
				and contentSR.siteResourceStatusID = @siteResourceStatusID
			inner join membercentral.dbo.cms_content c on c.siteResourceID = contentSR.siteResourceID
			inner join dbo.cms_contentLanguages cl on c.contentID = cl.contentID
				and cl.contentTitle = <cfqueryparam value="#arguments.contentTitle#" cfsqltype="CF_SQL_VARCHAR">
			inner join dbo.cms_languages l on l.languageID = cl.languageID
			inner join dbo.cms_contentVersions cv on cv.contentLanguageID = cl.contentLanguageID
			order by cv.contentVersionID desc;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryOptions>
	</cffunction>

	<cffunction name="getPaymentMethods" returntype="array" output="no">
		<cfreturn [
			{ key:"B", name:"ACH Direct Deposit" },
			{ key:"C", name:"Check" },
			{ key:"A", name:"Credit Card - American Express" },
			{ key:"D", name:"Credit Card - Discover" },
			{ key:"M", name:"Credit Card - Mastercard" },
			{ key:"V", name:"Credit Card - Visa" },
			{ key:"I", name:"Imported" },
			{ key:"L", name:"Lockbox" }
		]>
	</cffunction>
</cfcomponent>