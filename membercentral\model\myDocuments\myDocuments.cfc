<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.dataStruct = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.dataStruct.objSearch = createObject("component", "model.search.search")/>
		<cfset local.dataStruct.objDocuments = this>
		<cfset local.dataStruct.instanceSettings = getInstanceSettings(this.appInstanceID)>

		<cfset local.dataStruct.documentChatAllowed = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='isDocumentChatEnabled') EQ 1>
		<cfset local.dataStruct.depositionBucketID = local.dataStruct.objSearch.getBucketIDByType(siteID=local.rc.mc_siteinfo.siteID, bucketType='Depositions')>
		<cfset local.dataStruct.medlineBucketID = local.dataStruct.objSearch.getBucketIDByType(siteID=local.rc.mc_siteinfo.siteID, bucketType='Medline')>
		<cfset local.dataStruct.mydocumentsBucketID = local.dataStruct.objSearch.getBucketIDByType(siteID=local.rc.mc_siteinfo.siteID, bucketType='MyDocuments')>
		<cfif not IsDefined("session.mcstruct.doccartCaseRefs")>
			<cfset session.mcstruct.doccartCaseRefs = arrayNew(1)>
		</cfif>
		<!--- Redirect if invalid tab param found --->
		<cfif local.dataStruct.documentChatAllowed>
			<cfset local.allowedTabs = "CD,PD,PV,PMD,PDA,PDT,VDT,VDA,VPV,VMD,PTSAI,trialsmithchat">
		<cfelse>
			<cfset local.allowedTabs = "CD,PD,PV,PMD,PDA,PDT,VDT,VDA,VPV,VMD">
		</cfif>
		<cfset local.dataStruct.tab = arguments.event.getValue('tab','CD')>
		<cfset local.docId = arguments.event.getValue('docId','')>
		<cfset local.dataStruct.rootURL = "/?pg=myDocuments&tab=#local.dataStruct.tab#">
		<cfset local.dataStruct.thisBucketMaxPerPage = 10>
		<cfset local.dataStruct.startrow = int(val(arguments.event.getValue('start',1)))>

		<cfif arguments.event.valueExists('tab') and NOT listfindnocase(local.allowedTabs,arguments.event.getValue('tab'))>
			<cflocation url="/?pg=myDocuments" addtoken="no">
		</cfif>
		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") OR (isdefined("session.enableMobile") and session.enableMobile)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
			<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
			<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>
		</cfif>
		<cfset local.dataStruct.viewDirectory = local.viewDirectory>

		<cfif local.dataStruct.tab eq "VDA" AND len(local.docId)>
			<cfquery name="local.qryGetDocumentInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				select documentURL, title
				from disiplinaryActions.dbo.document
				where documentId = <cfqueryparam value="#local.docId#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfif local.qryGetDocumentInfo.recordCount>
				<cftry>
					<cfhttp url="#local.qryGetDocumentInfo.documentURL#" method="GET" throwonerror="Yes" result="local.PDFResult" getasbinary="yes" />

					<!--- create temp directory for pdf --->
					<cfif local.PDFResult.mimetype eq "application/pdf">
						<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=session.mcStruct.siteCode)>
						<cfset local.filename = "#local.qryGetDocumentInfo.title#.pdf">
						<cffile action="write" file="#local.strFolder.folderPath#/#local.fileName#" output="#local.PDFResult.fileContent#">
						<cfset local.tempDocumentURL = "/tsdd/" & application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.filename#", displayName="#local.filename#", forceDownload=1, deleteSourceFile=0)>

						<cfif len(local.tempDocumentURL)>
							<cflocation url="#local.tempDocumentURL#" addtoken="no">
							<cfabort>
						</cfif>
					<cfelse>
						<cfset local.viewToUse = "myDocuments/#local.viewDirectory#/noDocFound">
						<cfset local.dataStruct.docTitle = local.qryGetDocumentInfo.title>
						<cfset local.dataStruct.docID = local.docID>
						<cfset local.dataStruct.returnUrl = "#local.dataStruct.rootURL#&mode=direct&reportId=#arguments.event.getValue('reportid','0')#">
						<cfreturn returnAppStruct(local.dataStruct,local.viewToUse) >
					</cfif>
					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch)>
					</cfcatch>
				</cftry>
			<cfelse>
				<cfset local.viewToUse = "myDocuments/#local.viewDirectory#/noDocFound">
				<cfset local.dataStruct.docTitle = "">
				<cfset local.dataStruct.docID = 0>
				<cfset local.dataStruct.returnUrl = "#local.dataStruct.rootURL#&mode=direct&reportId=#arguments.event.getValue('reportid','0')#">
				<cfreturn returnAppStruct(local.dataStruct,local.viewToUse) >
			</cfif>

			<cfreturn returnAppStruct(local.dataStruct,"echo")>

		<cfelse>

			<cfif local.dataStruct.tab eq "VPV"><!--- Viewing purchased verdict --->

				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<cfset local.dataStruct.qryVerdict = QueryNew("verdictid","numeric")>
				<cfelse>
					<cfset local.dataStruct.qryVerdict = getVerdict(depomemberdataid=session.cfcuser.memberdata.depomemberdataid,verdictid=int(val(arguments.event.getValue('verdictid'))))>
				</cfif>
				<cfset local.viewToUse = "myDocuments/#local.viewDirectory#/dsp_PV">

			<cfelseif local.dataStruct.tab eq "VDA"><!--- Viewing purchased disc action report --->

				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<cfset local.dataStruct.qryDiscActionReport = QueryNew("professionalId","varchar")>
				<cfelse>
					<cfset local.dataStruct.qryDiscActionReport = getDiscAction(depomemberdataid=session.cfcuser.memberdata.depomemberdataid,reportID=int(val(arguments.event.getValue('reportID'))))>
				</cfif>
				<cfset local.dataStruct.reportID = arguments.event.getValue('reportID','0')>
				<cfset local.viewToUse = "myDocuments/#local.viewDirectory#/dsp_PDA">

			<cfelseif local.dataStruct.tab eq "VDT"><!--- Viewing purchased daubert tracker report --->

				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<cfset local.dataStruct.qryDaubertReport = QueryNew("documentID","numeric")>
				<cfelse>
					<cfset local.dataStruct.qryDaubertReport = getDaubertReport(depomemberdataid=session.cfcuser.memberdata.depomemberdataid,reportID=int(val(arguments.event.getValue('reportID'))))>
				</cfif>
				<cfset local.viewToUse = "myDocuments/#local.viewDirectory#/dsp_PDT">

			<cfelseif local.dataStruct.tab eq "VMD"><!--- Intermediate page for downloading Medline documents. --->

				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<cfset local.dataStruct.qryMedline = QueryNew("documentID","numeric")>
				<cfelse>
					<cfset local.dataStruct.qryMedline = getMedline(depomemberdataid=session.cfcuser.memberdata.depomemberdataid,reportID=int(val(arguments.event.getValue('reportID'))))>
				</cfif>
				<cfset local.viewToUse = "myDocuments/#local.viewDirectory#/dsp_PMD">
			<cfelseif local.dataStruct.documentChatAllowed and local.dataStruct.tab eq "trialsmithchat">
				<cfset local.cfcuser_membertype = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='memberType')>
				<cfset local.cfcuser_billingstate = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingState')>
				<cfset local.cfcuser_billingzip = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingZip')>
				<cfset local.tschatmode = arguments.event.getTrimValue('tschatmode','init')>
				<cfset local.viewToUse = "myDocuments/noRights">

				<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<cfset local.dataStruct.qryAIExpert = QueryNew("caseID,caseExpertID,first_name,last_name","numeric,numeric,varchar,varchar")>
				<cfelse>
					<cfset local.dataStruct.qryAIExpert = getAIExpert(depomemberdataid=session.cfcuser.memberdata.depomemberdataid,caseID=int(val(arguments.event.getValue('caseID',0))),expertID=int(val(arguments.event.getValue('expertID',0))))>
				</cfif>

				<cfif NOT local.dataStruct.qryAIExpert.recordCount>
					<cfset local.tschatmode = 'noRights'>
				</cfif>

				<cfset local.billingPlanID = val(local.dataStruct.qryAIExpert.billingPlanID)>

				<cfswitch expression="#local.tschatmode#">
					<cfcase value="listAssocDepos">
						<cfset local.qryDepos = getAssociatedDepos(depomemberdataid=session.cfcuser.memberdata.depomemberdataid, caseExpertID=val(local.dataStruct.qryAIExpert.caseExpertID), caseID=val(local.dataStruct.qryAIExpert.caseID))>
						
						<cfsavecontent variable="local.data">
							<cfinclude template="/views/myDocuments/listAssociatedDepos.cfm">
						</cfsavecontent>

						<cfreturn returnAppStruct(local.data,"echo")>
					</cfcase>
					<cfcase value="listAvailableDepos">
						<cfsavecontent variable="local.data">
							<cfinclude template="/views/myDocuments/frm_availableDepos.cfm">
						</cfsavecontent>

						<cfreturn returnAppStruct(local.data,"echo")>
					</cfcase>
					<cfcase value="getAvailableDepos">
						<cfset local.qryDepos = getAvailableDeposFromFilters(event=arguments.event, filterMode="list")>

						<cfif local.qryDepos.recordCount>
							<cfset local.currentDocument = CreateObject('component','model.system.platform.tsDocument')>
							<cfset local.qryDepoCaseActiveBillingPlan = getDepoCaseActiveBillingPlan(billingPlanID=local.billingPlanID)>
							<cfset local.startRow = int(val(arguments.event.getValue('startrow',1)))>
							<cfset local.maxPerPage = int(val(arguments.event.getValue('maxrowcount',1)))>
							<cfset local.totalCount = local.qryDepos.totalCount>
							<cfset local.startPage = 1>
							<cfset local.endPage = 1>
							<cfset local.maxPage = 5>
							<cfset local.numTotalPages = Ceiling(local.qryDepos.totalCount / local.maxPerPage)>
							<cfset local.numCurrentPage = int((int(local.startRow) + local.maxPerPage - 1) / local.maxPerPage)>

							<cfif local.numCurrentPage gt 1 and (Ceiling((local.numCurrentPage + (local.maxPage / 2))) gt local.maxPage)>
								<cfset local.endPage = Ceiling(local.numCurrentPage + (local.maxPage / 2))>
								<cfset local.startPage = local.endPage - (local.maxPage - 1)>
								<cfif local.endPage gte local.numTotalPages>
									<cfset local.endPage = local.numTotalPages>
									<cfif local.endPage lte local.maxPage>
										<cfset local.startPage = 1>
									<cfelse>
										<cfset local.startPage = local.endPage - (local.maxPage - 1)>
									</cfif>
								</cfif>
							<cfelseif local.numCurrentPage gte 1 and local.numCurrentPage lte local.maxPage and local.maxPage lte local.numTotalPages>
								<cfset local.endPage = local.maxPage>
							<cfelseif local.numTotalPages lte local.maxPage>
								<cfset local.endPage = local.numTotalPages>
							</cfif>

							<cfset local.selectedDocIDs = arguments.event.getTrimValue('selectedDocIDs','')>
						</cfif>
						
						<cfsavecontent variable="local.data">
							<cfinclude template="/views/myDocuments/listAvailableDepos.cfm">
						</cfsavecontent>

						<cfreturn returnAppStruct(local.data,"echo")>
					</cfcase>
					<cfcase value="listAddDeposSummary">
						<cfset local.qryDepos = getAvailableDeposFromFilters(event=arguments.event, filterMode="count")>
						<cfif local.qryDepos.recordCount>
							<cfset local.qryTSMemberData = application.objMember.getTSMemberData(depoMemberDataID=session.cfcuser.memberData.depoMemberDataID)>
							<cfset local.qryStates = application.objCommon.getTSStates()>							
							<cfset local.totalPrice = getDepoDocPriceIncFeePerUpload(qryDepos=local.qryDepos, billingPlanID=local.billingPlanID).totalPrice>
						</cfif>

						<cfsavecontent variable="local.data">
							<cfinclude template="/views/myDocuments/listAddDeposSummary.cfm">
						</cfsavecontent>

						<cfreturn returnAppStruct(local.data,"echo")>
					</cfcase>
					<cfcase value="loadPaymentForm">
						<cfset local.qryDepos = getAvailableDeposFromFilters(event=arguments.event, filterMode="count")>
						<cfset local.strTotals = getDepoDocPriceIncFeePerUpload(qryDepos=local.qryDepos, billingPlanID=local.billingPlanID)>

						<cfsavecontent variable="local.data">
							<cfinclude template="/views/myDocuments/frm_depoDocsPayment.cfm">
						</cfsavecontent>

						<cfreturn returnAppStruct(local.data,"echo")>
					</cfcase>
					<cfcase value="processPayment">
						<cfset arguments.event.setValue('filterOpts','seldocs')>
						<cfset arguments.event.setValue('selectedDocIDs',arguments.event.getTrimValue('documentIDs',''))>
						<cfset local.qryDepos = getAvailableDeposFromFilters(event=arguments.event, filterMode="count")>

						<cfif local.qryDepos.recordCount>
							<cfset local.strTotals = getDepoDocPriceIncFeePerUpload(qryDepos=local.qryDepos, billingPlanID=local.billingPlanID)>

							<cfif local.strTotals.totalPrice GT 0>
								<cfset local.cidForGather = "olddid_#session.cfcuser.memberData.depoMemberDataID#">
								<cfset local.strArgs = { "customerid"=local.cidForGather, "amount"=local.strTotals.totalPrice, "chargeDesc"="TrialSmith.com Deposition Document Purchase for Analysis", 
									"merchantProfile"='TS', "TransactionDepoMemberDataID"=session.cfcuser.memberData.depoMemberDataID }>
								<cfif arguments.event.valueExists('p_TS_tokenData') AND len(arguments.event.getTrimValue('p_TS_tokenData'))>
									<cfset local.strArgs['tokenData'] = deserializeJSON(arguments.event.getTrimValue('p_TS_tokenData'))>
								</cfif>
								<cfset local.strPaymentResponseResult = CreateObject("component","model.buyNow.BuyNow").chargeCC_TS(argumentcollection=local.strArgs)>

								<cfif local.strPaymentResponseResult.ccsuccess>
									<cfset local.caseRef = arguments.event.getTrimValue('caseRef','One Click Purchase')>
									<cfif NOT structKeyExists(session.mcStruct,"doccartCaseRefs")>
										<cfset session.mcstruct['doccartCaseRefs'] = arrayNew(1)>
									</cfif>
									<cfif len(local.caseRef) and local.caseRef neq "One Click Purchase" and session.mcstruct.doccartCaseRefs.indexOf(local.caseRef) lt 0>
										<cfset arrayAppend(session.mcstruct.doccartCaseRefs,local.caseRef)>
									</cfif>

									<cfquery name="local.qryCreateTransactions" datasource="#application.dsn.tlasites_trialsmith.dsn#">
										SET XACT_ABORT, NOCOUNT ON;
										BEGIN TRY

											IF OBJECT_ID('tempdb..##tmpDepoDocs') IS NOT NULL
												DROP TABLE ##tmpDepoDocs;
											CREATE TABLE ##tmpDepoDocs (documentID int PRIMARY KEY);

											DECLARE @depomemberdataID int, @billingstate varchar(5), @billingzip varchar(25), @orgCode varchar(10),
												@linksource varchar(50), @linkterms varchar(100), @statsSessionID int, @transactionID int, 
												@caseref varchar(600), @membertype int, @documentID int, @caseID int, @caseExpertID int, @billingPlanID int;
											
											SET @depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.depoMemberDataID#">;
											SET @billingstate = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.cfcuser_billingstate#">;
											SET @billingzip = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.cfcuser_billingzip#">;
											SET @orgCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#session.mcStruct.siteCode#">;
											SET @linksource = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#session.mcStruct.linksource#">;
											SET @linkterms = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#session.mcStruct.linkterms#">;
											SET @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">;
											SET @caseRef = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.caseRef#">;
											SET @membertype = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.cfcuser_membertype#">;
											SET @caseID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('caseID',0)#">;
											SET @caseExpertID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('expertID',0)#">;
											SET @billingPlanID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.billingPlanID#">;
											
											INSERT INTO ##tmpDepoDocs (documentID)
											SELECT DISTINCT listItem
											FROM membercentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#valueList(local.qryDepos.documentID)#">,',');

											BEGIN TRAN;
												SELECT @documentID = MIN(documentID) FROM ##tmpDepoDocs;
												WHILE @documentID IS NOT NULL BEGIN
													SET @transactionID = NULL;

													EXEC dbo.up_document_buy @documentID=@documentID, @depomemberdataID=@depomemberdataID, @membertype=@membertype, 
														@billingstate=@billingstate, @billingzip=@billingzip, @orgcode=@orgcode, @allowPCuse=0, 
														@incFeePerUpload=1, @billingPlanID=@billingPlanID, @caseref=@caseref, @linksource=@linksource, 
														@linkterms=@linkterms, @statsSessionID=@statsSessionID, @transactionID=@transactionID OUTPUT;

													IF @transactionID > 0
														INSERT INTO dbo.depomemberdataCaseExpertDocuments (documentID, caseID, caseExpertID, isActive, dateAdded)
														VALUES (@documentID, @caseID, @caseExpertID, 1, GETDATE());

													SELECT @documentID = MIN(documentID) FROM ##tmpDepoDocs WHERE documentID > @documentID;
												END
											COMMIT TRAN;

											IF OBJECT_ID('tempdb..##tmpDepoDocs') IS NOT NULL
												DROP TABLE ##tmpDepoDocs;
											
										END TRY
										BEGIN CATCH
											IF @@trancount > 0 ROLLBACK TRANSACTION;
											EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
										END CATCH
									</cfquery>

									<cfsavecontent variable="local.data">
										<cfoutput>
											<div class="alert alert-success d-flex align-items-center px-2" role="alert">
												<i class="fas fa-check-circle fa-lg me-2"></i>
												<div><span class="fw-semibold">Payment successful!</span><br>You have purchased <strong>#local.qryDepos.totalCount# document#local.qryDepos.totalCount GT 1 ? 's' : ''#</strong> for analysis.</div>
											</div>
										</cfoutput>
									</cfsavecontent>
								<cfelse>
									<cfsavecontent variable="local.data">
										<cfoutput>
											<div class="alert alert-danger d-flex align-items-center px-2" role="alert">
												<i class="fa-solid fa-circle-exclamation fa-lg me-2"></i>
												<div><span class="fw-semibold">Error!</span><br>#local.strPaymentResponseResult.ccresponse#</div>
											</div>
										</cfoutput>
									</cfsavecontent>
								</cfif>
							</cfif>
						<cfelse>
							<cfset local.data = "No depositions selected.">
						</cfif>

						<cfreturn returnAppStruct(local.data,"echo")>
					</cfcase>
					<cfdefaultcase>
						<cfset local.dataStruct.expDocChatResURL = "/?event=cms.showResource&resID=#local.rc.resID#&expertid=#local.dataStruct.qryAIExpert.caseExpertID#&caseid=#local.dataStruct.qryAIExpert.caseID#&tab=trialsmithchat&mode=stream">
						<cfset local.viewToUse = "myDocuments/documentChatUI">
					</cfdefaultcase>
				</cfswitch>
			
			<cfelse>
				<!--- check tax info --->
				<cfset local.dataStruct.billingState = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingState')>
				<cfset local.dataStruct.billingZip = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingZip')>
				<cfset local.dataStruct.hasMissingTaxInfo = len(local.dataStruct.billingState) eq 0 OR len(local.dataStruct.billingZip) eq 0>

				<cfset local.viewToUse = "myDocuments/#local.viewDirectory#/dsp_myDocuments">
			</cfif>
			<cfif arguments.event.getValue('mode','normal') EQ "stream" and local.dataStruct.tab neq "trialsmithchat">
				<cfswitch expression="#arguments.event.getValue("tab","CD")#">
					<cfcase value="CD">
						<cfreturn getAllContributedDocuments(maxrows=local.dataStruct.thisBucketMaxPerPage,startrow=arguments.event.getValue('startrow',1),viewdirectory=local.viewDirectory)>
					</cfcase>
					<cfcase value="PD">
						<cfreturn getAllPurchasedDocuments(maxrows=local.dataStruct.thisBucketMaxPerPage,startrow=arguments.event.getValue('startrow',1),viewdirectory=local.viewDirectory)>
					</cfcase>
					<cfcase value="PV">
						<cfreturn getAllVerdicts(maxrows=local.dataStruct.thisBucketMaxPerPage,startrow=arguments.event.getValue('startrow',1),viewdirectory=local.viewDirectory)>
					</cfcase>
					<cfcase value="PMD">
						<cfreturn getAllMedline(maxrows=local.dataStruct.thisBucketMaxPerPage,startrow=arguments.event.getValue('startrow',1),viewdirectory=local.viewDirectory)>
					</cfcase>
					<cfcase value="PDA">
						<cfreturn getAllDiscActions(maxrows=local.dataStruct.thisBucketMaxPerPage,startrow=arguments.event.getValue('startrow',1),viewdirectory=local.viewDirectory)>
					</cfcase>
					<cfcase value="PDT">
						<cfreturn getAllDaubert(maxrows=local.dataStruct.thisBucketMaxPerPage,startrow=arguments.event.getValue('startrow',1),viewdirectory=local.viewDirectory)>
					</cfcase>
					<cfcase value="PTSAI">
						<cfreturn getAllAIExperts(maxrows=local.dataStruct.thisBucketMaxPerPage,startrow=arguments.event.getValue('startrow',1),viewdirectory=local.viewDirectory)>
					</cfcase>
				</cfswitch>
			<cfelse>
				<cfreturn returnAppStruct(local.dataStruct,local.viewToUse)>
			</cfif>

		</cfif>
	</cffunction>

	<cffunction name="getStandardNumber" access="public" output="no" returntype="string" hint="Retrun the standard number needed for price estimates">
		<cfargument name="issn" type="any" required="true">
		<cfargument name="eissn" type="any" required="true">
		<cfargument name="isbn" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.standardNumber = "">
		<cftry>
 			<cfif len(arguments.issn)>
				<cfset local.standardNumber = arguments.issn>
 			<cfelseif len(arguments.eissn)>
				<cfset local.standardNumber = arguments.eissn>
 			<cfelseif len(arguments.isbn)>
				<cfset local.standardNumber = arguments.isbn>
			</cfif>
		<cfcatch type="any">
			<cfset local.standardNumber = "">
		</cfcatch>
		</cftry>

		<cfreturn local.standardNumber>
	</cffunction>

	<cffunction name="getAllAIExperts" access="public" output="no" returntype="struct" hint="I return all AI Case Experts by depomemberdataid">
		<cfargument  name="maxrows" type="numeric" required="true">
		<cfargument  name="startrow" type="numeric" required="true">
		<cfargument  name="viewdirectory" type="string" required="true">
		<cfset var local = structNew()>
		<cfset local.maxRows = arguments.maxrows>
		<cfset local.startRow = arguments.startrow>
		<cfset local.viewDirectory = arguments.viewdirectory>
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.qryAllAIExperts = QueryNew("reportID,itemCount","numeric,numeric")>
		<cfelse>
			<cfquery name="local.qryAllAIExperts" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				set nocount on;

				DECLARE @depomemberdataid int = <cfqueryparam value="#session.cfcuser.memberdata.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

				DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), caseID int, caseExpertID int, docCount int);
				DECLARE @itemcount int;

				INSERT INTO @tmpResults (caseID, caseExpertID, docCount)
				select dc.caseID,dce.caseExpertID,0
				from depomemberdataCases dc
				inner join depomemberdataCaseExperts dce
					on dc.caseID = dce.caseID
					and dc.depomemberdataid = @depomemberdataid

				set @itemcount = @@ROWCOUNT;

				UPDATE t
				SET docCount = COALESCE(d.docCount, 0)
				FROM @tmpResults t
				LEFT JOIN (
					SELECT caseID, caseExpertID, COUNT(*) AS docCount
					FROM [dbo].[depomemberdataCaseExpertDocuments]
					GROUP BY caseID, caseExpertID
				) d ON t.caseExpertID = d.caseExpertID AND t.caseID = d.caseID;

				select TOP (#arguments.maxrows#) @itemcount as itemCount, dce.caseExpertID, dce.first_name, dce.last_name, dce.expertSlug, dce.agentUserUID, dc.caseID, dc.caseReference, dc.dateCreated as caseDateCreated, docCount as docCount
				from @tmpResults as tmp
				inner join depomemberdataCases dc
					on tmp.caseID = dc.caseID
				inner join depomemberdataCaseExperts dce
					on dc.caseID = dce.caseID
					and dce.caseExpertID = tmp.caseExpertID
				where tmp.autoid >= #arguments.startrow#
				ORDER BY tmp.autoid;
			</cfquery>
		</cfif>
		<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode)>
		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName="myDocuments",siteID=local.siteInfo.siteID)>
		<cfsavecontent variable="local.data">
			<cfinclude  template="/views/myDocuments/#local.viewDirectory#/dsp_AIExpertList.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getAIExpert" access="private" output="no" returntype="query" hint="I return AI Expert details by caseID and caseExpertID">
		<cfargument  name="depoMemberDataID" type="numeric" required="true">
		<cfargument  name="caseID" type="numeric" required="true">
		<cfargument  name="expertID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery name="local.qryAIExpert" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			set nocount on;

			DECLARE
				@depomemberdataid int = <cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">,
				@caseID int = <cfqueryparam value="#arguments.caseID#" cfsqltype="CF_SQL_INTEGER">,
				@expertID int = <cfqueryparam value="#arguments.expertID#" cfsqltype="CF_SQL_INTEGER">;

			select dce.caseExpertID, dce.first_name, dce.last_name, dce.expertSlug, dce.agentUserUID, dc.caseID, dc.caseReference, dc.dateCreated as caseDateCreated, dce.billingPlanID
			from depomemberdataCases dc
			inner join depomemberdataCaseExperts dce
				on dc.caseID = dce.caseID
				and dc.depomemberdataid = @depomemberdataid
				and dc.caseID = @caseID
				and dce.caseexpertID = @expertID;
		</cfquery>
		<cfreturn local.qryAIExpert>
	</cffunction>

	<cffunction name="getAllVerdicts" access="public" output="no" returntype="struct" hint="I return all verdicts by depomemberdataid">
		<cfargument  name="maxrows" type="numeric" required="true">
		<cfargument  name="startrow" type="numeric" required="true">
		<cfargument  name="viewdirectory" type="string" required="true">
		<cfset var local = structNew()>

		<cfset local.maxRows = arguments.maxrows>
		<cfset local.startRow = arguments.startrow>
		<cfset local.viewDirectory = arguments.viewdirectory>
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.qryAllVerdicts = QueryNew("verdictid,itemCount","numeric,numeric")>
		<cfelse>
			<cfquery name="local.qryAllVerdicts" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				set nocount on;

				DECLARE @depomemberdataid int = <cfqueryparam value="#session.cfcuser.memberdata.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

				DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), verdictid int, dateAdded datetime);
				DECLARE @itemcount int;

				INSERT INTO @tmpResults (verdictid, dateAdded)
				SELECT v.verdictid, p.dateAdded
				FROM dbo.verdicts AS v
				INNER JOIN dbo.verdictPermissions AS p ON v.verdictid = p.verdictid
				inner join (
					select @depomemberdataid as depomemberdataid
					union
					select fpl.depoMemberDataID
					from dbo.tlaFirmPlanLink as fpl
					where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
				) as acct on acct.depomemberdataid = p.depomemberdataid
				ORDER BY p.dateAdded desc;

				set @itemcount = @@ROWCOUNT;

				select TOP (#arguments.maxrows#) @itemcount as itemCount, tmp.dateAdded,
					v.verdictid, v.casename, v.court, v.state, v.verdictDate, v.experts
				from @tmpResults as tmp
				inner join dbo.verdicts AS v on v.verdictid = tmp.verdictid
				where tmp.autoid >= #arguments.startrow#
				ORDER BY tmp.autoid;
			</cfquery>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfinclude  template="/views/myDocuments/#local.viewDirectory#/dsp_PVList.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getVerdict" access="private" output="no" returntype="query" hint="I return veridct details by verdictid">
		<cfargument  name="depoMemberDataID" type="numeric" required="true">
		<cfargument  name="verdictid" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery name="local.qryVerdict" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			set nocount on;

			DECLARE @depomemberdataid int = <cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

			select TOP 1 v.verdictid, vv.name as publisher, v.court, v.casename, v.docketNo, v.Judge, v.state, v.VerdictDate,
				v.Experts, v.Attorneys, v.Facts, v.Injuries, v.Verdict, v.AdditionalInfo
			from dbo.verdicts as v
				inner join dbo.verdictvendor as vv on vv.vendorid = v.vendorid
				INNER JOIN dbo.verdictPermissions AS p ON v.verdictid = p.verdictid
				inner join (
					select @depomemberdataid as depomemberdataid
						union
					select fpl.depoMemberDataID
					from dbo.tlaFirmPlanLink as fpl
					where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
				) as acct on acct.depomemberdataid = p.depomemberdataid
			where v.verdictID = <cfqueryparam value="#arguments.verdictID#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>
		<cfreturn local.qryVerdict>
	</cffunction>

	<cffunction name="getAllDiscActions" access="public" output="no" returntype="struct" hint="I return all verdicts by depomemberdataid">
		<cfargument  name="maxrows" type="numeric" required="true">
		<cfargument  name="startrow" type="numeric" required="true">
		<cfargument  name="viewdirectory" type="string" required="true">
		<cfset var local = structNew()>
		<cfset local.maxRows = arguments.maxrows>
		<cfset local.startRow = arguments.startrow>
		<cfset local.viewDirectory = arguments.viewdirectory>
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.qryAllDiscActions = QueryNew("reportID,itemCount","numeric,numeric")>
		<cfelse>
			<cfquery name="local.qryAllDiscActions" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				set nocount on;

				DECLARE @depomemberdataid int = <cfqueryparam value="#session.cfcuser.memberdata.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

				DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), reportID int, uniqueNameID int);
				DECLARE @itemcount int;

				INSERT INTO @tmpResults (uniqueNameID, reportID)
				select pr.uniqueNameID, max(pr.reportID) as reportID
				from dbo.da_purchasedReports as pr
				inner join (
					select @depomemberdataid as depomemberdataid
					union
					select fpl.depoMemberDataID
					from dbo.tlaFirmPlanLink as fpl
					where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
				) as acct on acct.depomemberdataid = pr.depomemberdataid
				group by pr.uniqueNameID
				order by reportID desc;

				set @itemcount = @@ROWCOUNT;

				select TOP (#arguments.maxrows#) @itemcount as itemCount, pr.reportID, pr.dateEntered,
					un.uniqueNameID, un.companyName, un.salutation, un.firstname, un.middlename, un.lastname, un.suffix, un.professionalSuffix
				from @tmpResults as tmp
				inner join dbo.da_purchasedReports as pr on pr.reportID = tmp.reportID
				inner join disiplinaryActions.dbo.uniqueNames AS un on un.uniqueNameid = pr.uniqueNameid
				where tmp.autoid >= #arguments.startrow#
				ORDER BY tmp.autoid;
			</cfquery>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfinclude  template="/views/myDocuments/#local.viewDirectory#/dsp_PDAList.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getDiscAction" access="private" output="no" returntype="query" hint="I return Disc Action Report details by reportID">
		<cfargument  name="depoMemberDataID" type="numeric" required="true">
		<cfargument  name="reportID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery name="local.qryDiscAction" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			set nocount on

			DECLARE @depomemberdataid int = <cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

			select pr.dateEntered, p.professionalId, p.companyName, p.salutation, p.firstName, p.lastName, p.middleName,
				p.suffix, p.professionalSuffix, p.alias, p.licenseNumber, p.birthYear, p.address1, p.address2, p.city,
				p.state, p.zip, p.phone, p.subSpecialties, p.actionDate, p.misconductDescr, p.actionsTaken,
				d.name as disciplineName, a.name as authorityName, a.state as authorityState, a.url as authorityURL,
				doc.documentID, doc.title, doc.documentURL
			from dbo.da_purchasedReports as pr
			INNER JOIN dbo.da_purchasedReportItems as pri on pri.reportID = pr.reportID
				AND pr.reportID = <cfqueryparam value="#arguments.reportID#" cfsqltype="CF_SQL_INTEGER">
			INNER JOIN disiplinaryActions.dbo.professional as p on p.professionalID = pri.professionalID
			LEFT OUTER JOIN disiplinaryActions.dbo.document as doc on doc.professionalID = pri.professionalID
				and doc.active = 1 and doc.documentURL is not null
			LEFT OUTER JOIN disiplinaryActions.dbo.discipline as d on d.disciplineID = p.disciplineID
			LEFT OUTER JOIN disiplinaryActions.dbo.authority as a on a.authorityID = p.authorityID
			inner join (
				select @depomemberdataid as depomemberdataid
					union
				select fpl.depoMemberDataID
				from dbo.tlaFirmPlanLink as fpl
				where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
			) as acct on acct.depomemberdataid = pr.depomemberdataid
			order by p.lastActionDate desc, p.professionalId;
		</cfquery>
		<cfreturn local.qryDiscAction>
	</cffunction>

	<cffunction name="getAllDaubert" access="public" output="no" returntype="struct" hint="I return all verdicts by depomemberdataid">
		<cfargument  name="maxrows" type="numeric" required="true">
		<cfargument  name="startrow" type="numeric" required="true">
		<cfargument  name="viewdirectory" type="string" required="true">
		<cfset var local = structNew()>
		<cfset local.maxRows = arguments.maxrows>
		<cfset local.startRow = arguments.startrow>
		<cfset local.viewDirectory = arguments.viewdirectory>
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.qryAllDaubert = QueryNew("reportID,itemCount","numeric,numeric")>
		<cfelse>
			<cfquery name="local.qryAllDaubert" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				set nocount on;

				DECLARE @depomemberdataid int = <cfqueryparam value="#session.cfcuser.memberdata.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

				DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), reportID int, uniqueNameID int);
				DECLARE @itemcount int;

				INSERT INTO @tmpResults (uniqueNameID, reportID)
				select pr.mcRecordID, max(pr.reportID) as reportID
				from dbo.dt_purchasedReports as pr
				inner join (
					select @depomemberdataid as depomemberdataid
					union
					select fpl.depoMemberDataID
					from dbo.tlaFirmPlanLink as fpl
					where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
				) as acct on acct.depomemberdataid = pr.depomemberdataid
				group by pr.mcRecordID
				order by reportID desc;

				set @itemcount = @@ROWCOUNT;

				select TOP (#arguments.maxrows#) @itemcount as itemCount, pr.reportID, pr.dateEntered,
					daubert.mcRecordID, daubert.salutation, daubert.firstname, daubert.middlename, daubert.lastname, daubert.suffix,
					daubert.OpinionDate, daubert.jurisdiction, daubert.casecaption
				from @tmpResults as tmp
				inner join dbo.dt_purchasedReports as pr on pr.reportID = tmp.reportID
				inner join daubertTracker.dbo.documents AS daubert on daubert.mcRecordID = pr.mcRecordID
				where tmp.autoid >= #arguments.startrow#
				ORDER BY tmp.autoid;
			</cfquery>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfinclude  template="/views/myDocuments/#local.viewDirectory#/dsp_PDTList.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getDaubertReport" access="private" output="no" returntype="query" hint="I return DaubertReport by reportID">
		<cfargument  name="depoMemberDataID" type="numeric" required="true">
		<cfargument  name="reportID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery name="local.qryDaubertReport" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			set nocount on;

			DECLARE @depomemberdataid int = <cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

			select pr.dateEntered, d.*
			from dbo.dt_purchasedReports as pr
			INNER JOIN dbo.dt_purchasedReportItems as pri on pri.reportID = pr.reportID
			INNER JOIN daubertTracker.dbo.documents as d on d.caseRecordID = pri.caseRecordID
			inner join (
				select @depomemberdataid as depomemberdataid
				union
				select fpl.depoMemberDataID
				from dbo.tlaFirmPlanLink as fpl
				where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
			) as acct on acct.depomemberdataid = pr.depomemberdataid
			where pr.reportID = <cfqueryparam value="#arguments.reportID#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>
		<cfreturn local.qryDaubertReport>
	</cffunction>

	<cffunction name="getAllMedline" access="public" output="no" returntype="struct" hint="I return all verdicts by depomemberdataid">
		<cfargument  name="maxrows" type="numeric" required="true">
		<cfargument  name="startrow" type="numeric" required="true">
		<cfargument  name="viewdirectory" type="string" required="true">
		<cfset var local = structNew()>
		<cfset local.maxRows = arguments.maxrows>
		<cfset local.startRow = arguments.startrow>
		<cfset local.viewDirectory = arguments.viewdirectory>
		<cfset local.objDocuments = this/>
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.qryAllMedline = QueryNew("documentID,itemCount","numeric,numeric")>
		<cfelse>
			<cfquery name="local.qryAllMedline" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				set nocount on;

				DECLARE @depomemberdataid int = <cfqueryparam value="#session.cfcuser.memberdata.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

				DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), documentID int);
				DECLARE @itemcount int;

				INSERT INTO @tmpResults (documentID)
				select pd.documentID
				from dbo.medline_purchasedDocuments as pd
				inner join (
					select @depomemberdataid as depomemberdataid
					union
					select fpl.depoMemberDataID
					from dbo.tlaFirmPlanLink as fpl
					where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
				) as acct on acct.depomemberdataid = pd.depomemberdataid
				order by pd.documentID desc;

				set @itemcount = @@ROWCOUNT;

				select TOP (#arguments.maxrows#) @itemcount as itemCount, pd.documentID, pd.dateEntered,
					medline.documentItemID, medline.pmid, medline.expertname, medline.docTitle, medline.documentURL,
					medline.urlExpiration, datediff(day, getDate(), medline.urlExpiration) as urlExpirationInDays,
					medline.message, medline.lastUpdated, statuses.medlineStatus, medline.orderID, medline.rndID,
					medline.issn, medline.eissn, medline.isbn, medline.pubdate,
					trialsmith.dbo.medline_DocumentsOwnedXML(@depomemberdataid,medline.pmid).value('/medline[1]/@documentID','int') as owned,
					inCart = CASE
						WHEN EXISTS (select documentid from trialsmith.dbo.documentcart where documentid = medline.pmid and depomemberdataid = @depomemberdataid and itemTypeID = 8) then 1
						ELSE 0
						END
				from @tmpResults as tmp
				inner join dbo.medline_purchasedDocuments as pd on pd.documentID = tmp.documentID
				inner join dbo.medline_purchasedDocumentItems AS medline on medline.documentID = pd.documentID
				inner join dbo.medline_statuses statuses on statuses.medlineStatusID = medline.medlineStatusID
				where tmp.autoid >= #arguments.startrow#
				ORDER BY tmp.autoid;
			</cfquery>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfinclude  template="/views/myDocuments/#local.viewDirectory#/dsp_PMDList.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getMedline" access="private" output="no" returntype="query" hint="I return Medline by reportID">
		<cfargument  name="depoMemberDataID" type="numeric" required="true">
		<cfargument  name="reportID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery name="local.qryMedline" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			set nocount on;

			DECLARE @depomemberdataid int = <cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

			select pr.dateEntered, pri.*
			from dbo.medline_purchasedDocuments as pr
			INNER JOIN dbo.medline_purchasedDocumentItems as pri on pri.documentID = pr.documentID
			inner join (
				select @depomemberdataid as depomemberdataid
				union
				select fpl.depoMemberDataID
				from dbo.tlaFirmPlanLink as fpl
				where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
			) as acct on acct.depomemberdataid = pr.depomemberdataid
			where pr.documentID = <cfqueryparam value="#arguments.reportID#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>
		<cfreturn local.qryMedline>
	</cffunction>

	<cffunction name="getAllContributedDocuments" access="public" output="no" returntype="struct" hint="I return all contributed documents tab content">
		<cfargument name="maxrows" type="numeric" required="true">
		<cfargument name="startrow" type="numeric" required="true">
		<cfargument name="viewdirectory" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.maxRows = arguments.maxrows>
		<cfset local.startRow = arguments.startrow>
		<cfset local.viewDirectory = arguments.viewdirectory>
		<cfset local.dropboxAppKey = "">
		<cfif structKeyExists(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode), "dropboxappkey") and len(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey)>
			<cfset local.dropboxAppKey = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey>
		</cfif>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.qryAllContributedDocuments = QueryNew("documentID,itemCount","numeric,numeric")>
		<cfelse>
			<cfset local.objTSDocument = CreateObject('component','model.system.platform.tsDocument')>

			<cfquery name="local.qryAllContributedDocuments" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				set nocount on;

				DECLARE @depomemberdataid int = <cfqueryparam value="#session.cfcuser.memberdata.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

				DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), documentid int);
				DECLARE @itemcount int;

				INSERT INTO @tmpResults (documentid)
				SELECT d.DocumentID
				FROM dbo.depoDocuments AS d
				inner join dbo.depodocumenttypes as dt on d.documenttypeid = dt.typeid and dt.acctcode between 3000 and 3999
				inner join (
					select @depomemberdataid as depomemberdataid
					union
					select fpl.depoMemberDataID
					from dbo.tlaFirmPlanLink as fpl
					where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
				) as acct on acct.depomemberdataid = d.depomemberdataid
				WHERE d.disabled = 'N'
				and d.uploadStatus <> 1
				and d.reviewFlag = 0
				ORDER BY d.DocumentID desc;

				set @itemcount = @@ROWCOUNT;

				select TOP (#local.maxrows#) @itemcount as itemCount,
					d.documentid, d.expertname, d.documentdate, d.style, d.state,
					ct.description as causedesc, d.notes, m.email, m.firstname, m.lastname, m.billingfirm,
					m.phone, dfo.dateLastModified as uploadpdfdate
				from @tmpResults as tmp
				inner join dbo.depodocuments AS d on d.documentid = tmp.documentid
				inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
				inner join dbo.depomemberdata as m on m.depomemberdataid = d.depomemberdataid
				left outer join dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'pdf'
				where tmp.autoid >= #local.startRow#
				ORDER BY tmp.autoid;
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude  template="/views/myDocuments/#local.viewDirectory#/dsp_CDList.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getAllPurchasedDocuments" access="public" output="no" returntype="struct" hint="I return all verdicts by depomemberdataid">
		<cfargument name="maxrows" type="numeric" required="true">
		<cfargument name="startrow" type="numeric" required="true">
		<cfargument name="viewdirectory" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.maxRows = arguments.maxrows>
		<cfset local.startRow = arguments.startrow>
		<cfset local.viewDirectory = arguments.viewdirectory>
		<cfset local.dropboxAppKey = "">
		<cfif structKeyExists(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode), "dropboxappkey") and len(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey)>
			<cfset local.dropboxAppKey = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey>
		</cfif>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.qryAllPurchasedDocuments = QueryNew("documentID,itemCount","numeric,numeric")>
		<cfelse>
			<cfset local.objTSDocument = CreateObject('component','model.system.platform.tsDocument')>

			<cfquery name="local.qryAllPurchasedDocuments" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				set nocount on;

				DECLARE @depomemberdataid int = <cfqueryparam value="#session.cfcuser.memberdata.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

				DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), documentid int, dateAdded datetime);
				DECLARE @itemcount int;

				INSERT INTO @tmpResults (documentid, dateAdded)
				SELECT d.DocumentID, p.DateAdded
				FROM dbo.depoDocuments AS d
				INNER JOIN dbo.depoPermissions AS p ON d.DocumentID = p.DocumentID
				inner join dbo.depodocumenttypes as dt on d.documenttypeid = dt.typeid and (dt.acctcode between 3000 and 3999 or dt.acctcode in (5005,5006))
				inner join (
					select @depomemberdataid as depomemberdataid
					union
					select fpl.depoMemberDataID
					from dbo.tlaFirmPlanLink as fpl
					where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
				) as acct on acct.depomemberdataid = p.depomemberdataid
				WHERE d.disabled = 'N'
				ORDER BY p.DateAdded desc;

				set @itemcount = @@ROWCOUNT;

				select TOP (#local.maxrows#) @itemcount as itemCount,
					d.documentid, d.expertname, d.documentdate, d.style, d.state,
					ct.description as causedesc, d.notes, m.email, m.firstname, m.lastname, m.billingfirm,
					m.phone, tmp.dateAdded, dfo.dateLastModified as uploadpdfdate
				from @tmpResults as tmp
				inner join dbo.depodocuments AS d on d.documentid = tmp.documentid
				inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
				inner join dbo.depomemberdata as m on m.depomemberdataid = d.depomemberdataid
				left outer join dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'pdf'
				where tmp.autoid >= #local.startrow#
				ORDER BY tmp.autoid;
			</cfquery>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfinclude  template="/views/myDocuments/#local.viewDirectory#/dsp_PDList.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getPaginationHTML" access="public" output="no" returntype="string" hint="I return pagination html content">
		<cfargument  name="topOrBottom" type="string" required="true">
		<cfargument  name="recordCount" type="numeric" required="true">
		<cfargument  name="itemCount" type="numeric" required="true">
		<cfargument  name="docType" type="string" required="true">
		<cfargument  name="maxRows" type="numeric" required="true">
		<cfargument  name="startRow" type="numeric" required="true">
		<cfargument  name="tab" type="string" required="true">
		<cfargument  name="viewDirectory" type="string" required="true">
		<cfargument  name="noRecordsMsg" type="string" required="true">
		<cfargument  name="dspSaveBtn" type="boolean" required="false" default="false">
		<cfscript>
			var local = structNew();
			/*adjust maxperpage based on actual data if necessary and get page variables*/
			local.startPage  = 1;
			local.MaxPerPage = iif(arguments.recordcount gt arguments.maxRows,arguments.maxRows,arguments.recordcount);
			if (local.MaxPerPage gt 0) {
				local.NumTotalPages = Ceiling(arguments.itemCount / arguments.maxRows);
				local.NumCurrentPage = int((int(arguments.startRow) + arguments.maxRows - 1) / arguments.maxRows);
			} else {
				local.NumTotalPages = 0;
				local.NumCurrentPage = 0;
			}
			local.endPage  = local.NumTotalPages;
			if(local.NumCurrentPage gt 1 and ((local.NumCurrentPage + (arguments.maxRows / 2)) gt arguments.maxRows)){
				local.endPage = local.NumCurrentPage + (arguments.maxRows / 2);
				local.startPage  = local.endPage - ( arguments.maxRows - 1);
				if(local.endPage gte local.NumTotalPages){
					local.endPage = local.NumTotalPages;
					if(local.endPage lte arguments.maxRows){
						local.startPage  = 1
					} else
						local.startPage  = local.endPage - ( arguments.maxRows - 1);
				}
			} else if(local.NumCurrentPage gte 1 and local.NumCurrentPage lte arguments.maxRows and arguments.maxRows lte local.NumTotalPages){
				local.endPage  = arguments.maxRows
			}
		</cfscript>
		<cfset local.pagingLinkPrevious = "javascript:d_doSearchPN('#arguments.tab#',#arguments.startrow - arguments.maxRows#);">
		<cfset local.pagingLinkNext = "javascript:d_doSearchPN('#arguments.tab#',#arguments.startrow + arguments.maxRows#);">
		<cfset local.pagingLinkFirst = "javascript:d_doSearchPN('#arguments.tab#',1);">
		<cfset local.pagingLinkLast = "javascript:d_doSearchPN('#arguments.tab#', #(arguments.maxRows * (local.NumTotalPages-1)) + 1#);">

		<cfif arguments.recordcount EQ 0>
			<cfsavecontent variable="local.paginationHTML">
				<cfoutput>
					<cfif arguments.viewDirectory NEQ "responsive" OR arguments.topOrBottom EQ "top">
						<div>#arguments.noRecordsMsg#</div>
					</cfif>
				</cfoutput>
			</cfsavecontent>

			<cfreturn local.paginationHTML>
		</cfif>

		<cfsavecontent variable="local.paginationHTML">
			<cfoutput>
				<cfset local.currentPageLabel = "">
				<cfif local.numTotalPages gt 1>
					<cfset local.currentPageLabel = "Page #local.NumCurrentPage# of #numberformat(local.NumTotalPages)#">
				</cfif>
				<cfif arguments.viewDirectory EQ "responsive">
					<cfif arguments.topOrBottom EQ "top">
						<div class="row-fluid">
							<div class="span12">
								<div id="paginationBar" class="well well-small " style="margin:3px 0px;" data-totalpages="#local.numTotalPages#" data-currentpage="#local.NumCurrentPage#">
										<cfif local.numTotalPages gt 1 or (arguments.dspSaveBtn and arguments.itemCount)>
											<div id="tsSearchTopPagingControls" class="pull-right">
												<div class="visible-desktop">
													<ul class="pager">
														<cfif local.NumCurrentPage gt 1>
															<li><a class="bucketPagingButtonPreviousPage" href="#local.pagingLinkPrevious#"><strong>Previous</strong></a></li>
														</cfif>
														<cfif len(trim(local.currentPageLabel))>
															<li><span style="border-radius:5px;">#local.currentPageLabel# </span></li>
														</cfif>
														<cfif local.NumCurrentPage lt local.NumTotalPages>
															<li><a class="bucketPagingButtonNextPage" href="#local.pagingLinkNext#"><strong>Next</strong></a></li>
														</cfif>
														<cfif arguments.dspSaveBtn and arguments.itemCount>
															<li><a href="javascript:doDropboxSaveAll();" id="dropdownMenuLink" data-toggle="tooltip" data-html="true" title="Save all PDF files to Dropbox."><strong> Save to Dropbox</strong></a></li>
														</cfif>
													</ul>
												</div>
												<div class="hidden-desktop" style="min-height:26px">
													<ul class="pager">
														<li><span>#local.currentPageLabel#</span></li>
														<li style="margin-top:2px;">
															<cfif local.NumCurrentPage gt 1>
															<a href="#local.pagingLinkPrevious#" title="Previous Page">&lt;&lt;</a>
															</cfif>
															<cfif local.NumCurrentPage lt local.NumTotalPages>
															<a href="#local.pagingLinkNext#" title="Next Page">&gt;&gt;</a>
															</cfif>
														</li>
													</ul>
												</div>
											</div>
										</cfif>
										<div> <p style="margin-bottom:5px;"> <b>#numberformat(arguments.itemCount)#</b> #arguments.docType#<cfif arguments.itemCount is not 1>s</cfif> found </p>  <div class="hidden-desktop" style="min-height:26px"></div></div>
								</div>
							</div>
						</div>
					<cfelse>
						<cfif local.numTotalPages>
							<div class="text-center paginationBar">
								<div class="pagination" style="margin:10px 0px 0px;">
									<ul>
										<cfif local.NumCurrentPage gt 1>
											<li><a href="#local.pagingLinkFirst#" title="First Page" class="bucketPagingButtonFirstPage">&lt;&lt;</a></li>
											<li><a href="#local.pagingLinkPrevious#" title="Previous Page" class="bucketPagingButtonPreviousPage">&lt;</a></li>
										</cfif>

										<cfloop index="local.i" from="#local.startPage#" to="#local.endPage#">
											<cfset local.thisStartRow = local.i>
											<cfif local.i gt 1>
												<cfset local.thisStartRow = (arguments.maxRows * (local.i-1)) + 1>
											</cfif>
											<cfset local.thisPageLink = "javascript:d_doSearchPN('#arguments.tab#',#local.thisStartRow#);">
											<cfif local.NumCurrentPage eq local.i>
												<li><span class="text-error"><strong>#local.i#</strong></span></li>
											<cfelse>
												<li <cfif Abs(local.NumCurrentPage - i) gt 1>class="hidden-phone hidden-tablet"</cfif>><a href="#local.thisPageLink#">#local.i#</a></li>
											</cfif>
										</cfloop>

										<cfif local.NumCurrentPage lt local.NumTotalPages>
											<li><a href="#local.pagingLinkNext#" title="Next Page" class="bucketPagingButtonNextPage">&gt;</a></li>
											<li><a href="#local.pagingLinkLast#" title="Last Page" class="bucketPagingButtonLastPage">&gt;&gt;</a></li>
										</cfif>
									</ul>
								</div>
								<div class="currentLabel">#local.currentPageLabel#</div>
							</div>
						</cfif>
					</cfif>
				<cfelse>
					<div class="s_rhrd s_pgtop tsAppBB" data-totalPages="#local.numTotalPages#" data-currentPage="#local.NumCurrentPage#">
						<cfif local.numTotalPages gt 1 or (arguments.dspSaveBtn and arguments.itemCount)>
							<span style="float:right;">
								<cfif local.NumCurrentPage gt 1>
									<a href="#local.pagingLinkPrevious#">Previous</a> &##149;
								</cfif>
								Page #local.NumCurrentPage# of #numberformat(local.NumTotalPages)#
								<cfif local.NumCurrentPage lt local.NumTotalPages>
									&##149; <a href="#local.pagingLinkNext#">Next</a>
								</cfif>
								<cfif arguments.dspSaveBtn and arguments.itemCount>
									&##149; <a href="javascript:doDropboxSaveAll();" id="dropdownMenuLink" title="Save all PDF files to Dropbox">Save to Dropbox</a>
								</cfif>
							</span>
						</cfif>
						<b>#numberformat(arguments.itemCount)#</b> #arguments.docType#<cfif arguments.itemCount is not 1>s</cfif> found
					</div>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.paginationHTML>
	</cffunction>

	<cffunction name="s3DocumentURL" access="public" output="false" returntype="array">
		<cfargument name="docarr" type="string">

		<cfset var local = structNew()>
		<cfset local.returnArr = arrayNew(1)>
 		<cfset local.returnArr = deserializeJSON(arguments.docarr)>

		<cfset local.s3bucket = "trialsmith-depos">
		<cfset local.s3requesttype = "vhost">
		<cfset local.s3expire = 15 />

		<cfset local.arrAmzHeaders = arrayNew(1)>
		<cfset local.tmpStr = { key="response-content-disposition", value="inline;" }>
		<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>

		<!--- Override protocol if Request is secure --->
		<cfif application.objPlatform.isRequestSecure()>
			<cfset local.protocol = "https">
		<cfelse>
			<cfset local.protocol = "http">
		</cfif>

		<cfset local.currentDocument = CreateObject('component','model.system.platform.tsDocument')>

		<cfloop array="#local.returnArr#" item="local.thisItem">
			<cfset local.documentid = local.thisItem.docid>

			<cfset local.currentDocument.load(documentid=local.documentid)>

			<!--- no access --->
			<cfif NOT local.currentDocument.hasPermissions(session.cfcuser.memberdata.depomemberdataid) AND
				  NOT local.currentDocument.hasContributed(session.cfcuser.memberdata.depomemberdataid) AND
				  NOT local.currentDocument.hasBankAccess(session.cfcuser.memberdata.depomemberdataid) AND
				  NOT local.currentDocument.isExpertDoc()>
				<cfset local.thisItem.url = "">

			<!--- has access --->
			<cfelse>
				<cfset local.s3keyMod = numberFormat(local.documentID mod 1000,"0000")>
				<cfset local.s3objectKey = lcase("depos/pdfs/#local.s3keyMod#/#local.documentid#.pdf")>

				<cfif application.objS3.s3FileExists(bucket=local.s3bucket, objectKey=local.s3objectKey, requestType=local.s3requesttype)>
					<cfset local.thisItem.url = application.objS3.s3Url(bucket=local.s3bucket, objectKey=local.s3objectKey, requestType=local.s3requesttype,
						expireInMinutes=local.s3expire, canonicalizedAmzHeaders=local.arrAmzHeaders, protocol=local.protocol)>
				<cfelseif FileExists("#application.paths.docs.pdfs.path##local.documentid#.pdf")>
					<cfset local.thisItem.url = "#application.paths.docs.pdfs.path##local.documentid#.pdf">
				</cfif>
			</cfif>
		</cfloop>

		<cfreturn local.returnArr>
	</cffunction>

	<cffunction name="getAssociatedDepos" access="private" output="false" returntype="query">
		<cfargument name="depomemberdataid" type="numeric" required="true">
		<cfargument name="caseExpertID" type="numeric" required="true">
		<cfargument name="caseID" type="numeric" required="true">

		<cfset var qryDepos = "">

		<cfquery name="qryDepos" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @depomemberdataid int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataid#">, 
				@caseExpertID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.caseExpertID#">, 
				@caseID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.caseID#">;

			SELECT d.DocumentID, d.pages, d.ExpertName, d.DocumentDate, d.Style, d.State, ct.Description AS causeDesc, d.Notes, d.jurisdiction,
				m.email, m.firstname, m.lastname, m.billingfirm, m.BillingCity, m.BillingState, m.phone,
				(select Name from States where code = d.jurisdiction) as stateName,
				owned = CASE
					WHEN d.depomemberdataid = @depomemberdataid then 1
					WHEN EXISTS (select documentID from dbo.depoPermissions where depomemberdataid = @depomemberdataid and documentid = d.documentid) then 1
					WHEN dbo.fn_Documents_hasContributed(d.documentid,@depomemberdataid) = 1 THEN 1
					WHEN dbo.fn_Documents_checkPermissionsToDoc(d.documentid,@depomemberdataid) = 1 THEN 1
					ELSE 0
					END,
				contributed = CASE WHEN dbo.fn_Documents_hasContributed(d.documentid,@depomemberdataid) = 1 THEN 1 ELSE 0 END
			FROM dbo.depomemberdataCaseExperts AS cs
			INNER JOIN dbo.depomemberdataCases AS c ON c.caseID = cs.caseID
				AND cs.caseExpertID = @caseExpertID
				AND c.caseID = @caseID
			INNER JOIN dbo.depomemberdataCaseExpertDocuments AS ed ON ed.caseExpertID = cs.caseExpertID
				AND ed.caseID = cs.caseID
			INNER JOIN dbo.depodocuments AS d ON d.documentID = ed.documentID
			INNER JOIN dbo.depoCaseTypes AS ct ON d.caseTypeId = ct.caseTypeID
			INNER JOIN dbo.depomemberdata AS m ON m.depomemberdataid = d.depomemberdataid
			WHERE c.depomemberdataid = @depomemberdataid;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryDepos>
	</cffunction>

	<cffunction name="getAvailableDeposFromFilters" access="private" output="false" returntype="query">
		<cfargument name="event" type="any">
		<cfargument name="filterMode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.caseID = int(val(arguments.event.getValue('caseID',0)))>
		<cfset local.expertID = int(val(arguments.event.getValue('expertID',0)))>

		<cfquery name="local.qJoinedAndPublicGroups" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select groupid, description
			from dbo.depogroups
		</cfquery>
		<cfset local.stringJoinedAndPublicGroups = listprepend(valuelist(local.qJoinedAndPublicGroups.groupid),0)>

		<cfset local.qryAIExpert = getAIExpert(depomemberdataid=session.cfcuser.memberdata.depomemberdataid, caseID=local.caseID, expertID=local.expertID)>
		<cfscript>
			//prepare expertname keywords
			local.keywordsexpertname = "";
			if (Len(local.qryAIExpert.first_name))
				local.keywordsexpertname = listAppend(local.keywordsexpertname,'"#local.qryAIExpert.first_name#"',chr(7));
			if (Len(local.qryAIExpert.last_name))
				local.keywordsexpertname = listAppend(local.keywordsexpertname,'"#local.qryAIExpert.last_name#"',chr(7));
			local.keywordsexpertname = Replace(local.keywordsexpertname,chr(7)," and ","ALL");

			local.startRow = int(val(arguments.event.getValue('startrow',1)));
			local.maxPerPage = int(val(arguments.event.getValue('maxrowcount',10)));
			local.filterOpts = arguments.event.getTrimValue('filterOpts','');
		</cfscript>

		<cfquery name="local.qryDepos" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpAssociatedDocs') IS NOT NULL
				DROP TABLE ##tmpAssociatedDocs;
			IF OBJECT_ID('tempdb..##tmpDocumentsSearch') IS NOT NULL
				DROP TABLE ##tmpDocumentsSearch;
			IF OBJECT_ID('tempdb..##tmpDocumentsFTS') IS NOT NULL
				DROP TABLE ##tmpDocumentsFTS;
			
			CREATE TABLE ##tmpAssociatedDocs (documentID int PRIMARY KEY);
			CREATE TABLE ##tmpDocumentsSearch (documentID int PRIMARY KEY, docDepoMemberDataID int, owned bit, rank int, row int);
			CREATE TABLE ##tmpDocumentsFTS (documentID int PRIMARY KEY, rank int);

			DECLARE @depomemberdataid int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.depomemberdataid#">, 
				@caseExpertID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.expertID#">, 
				@caseID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.caseID#">,
				@expertsearchterms varchar(8000), @totalCount int;
			DECLARE @tblGroups TABLE (groupID int PRIMARY KEY);
			DECLARE @tmpResults TABLE (documentid int, rank int, row int);

			SET @expertsearchterms = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.keywordsexpertname#">;

			INSERT INTO ##tmpAssociatedDocs (documentID)
			SELECT DISTINCT ed.documentID
			FROM dbo.depomemberdataCaseExperts AS cs
			INNER JOIN dbo.depomemberdataCases AS c ON c.caseID = cs.caseID
				AND cs.caseExpertID = @caseExpertID
				AND c.caseID = @caseID
			INNER JOIN dbo.depomemberdataCaseExpertDocuments AS ed ON ed.caseExpertID = cs.caseExpertID
				AND ed.caseID = cs.caseID
			WHERE c.depomemberdataid = @depomemberdataid;

			INSERT INTO ##tmpDocumentsFTS (documentID, rank)
			SELECT DISTINCT docs.documentid, (expertsearch.[rank]) as rank
			FROM dbo.depoDocuments AS docs
			INNER JOIN containstable(trialsmith.dbo.depodocuments,expertname,@expertsearchterms) AS expertsearch ON expertsearch.[key] = docs.documentid;

			INSERT INTO @tblGroups (groupID)
			SELECT distinct listitem
			FROM membercentral.dbo.fn_intListToTable(<cfqueryparam value="#local.stringJoinedAndPublicGroups#" cfsqltype="CF_SQL_VARCHAR">,',');

			INSERT INTO ##tmpDocumentsSearch (documentID, docDepoMemberDataID, rank,<cfif listFindNoCase(local.filterOpts,"owndocs")> owned,</cfif> row)
			SELECT d.documentID, d.depomemberDataID, tmpd.rank AS rank, 
				<cfif listFindNoCase(local.filterOpts,"owndocs")>
					owned = CASE WHEN d.depomemberdataid = @depomemberdataid then 1
								WHEN EXISTS (select documentID from dbo.depoPermissions where depomemberdataid = @depomemberdataid and documentid = d.documentid) then 1
								WHEN dbo.fn_Documents_hasContributed(d.documentid,@depomemberdataid) = 1 THEN 1
								WHEN dbo.fn_Documents_checkPermissionsToDoc(d.documentid,@depomemberdataid) = 1 THEN 1
								ELSE 0
							END,
				</cfif>
				ROW_NUMBER() OVER (ORDER BY d.DocumentDate DESC) as row
			FROM dbo.depoDocuments AS d
			INNER JOIN dbo.depogroups AS g ON d.groupid = g.groupid
			INNER JOIN @tblGroups AS tblg ON tblg.groupID = g.groupID
			INNER JOIN ##tmpDocumentsFTS AS tmpd ON tmpd.documentID = d.documentID
			LEFT OUTER JOIN ##tmpAssociatedDocs AS ad ON ad.documentID = d.documentID
			WHERE d.disabled = 'N'
			AND d.documenttypeid = 1
			AND d.uploadStatus <> 1
			AND d.reviewFlag = 0
			AND ad.documentID IS NULL;

			<cfif listFindNoCase(local.filterOpts,"owndocs")>
				DELETE FROM ##tmpDocumentsSearch
				WHERE owned = 0;
			</cfif>
			<cfif listFindNoCase(local.filterOpts,"seldocs")>
				DELETE FROM ##tmpDocumentsSearch
				WHERE documentID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#arguments.event.getValue('selectedDocIDs',0)#">);
			</cfif>
			<cfif listFindNoCase(local.filterOpts,"owndocs") OR listFindNoCase(local.filterOpts,"seldocs")>
				WITH tmpCTE AS (
					SELECT documentID, ROW_NUMBER() OVER (ORDER BY row) AS newRow
					FROM ##tmpDocumentsSearch
				)
				UPDATE d
				SET d.row = tmp.newRow
				FROM ##tmpDocumentsSearch AS d
				INNER JOIN tmpCTE AS tmp ON tmp.documentID = d.documentID;
			</cfif>

			SELECT @totalCount = COUNT(documentID) FROM ##tmpDocumentsSearch;

			IF OBJECT_ID('tempdb..##tmpDocumentsFTS') IS NOT NULL
				DROP TABLE ##tmpDocumentsFTS;

			<cfif arguments.filterMode EQ 'list'>
				INSERT INTO @tmpResults (documentid, rank, row)
				SELECT TOP (<cfqueryparam value="#local.startrow + local.maxPerPage - 1#" cfsqltype="CF_SQL_INTEGER">) documentID, rank, row
				FROM ##tmpDocumentsSearch
				ORDER BY row;

				-- return top x
				SELECT TOP (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.maxPerPage#">) @totalCount AS totalCount, 
					d.documentid, d.expertname, d.documentdate, d.style, d.jurisdiction, m.billingfirm, dt.acctCode
				FROM @tmpResults AS tmp
				INNER JOIN dbo.depodocuments AS d ON d.documentid = tmp.documentid
				INNER JOIN dbo.depomemberdata AS m ON m.depomemberdataid = d.depomemberdataid
				INNER JOIN dbo.depodocumenttypes AS dt ON dt.typeid = d.documenttypeid
				WHERE tmp.row >= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.startrow#">
				ORDER BY tmp.row;
			<cfelse>
				SELECT d.documentID, dt.acctCode, @totalCount AS totalCount
				FROM ##tmpDocumentsSearch AS tmp
				INNER JOIN dbo.depodocuments AS d ON d.documentid = tmp.documentid
				INNER JOIN dbo.depodocumenttypes AS dt ON dt.typeid = d.documenttypeid;
			</cfif>

			IF OBJECT_ID('tempdb..##tmpAssociatedDocs') IS NOT NULL
				DROP TABLE ##tmpAssociatedDocs;
			IF OBJECT_ID('tempdb..##tmpDocumentsSearch') IS NOT NULL
				DROP TABLE ##tmpDocumentsSearch;
			IF OBJECT_ID('tempdb..##tmpDocumentsFTS') IS NOT NULL
				DROP TABLE ##tmpDocumentsFTS;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryDepos>
	</cffunction>

	<cffunction name="getDepoCaseActiveBillingPlan" access="private" output="false" returntype="query">
		<cfargument name="billingPlanID" type="numeric" required="true">
		
		<cfset var qryActiveBillingPlan = "">

		<cfquery name="qryActiveBillingPlan" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT billingPlanID, annualFee, feePerQuestion, feePerUpload
			FROM dbo.depomemberDataCaseBillingPlans
			WHERE billingPlanID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.billingPlanID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		
		<cfreturn qryActiveBillingPlan>
	</cffunction>

	<cffunction name="getDepoDocPriceIncFeePerUpload" access="private" output="false" returntype="struct">
		<cfargument name="qryDepos" type="query" required="true">
		<cfargument name="billingPlanID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.qryDepoCaseActiveBillingPlan = getDepoCaseActiveBillingPlan(billingPlanID=arguments.billingPlanID)>

		<cfset local.cfcuser_membertype = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='memberType')>
		<cfset local.cfcuser_billingstate = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingState')>
		<cfset local.cfcuser_billingzip = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingZip')>

		<cfset local.subTotal = 0>
		<cfset local.totalSalesTax = 0>
		<cfset local.totalPrice = 0>

		<cfloop query="arguments.qryDepos">
			<cfquery name="local.qryThisDocPrice" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @price numeric(9,2), @billingstate varchar(5), @billingzip varchar(10), @acctCode varchar(5);

				SET @billingstate = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.cfcuser_billingstate#">;
				SET @billingzip = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.cfcuser_billingzip#">;
				SET @acctCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.qryDepos.acctCode#">;

				<!--- doc price plus fee per upload --->
				SELECT @price = price + <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(local.qryDepoCaseActiveBillingPlan.feePerUpload)#">
				FROM dbo.fn_Documents_getPrice(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryDepos.documentID#">, 
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.cfcuser_membertype#">, 
					@billingstate, 
					@billingzip,
					<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#session.mcstruct.sitecode#">, 
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.depomemberdataid#">);

				SELECT @price AS price, dbo.fn_tax_getTax('TS',@billingstate,@billingzip,@acctCode,@price) AS salesTax;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.subTotal = precisionEvaluate(local.subTotal + local.qryThisDocPrice.price)>
			<cfset local.totalSalesTax = precisionEvaluate(local.totalSalesTax + local.qryThisDocPrice.salesTax)>
		</cfloop>

		<cfset local.totalPrice = precisionEvaluate(local.subTotal + local.totalSalesTax)>

		<cfreturn { "subTotal": local.subTotal, "totalSalesTax": local.totalSalesTax, "totalPrice": local.totalPrice }>
	</cffunction>

</cfcomponent>
