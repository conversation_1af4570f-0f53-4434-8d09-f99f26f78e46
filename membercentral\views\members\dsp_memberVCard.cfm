<cfset local.strData = attributes.data>
<cfif local.strData.qryMembersCount is 0 or val(local.strData.arrMembers[1].qryMember.memberid) is 0>
	<cfabort>
</cfif>

<cfscript>
local.thisMember = local.strData.arrMembers[1];
local.strMemberData = QueryRowData(local.thisMember.qryViewMemberData,1);
local.arrQryCols = local.thisMember.qryViewMemberData.ColumnArray();
local.appendFSColName = "_mfs#local.thisMember.fieldsetID#";
if (ArrayContains(local.arrQryCols,local.appendFSColName,true)) {
	cfloop(array=local.arrQryCols, index="local.thisCol") {
		if (local.thisCol.FindNoCase(local.appendFSColName)) {
			local.strMemberData[local.thisCol.replace(local.appendFSColName,'')] = local.strMemberData[local.thisCol];
		}
	}
}

local.objMFS = CreateObject("component","model.system.platform.memberFieldsets");
local.xmlResultFields = local.objMFS.getMemberFieldsXML(fieldsetid=local.thisMember.fieldsetID, usage="memberDirectoryVCard");

local.arrNamesNodes = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='m_prefix' or @fieldCode='m_firstname' or @fieldCode='m_middlename' or @fieldCode='m_lastname' or @fieldCode='m_suffix' or @fieldCode='m_professionalsuffix']");
local.lstNameFields = "";
for (local.thisNameNode in local.arrNamesNodes) {
	local.lstNameFields = listAppend(local.lstNameFields,local.thisNameNode.xmlattributes.fieldCode);
}

local.stVcardName = '';
if (listFindNoCase(local.lstNameFields,'m_lastname')) local.stVcardName &= local.thisMember.qryMember.lastname; local.stVcardName &= ";";
if (listFindNoCase(local.lstNameFields,'m_firstname')) local.stVcardName &= local.thisMember.qryMember.firstname; local.stVcardName &= ";";
if (listFindNoCase(local.lstNameFields,'m_middlename') and len(local.thisMember.qryMember.middlename)) local.stVcardName &= "#local.thisMember.qryMember.middlename#;";
if (listFindNoCase(local.lstNameFields,'m_prefix') and len(local.thisMember.qryMember.prefix)) local.stVcardName &= "#local.thisMember.qryMember.prefix#;";
if (listFindNoCase(local.lstNameFields,'m_suffix') and len(local.thisMember.qryMember.suffix)) local.stVcardName &= "#local.thisMember.qryMember.suffix#";

local.stFullName = '';
if (listFindNoCase(local.lstNameFields,'m_prefix') and len(local.thisMember.qryMember.prefix)) local.stFullName &= "#local.thisMember.qryMember.prefix# ";
if (listFindNoCase(local.lstNameFields,'m_firstname')) local.stFullName &= "#local.thisMember.qryMember.firstname# ";
if (listFindNoCase(local.lstNameFields,'m_middlename') and len(local.thisMember.qryMember.middlename)) local.stFullName &= "#local.thisMember.qryMember.middlename# ";
if (listFindNoCase(local.lstNameFields,'m_lastname')) local.stFullName &= "#local.thisMember.qryMember.lastname#";
if (listFindNoCase(local.lstNameFields,'m_suffix') and len(local.thisMember.qryMember.suffix)) local.stFullName &= ", #local.thisMember.qryMember.suffix#";
if (listFindNoCase(local.lstNameFields,'m_professionalsuffix') and len(local.thisMember.qryMember.professionalsuffix)) local.stFullName &= ", #local.thisMember.qryMember.professionalsuffix#";
local.stFullName = replace(replace(local.stFullName,'  ',' ','ALL'),' ,',',','ALL');

local.linebreak = "#chr(13)##chr(10)#";
local.vCardText = "";	
local.vCardText = local.vCardText & "BEGIN:VCARD" & local.linebreak;
local.vCardText = local.vCardText & "VERSION:2.1" & local.linebreak;
local.vCardText = local.vCardText & "N:#local.stVcardName#" & local.linebreak;
local.vCardText = local.vCardText & "FN:#local.stFullName#" & local.linebreak;
if (arrayLen(XMLSearch(local.xmlResultFields,"//mf[@fieldCode='m_company']")) and len(local.thisMember.qryMember.company))
	local.vCardText = local.vCardText & "ORG:#local.thisMember.qryMember.company#" & local.linebreak;

// phones
for (local.thisField in local.xmlResultFields.xmlRoot.xmlChildren) {
	if (ReFindNoCase('mp_[0-9]+_[0-9]+',local.thisField.xmlattributes.fieldcode) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])) {
		local.phoneLabel = "TEL;WORK";
		if (FindNoCase("Fax", local.thisField.xmlattributes.fieldLabel)) local.phoneLabel &= ";FAX";
		else local.phoneLabel &= ";VOICE";
		local.vCardText = local.vCardText & "#local.phoneLabel#:#local.strMemberData[local.thisField.xmlattributes.fieldLabel]#" & local.linebreak;
	}
}

// addresses (show one)
local.strKey = "";
local.atID = 0;
for (local.thisField in local.xmlResultFields.xmlRoot.xmlChildren) {
	if (ReFindNoCase('ma_[0-9]+_[a-z0-9]+',local.thisField.xmlattributes.fieldcode)) {
		local.atID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_");
		local.strKey = local.atID;
		break;
	} else if (ReFindNoCase('mat_[0-9]+_[a-z0-9]+',local.thisField.xmlattributes.fieldcode)) {
		local.atID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_");
		local.strKey ="t" & local.atID;
		break;
	}
}
if (left(local.strKey,1) eq "t") {
	local.MAfcPrefix = "mat_#local.atID#_";
	local.MPfcPrefix = "mpt_#local.atID#_";
} else {
	local.MAfcPrefix = "ma_#local.atID#_";
	local.MPfcPrefix = "mp_#local.atID#_";
}

if (local.atID gt 0) {
	local.addrLabel = "ADR;WORK";

	local.currAddress = "";
	local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#address1']");
	if (arrayLen(local.tmp) is 1 and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])) local.currAddress &= "#local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]#";
	local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#address2']");
	if (arrayLen(local.tmp) is 1 and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])) local.currAddress &= " #local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]#";
	local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#address3']");
	if (arrayLen(local.tmp) is 1 and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])) local.currAddress &= " #local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]#";
	local.currAddress &= ";";

	local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#city']");
	if (arrayLen(local.tmp) is 1 and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])) local.currAddress &= "#local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]#";
	local.currAddress &= ";";

	local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#stateprov']");
	if (arrayLen(local.tmp) is 1 and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])) local.currAddress &= "#local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]#";
	local.currAddress &= ";";

	local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#postalcode']");
	if (arrayLen(local.tmp) is 1 and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])) local.currAddress &= "#local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]#";
	local.currAddress &= ";";

	local.tmp = XMLSearch(local.xmlResultFields,"//mf[@fieldCode='#local.MAfcPrefix#country']");
	if (arrayLen(local.tmp) is 1 and len(local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel])) local.currAddress &= "#local.strMemberData[local.tmp[1].xmlAttributes.fieldLabel]#";

	local.vCardText = local.vCardText & "#local.addrLabel#:;;#local.currAddress#" & local.linebreak;
}
		
// emails
for (local.thisField in local.xmlResultFields.xmlRoot.xmlChildren) {
	if ((ReFindNoCase('me_[0-9]+_email',local.thisField.xmlattributes.fieldcode) or ReFindNoCase('met_[0-9]+_email',local.thisField.xmlattributes.fieldcode)) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])) {
		local.emailLabel = "EMAIL;INTERNET";
		local.vCardText = local.vCardText & "#local.emailLabel#:#local.strMemberData[local.thisField.xmlattributes.fieldLabel]#" & local.linebreak;
	}
}

// websites
for (local.thisField in local.xmlResultFields.xmlRoot.xmlChildren) {
	if (ReFindNoCase('mw_[0-9]+_website',local.thisField.xmlattributes.fieldcode) and len(local.strMemberData[local.thisField.xmlattributes.fieldLabel])) {
		local.websiteLabel = "URL;WORK";
		local.vCardText = local.vCardText & "#local.websiteLabel#:#local.strMemberData[local.thisField.xmlattributes.fieldLabel]#" & local.linebreak;
	}
}

local.vCardText = local.vCardText & "END:VCARD" & local.linebreak;
</cfscript>

<cfsetting showdebugoutput="No">
<CFHEADER NAME="content-disposition" VALUE="attachment; filename=""#local.thisMember.qryMember.firstname# #local.thisMember.qryMember.lastname#.vcf""">
<cfcontent type="application/x-vcard" reset="Yes"><cfoutput>#local.vCardText#</cfoutput>
